import { type NextRequest, NextResponse } from "next/server"
import { recordGameInteraction } from "@/lib/models/game"

export async function POST(request: NextRequest, { params }: { params: { gameId: string | Promise<string> } }) {
  try {
    // In Next.js 14, we need to await the params object itself first
    const resolvedParams = await params
    const gameId = resolvedParams.gameId

    // Get the interaction data from the request body
    const data = await request.json()
    const { type, phoneNumber } = data

    if (!type || !phoneNumber) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    console.log("API: Recording interaction for game ID:", gameId)

    // Record the interaction
    await recordGameInteraction(gameId, type, phoneNumber)

    // Return success
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("API Error recording interaction:", error)
    return NextResponse.json({ error: "Failed to record interaction" }, { status: 500 })
  }
}
