import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET(request: Request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)
    console.log("Session:", session)

    if (!session || !session.user || !session.user.id) {
      console.error("Unauthorized: No valid session or user ID")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id
    console.log("Fetching performance data for user:", userId)

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "all"
    console.log("Period:", period)

    // Connect to MongoDB
    const client = await clientPromise
    const db = client.db()

    // Try querying games with both ObjectId and string userId
    let games = []
    if (ObjectId.isValid(userId)) {
      games = await db
        .collection("games")
        .find({
          userId: new ObjectId(userId),
        })
        .toArray()
      console.log("Games found with ObjectId userId:", games.length)
    }

    if (games.length === 0) {
      games = await db
        .collection("games")
        .find({
          userId: userId,
        })
        .toArray()
      console.log("Games found with string userId:", games.length)
    }

    // If no games found, return sample data
    if (!games || games.length === 0) {
      console.log("No games found for user:", userId)
      const labels = ["No Data"]
      const playsData = [0]
      const conversionsData = [0]
      console.log("Generating fallback sample data due to no games")
      return NextResponse.json({
        labels,
        playsData,
        conversionsData,
        isSampleData: true,
      })
    }

    console.log(`Found ${games.length} games for user ${userId}`)

    // Determine date range based on period
    const now = new Date()
    let startDate: Date

    if (period === "7days") {
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    } else if (period === "30days") {
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    } else {
      const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      const earliestGame = games.reduce((earliest, game) => {
        const gameDate = game.createdAt instanceof Date ? game.createdAt : new Date(game.createdAt || earliest)
        return gameDate < earliest ? gameDate : earliest
      }, now)
      startDate = earliestGame < ninetyDaysAgo ? ninetyDaysAgo : earliestGame
    }

    // Generate date labels and initialize data arrays
    const labels: string[] = []
    const playsData: number[] = []
    const conversionsData: number[] = []

    // Determine the interval based on the period
    let interval: number // in days
    let format: string

    if (period === "7days") {
      interval = 1
      format = "day"
    } else if (period === "30days") {
      interval = 5
      format = "5days"
    } else {
      const daysDiff = Math.ceil((now.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000))
      if (daysDiff <= 30) {
        interval = 1
        format = "day"
      } else if (daysDiff <= 90) {
        interval = 7
        format = "week"
      } else {
        interval = 30
        format = "month"
      }
    }

    // Generate the date points
    const datePoints: Date[] = []
    let currentDate = new Date(startDate)

    while (currentDate <= now) {
      datePoints.push(new Date(currentDate))
      currentDate = new Date(currentDate.getTime() + interval * 24 * 60 * 60 * 1000)
    }

    if (datePoints.length === 0 || datePoints[datePoints.length - 1].getTime() !== now.getTime()) {
      datePoints.push(new Date(now))
    }

    // Format the labels
    for (const date of datePoints) {
      if (format === "day") {
        labels.push(date.toLocaleDateString("en-US", { month: "short", day: "numeric" }))
      } else if (format === "5days" || format === "week") {
        labels.push(date.toLocaleDateString("en-US", { month: "short", day: "numeric" }))
      } else {
        labels.push(date.toLocaleDateString("en-US", { month: "short", year: "numeric" }))
      }
    }

    // Get all plays and conversions from the database (primary source)
    const playsCollection = await db
      .collection("plays")
      .find({
        gameId: { $in: games.map((game) => game._id) },
      })
      .toArray()

    const conversionsCollection = await db
      .collection("conversions")
      .find({
        gameId: { $in: games.map((game) => game._id) },
      })
      .toArray()

    console.log(`Found ${playsCollection.length} plays and ${conversionsCollection.length} conversions`)

    // Log sample entries for debugging
    if (playsCollection.length > 0) {
      console.log("Sample play entry:", playsCollection[0])
    }
    if (conversionsCollection.length > 0) {
      console.log("Sample conversion entry:", conversionsCollection[0])
    }

    // For each date point, calculate cumulative plays and conversions
    for (const date of datePoints) {
      let playsCount = 0
      let conversionsCount = 0

      // Count plays from the plays collection (primary source)
      playsCount = playsCollection.reduce((count, play) => {
        const playDate = play.timestamp
          ? play.timestamp instanceof Date
            ? play.timestamp
            : new Date(play.timestamp)
          : new Date(play.createdAt || 0)
        if (isNaN(playDate.getTime())) {
          console.warn("Invalid play timestamp:", play)
          return count
        }
        return playDate <= date ? count + 1 : count
      }, 0)

      // Count conversions from the conversions collection (primary source)
      conversionsCount = conversionsCollection.reduce((count, conversion) => {
        const conversionDate = conversion.timestamp
          ? conversion.timestamp instanceof Date
            ? conversion.timestamp
            : new Date(conversion.timestamp)
          : new Date(conversion.createdAt || 0)
        if (isNaN(conversionDate.getTime())) {
          console.warn("Invalid conversion timestamp:", conversion)
          return count
        }
        return conversionDate <= date ? count + 1 : count
      }, 0)

      // Only use gameInteractions or counters if collections are empty
      if (playsCount === 0) {
        for (const game of games) {
          if (game.gameInteractions && game.gameInteractions.plays && Array.isArray(game.gameInteractions.plays)) {
            playsCount += game.gameInteractions.plays.reduce((count, play) => {
              const playDate = play.timestamp
                ? play.timestamp instanceof Date
                  ? play.timestamp
                  : new Date(play.timestamp)
                : new Date(game.createdAt || 0)
              if (isNaN(playDate.getTime())) {
                console.warn("Invalid game interaction play timestamp:", play)
                return count
              }
              return playDate <= date ? count + 1 : count
            }, 0)
            console.log(`Added ${playsCount} plays from gameInteractions for game ${game._id}`)
          }
        }
      }

      if (conversionsCount === 0) {
        for (const game of games) {
          if (
            game.gameInteractions &&
            game.gameInteractions.conversions &&
            Array.isArray(game.gameInteractions.conversions)
          ) {
            conversionsCount += game.gameInteractions.conversions.reduce((count, conversion) => {
              const conversionDate = conversion.timestamp
                ? conversion.timestamp instanceof Date
                  ? conversion.timestamp
                  : new Date(conversion.timestamp)
                : new Date(game.createdAt || 0)
              if (isNaN(conversionDate.getTime())) {
                console.warn("Invalid game interaction conversion timestamp:", conversion)
                return count
              }
              return conversionDate <= date ? count + 1 : count
            }, 0)
            console.log(`Added ${conversionsCount} conversions from gameInteractions for game ${game._id}`)
          }
        }
      }

      // Avoid using counters to prevent double-counting
      // If needed, counters can be used as a last resort with validation
      if (playsCount === 0 && conversionsCount === 0) {
        for (const game of games) {
          if (typeof game.plays === "number" && game.plays > 0) {
            const gameDate = game.createdAt instanceof Date ? game.createdAt : new Date(game.createdAt || 0)
            if (gameDate <= date) {
              playsCount += game.plays
              console.log(`Added ${game.plays} plays from game counter for game ${game._id}`)
            }
          }
          if (typeof game.conversions === "number" && game.conversions > 0) {
            const gameDate = game.createdAt instanceof Date ? game.createdAt : new Date(game.createdAt || 0)
            if (gameDate <= date) {
              conversionsCount += game.conversions
              console.log(`Added ${game.conversions} conversions from game counter for game ${game._id}`)
            }
          }
        }
      }

      playsData.push(playsCount)
      conversionsData.push(conversionsCount)
    }

    // Generate sample data if no real data is found
    if (playsData.every((count) => count === 0) && conversionsData.every((count) => count === 0)) {
      console.log("No real data found, generating sample data")
      const gameCount = games.length

      for (let i = 0; i < datePoints.length; i++) {
        playsData[i] = Math.floor(gameCount * 5 * (i + 1) * (Math.random() * 0.5 + 0.75))
        conversionsData[i] = Math.floor(playsData[i] * 0.3 * (Math.random() * 0.5 + 0.75))
      }

      console.log("Returning sample data:", {
        labels,
        playsData,
        conversionsData,
      })
      return NextResponse.json({
        labels,
        playsData,
        conversionsData,
        isSampleData: true,
      })
    }

    // Log the final data
    console.log("Returning performance data:", {
      labels,
      playsData,
      conversionsData,
    })
    return NextResponse.json({
      labels,
      playsData,
      conversionsData,
      isSampleData: false,
    })
  } catch (error) {
    console.error("Error fetching performance data:", error)
    return NextResponse.json({ error: "Failed to fetch performance data" }, { status: 500 })
  }
}
