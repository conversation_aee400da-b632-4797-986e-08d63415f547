'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { useSession } from 'next-auth/react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import WelcomeStep from './steps/WelcomeStep';
import InstagramInfoStep from './steps/InstagramInfoStep';
import GameTypeStep from './steps/GameTypeStep';
import CustomizationStep from './steps/CustomizationStep';
import FinalStep from './steps/FinalStep';
import PlanSelectionStep from './steps/PlanSelectionStep';
import InvoiceStep from './steps/InvoiceStep';

// Update the STEPS array to include the new Invoice step
const STEPS = ['خوش آمدید', 'اطلاعات اینستاگرام', 'نوع بازی', 'سفارشی‌سازی', 'انتخاب پلن', 'فاکتور', 'پایان'];

// Invoice data interface
export interface InvoiceData {
  planId: string;
  planName: string;
  planCredits: number;
  planPrice: number;
  totalPrizes: number;
  gameData: {
    name: string;
    type: string;
    instagramHandle: string;
    pageCategory: string;
    followerCount: string;
    prizes: {
      type: string;
      description: string;
      probability: number;
      quantity: number;
      remainingQuantity: number;
    }[];
    colorScheme: string;
  };
  createdAt: Date;
}

// Update the WizardFormData type to include selectedPlanId and invoice data
export type WizardFormData = {
  // Instagram Info
  instagramHandle: string;
  pageCategory: string;
  followerCount: string;

  // Game Type
  gameType: 'wheel' | 'lever' | 'envelope' | '';

  // Customization
  gameName: string;
  prizes: {
    type: string;
    description: string;
    probability: number;
    quantity: number;
    remainingQuantity: number;
  }[];
  colorScheme: string;

  // Plan Selection
  selectedPlanId: string;

  // Invoice Data
  invoiceData?: InvoiceData;

  // Generated data
  gameLink: string;
  gameId: string;

  // Status
  isSaving: boolean;
  isError: boolean;
  errorMessage: string;
  isSaved: boolean;
};

export default function GameWizard() {
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();

  const [currentStep, setCurrentStep] = useState(0);
  // Update the initial formData state to include selectedPlanId
  const [formData, setFormData] = useState<WizardFormData>({
    instagramHandle: '',
    pageCategory: '',
    followerCount: '',
    gameType: '',
    gameName: '',
    prizes: [
      {
        type: 'Discount',
        description: '10% Off',
        probability: 30,
        quantity: 100,
        remainingQuantity: 100,
      },
      {
        type: 'Free Item',
        description: 'Free Sample',
        probability: 10,
        quantity: 50,
        remainingQuantity: 50,
      },
      {
        type: 'Thank You',
        description: 'Thanks for participating!',
        probability: 60,
        quantity: 1000,
        remainingQuantity: 1000,
      },
    ],
    colorScheme: 'purple',
    selectedPlanId: '',
    gameLink: '',
    gameId: '',
    isSaving: false,
    isError: false,
    errorMessage: '',
    isSaved: false,
  });

  // Handle form data updates
  const updateFormData = (data: Partial<WizardFormData>) => {
    setFormData((prev) => ({ ...prev, ...data }));
  };

  // Save the game to the database
  // Update the saveGame function to include the selectedPlanId
  const saveGame = async (): Promise<boolean> => {
    if (!session?.user) {
      toast({
        title: 'خطای احراز هویت',
        description: 'برای ایجاد بازی باید وارد شده باشید',
        variant: 'destructive',
      });
      return false;
    }

    try {
      // Set saving state
      updateFormData({ isSaving: true, isError: false, errorMessage: '' });

      // Prepare the game data
      const gameData = {
        name: formData.gameName,
        type: formData.gameType,
        instagramHandle: formData.instagramHandle,
        pageCategory: formData.pageCategory,
        followerCount: formData.followerCount,
        prizes: formData.prizes,
        colorScheme: formData.colorScheme,
        selectedPlanId: formData.selectedPlanId,
      };

      // console.log\(.+\);

      // Send the data to the API
      const response = await fetch('/api/games/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(gameData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create game');
      }

      // console.log\(.+\);

      // Update the form data with the game link and ID from the server
      updateFormData({
        gameLink: data.game.gameLink,
        gameId: data.game._id,
        isSaving: false,
        isSaved: true,
      });

      // console.log\(.+\);
      // console.log\(.+\);

      toast({
        title: 'موفقیت!',
        description: 'بازی شما با موفقیت ایجاد شد',
      });

      return true;
    } catch (error) {
      console.error('Error saving game:', error);
      updateFormData({
        isSaving: false,
        isError: true,
        errorMessage: error.message || 'ایجاد بازی ناموفق بود',
      });

      toast({
        title: 'خطا',
        description: error.message || 'ایجاد بازی ناموفق بود',
        variant: 'destructive',
      });

      return false;
    }
  };

  // Navigate to next step
  const nextStep = async () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  // Handle finish - redirect to dashboard
  const handleFinish = () => {
    // Redirect to dashboard
    router.push('/dashboard');
  };

  // Determine if next button should be disabled
  const isNextDisabled = () => {
    switch (currentStep) {
      case 1: // Instagram Info
        return !formData.instagramHandle;
      case 2: // Game Type
        return !formData.gameType;
      case 3: // Customization
        return !formData.gameName;
      case 4: // Plan Selection
        return !formData.selectedPlanId || formData.isSaving;
      default:
        return false;
    }
  };

  // Render the current step
  // Update the renderStep function to include the new InvoiceStep
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <WelcomeStep onNext={nextStep} />;
      case 1:
        return <InstagramInfoStep formData={formData} updateFormData={updateFormData} />;
      case 2:
        return <GameTypeStep formData={formData} updateFormData={updateFormData} />;
      case 3:
        return <CustomizationStep formData={formData} updateFormData={updateFormData} />;
      case 4:
        return (
          <PlanSelectionStep formData={formData} updateFormData={updateFormData} onBack={prevStep} onNext={nextStep} />
        );
      case 5:
        return <InvoiceStep formData={formData} updateFormData={updateFormData} onBack={prevStep} onNext={nextStep} />;
      case 6:
        return (
          <FinalStep
            formData={formData}
            onFinish={handleFinish}
            isSaving={formData.isSaving}
            isError={formData.isError}
            errorMessage={formData.errorMessage}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          {STEPS.map((step, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                  index <= currentStep ? 'bg-primary text-white' : 'bg-gray-200 text-gray-500'
                }`}
                style={{
                  backgroundColor: index <= currentStep ? 'var(--primary)' : '#e5e7eb',
                  color: index <= currentStep ? 'var(--button-text)' : 'var(--text-secondary)',
                }}
              >
                {index + 1}
              </div>
              <span
                className={`text-xs hidden sm:block ${index <= currentStep ? 'font-medium' : 'font-normal'}`}
                style={{
                  color: index <= currentStep ? 'var(--text-primary)' : 'var(--text-secondary)',
                }}
              >
                {step}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-2 h-1 bg-gray-200 rounded-full">
          <motion.div
            className="absolute top-0 left-0 h-full rounded-full"
            style={{ backgroundColor: 'var(--primary)' }}
            initial={{ width: `${(currentStep / (STEPS.length - 1)) * 100}%` }}
            animate={{ width: `${(currentStep / (STEPS.length - 1)) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>

      {/* Step content */}
      <Card className="backdrop-blur-sm border-none shadow-xl overflow-hidden">
        <div className="absolute inset-0 bg-white/80 -z-10" />

        <div className="p-6 min-h-[400px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStep()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation buttons - exclude Invoice step (step 5) as it has its own buttons */}
        {currentStep > 0 && currentStep < STEPS.length - 1 && currentStep !== 5 && (
          <div className="p-6 pt-0 flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              className="flex items-center gap-2"
              disabled={formData.isSaving}
            >
              <ChevronLeft className="h-4 w-4 rotate-180" />
              بازگشت
            </Button>
            <Button
              onClick={nextStep}
              disabled={isNextDisabled()}
              className="flex items-center gap-2"
              style={{
                backgroundColor: 'var(--button-bg)',
                color: 'var(--button-text)',
              }}
            >
              {currentStep === 3 && formData.isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin ml-2" />
                  در حال ذخیره...
                </>
              ) : (
                <>
                  {currentStep === 3 ? 'ذخیره و ادامه' : 'بعدی'}
                  <ChevronRight className="h-4 w-4 rotate-180" />
                </>
              )}
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}
