import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { getUserNotificationPreferences, updateUserNotificationPreferences } from "@/lib/models/notification"

// Get notification preferences for the current user
export async function GET() {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id

    // Get preferences
    const preferences = await getUserNotificationPreferences(userId)

    return NextResponse.json(preferences)
  } catch (error) {
    console.error("Error fetching notification preferences:", error)
    return NextResponse.json(
      { error: "Failed to fetch notification preferences", details: error.message },
      { status: 500 },
    )
  }
}

// Update notification preferences for the current user
export async function PUT(request: Request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id

    // Parse request body
    const body = await request.json()
    const { gameUpdates, accountAlerts, marketingMessages, systemAnnouncements } = body

    // Update preferences
    const success = await updateUserNotificationPreferences(userId, {
      gameUpdates,
      accountAlerts,
      marketingMessages,
      systemAnnouncements,
    })

    if (!success) {
      return NextResponse.json({ error: "Failed to update preferences" }, { status: 500 })
    }

    // Get updated preferences
    const updatedPreferences = await getUserNotificationPreferences(userId)

    return NextResponse.json(updatedPreferences)
  } catch (error) {
    console.error("Error updating notification preferences:", error)
    return NextResponse.json(
      { error: "Failed to update notification preferences", details: error.message },
      { status: 500 },
    )
  }
}
