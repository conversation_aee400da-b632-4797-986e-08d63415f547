"use client"

import type React from "react"

import { formatDistanceToNow } from "date-fns"
import { <PERSON>, Check, Trash2 } from "lucide-react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

interface NotificationItemProps {
  notification: {
    _id: string
    type: string
    title: string
    message: string
    link?: string
    isRead: boolean
    createdAt: string
  }
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
}

export default function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [isMarking, setIsMarking] = useState(false)

  const handleMarkAsRead = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (notification.isRead) return

    setIsMarking(true)
    await onMarkAsRead(notification._id)
    setIsMarking(false)
  }

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    setIsDeleting(true)
    await onDelete(notification._id)
    setIsDeleting(false)
  }

  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case "welcome":
        return <Bell className="h-5 w-5 text-purple-500" />
      case "feature":
        return <Bell className="h-5 w-5 text-blue-500" />
      case "game_activity":
        return <Bell className="h-5 w-5 text-green-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  const content = (
    <div
      className={`p-4 border rounded-lg mb-3 transition-colors ${notification.isRead ? "bg-white" : "bg-purple-50"}`}
    >
      <div className="flex items-start gap-3">
        <div className="mt-1">{getIcon()}</div>
        <div className="flex-1">
          <div className="flex justify-between items-start">
            <h4 className={`text-sm font-medium ${notification.isRead ? "" : "font-semibold"}`}>
              {notification.title}
            </h4>
            <span className="text-xs text-gray-500">
              {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
            </span>
          </div>
          <p className="text-sm text-gray-600 mt-1">{notification.message}</p>

          <div className="flex justify-end mt-2 space-x-2">
            {!notification.isRead && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2 text-xs"
                onClick={handleMarkAsRead}
                disabled={isMarking}
              >
                <Check className="h-3.5 w-3.5 mr-1" />
                Mark as read
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              className="h-8 px-2 text-xs text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              <Trash2 className="h-3.5 w-3.5 mr-1" />
              Delete
            </Button>
          </div>
        </div>
      </div>
    </div>
  )

  if (notification.link) {
    return (
      <Link href={notification.link} className="block">
        {content}
      </Link>
    )
  }

  return content
}
