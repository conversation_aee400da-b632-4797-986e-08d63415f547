"use client";

import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface AudienceGrowthChartProps {
  timeRange: "7days" | "30days" | "90days";
  setTimeRange: (timeRange: "7days" | "30days" | "90days") => void;
  chartData: {
    labels: string[];
    totalPlayers: number[];
    newPlayers: number[];
    returningPlayers: number[];
  } | null;
}

export default function AudienceGrowthChart({
  timeRange,
  setTimeRange,
  chartData,
}: AudienceGrowthChartProps) {
  // Log chartData for debugging
  // console.log\(.+\);

  // Fallback data
  const fallbackData = {
    labels: [],
    totalPlayers: [],
    newPlayers: [],
    returningPlayers: [],
  };

  const data = chartData || fallbackData;

  const chartConfig = {
    labels: data.labels,
    datasets: [
      {
        label: "Total Players",
        data: data.totalPlayers,
        borderColor: "#8b5cf6",
        backgroundColor: "rgba(139, 92, 246, 0.1)",
        fill: true,
        tension: 0.4,
        yAxisID: "y",
      },
      {
        label: "New Players",
        data: data.newPlayers,
        borderColor: "#84cc16",
        backgroundColor: "rgba(132, 204, 22, 0.1)",
        fill: true,
        tension: 0.4,
        yAxisID: "y1",
      },
      {
        label: "Returning Players",
        data: data.returningPlayers,
        borderColor: "#f97316",
        backgroundColor: "rgba(249, 115, 22, 0.1)",
        fill: true,
        tension: 0.4,
        yAxisID: "y1",
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      tooltip: {
        mode: "index" as const,
        intersect: false,
      },
    },
    scales: {
      y: {
        type: "linear" as const,
        display: true,
        position: "left" as const,
        title: {
          display: true,
          text: "Total Players",
        },
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
      },
      y1: {
        type: "linear" as const,
        display: true,
        position: "right" as const,
        title: {
          display: true,
          text: "New/Returning",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
    interaction: {
      mode: "nearest" as const,
      axis: "x" as const,
      intersect: false,
    },
  };

  return (
    <div className="w-full h-[400px] pb-[70px] pr-4 pl-4 bg-white rounded-lg shadow">
      <div className="flex justify-end mb-4">
        <button
          className={`px-4 py-2 mx-1 rounded-lg ${
            timeRange === "7days"
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700"
          }`}
          onClick={() => setTimeRange("7days")}
        >
          7 Days
        </button>
        <button
          className={`px-4 py-2 mx-1 rounded-lg ${
            timeRange === "30days"
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700"
          }`}
          onClick={() => setTimeRange("30days")}
        >
          30 Days
        </button>
        <button
          className={`px-4 py-2 mx-1 rounded-lg ${
            timeRange === "90days"
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700"
          }`}
          onClick={() => setTimeRange("90days")}
        >
          90 Days
        </button>
      </div>
      {data.labels.length > 0 ? (
        <Line data={chartConfig} options={options} />
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">
          No data available for the selected time range
        </div>
      )}
    </div>
  );
}
