/**
 * Generates a game link based on the game type, Instagram handle, and game ID.
 * This creates a permanent link that includes the actual database ID.
 */
export function generateGameLink(gameType: string, instagramHandle: string, gameId: string): string {
  const baseUrl = getBaseUrl()
  const handle = instagramHandle.replace(/^@/, "") // Remove leading "@" if present

  console.log(`URL Helper: Generating game link with type=${gameType}, handle=${handle}, id=${gameId}`)
  const gameLink = `${baseUrl}/play/${gameType}-${handle}-${gameId}`
  console.log(`URL Helper: Generated link: ${gameLink}`)

  return gameLink
}

/**
 * Gets the base URL of the application
 * Works in both development and production
 */
export function getBaseUrl() {
  // Check if we have access to window (client-side)
  if (typeof window !== "undefined") {
    const origin = window.location.origin
    console.log(`URL Helper: Using browser origin: ${origin}`)
    return origin
  }

  // Server-side: use environment variable or default to localhost
  const vercelUrl = process.env.VERCEL_URL
  if (vercelUrl) {
    const url = `https://${vercelUrl}`
    console.log(`URL Helper: Using Vercel URL: ${url}`)
    return url
  }

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL
  if (siteUrl) {
    console.log(`URL Helper: Using NEXT_PUBLIC_SITE_URL: ${siteUrl}`)
    return siteUrl
  }

  // Default for local development
  console.log(`URL Helper: Using default localhost URL`)
  return "http://localhost:3000"
}
