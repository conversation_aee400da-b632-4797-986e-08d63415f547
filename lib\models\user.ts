import type { ObjectId } from 'mongodb';
import bcrypt from 'bcryptjs';
import clientPromise from '../mongodb';
import { ObjectId as ObjectIdClass } from 'mongodb';

export interface Transaction {
  id: string;
  date: Date;
  amount: number;
  type: 'purchase' | 'usage' | 'package_purchase' | 'payment_attempt' | 'payment_success' | 'payment_failed';
  description?: string;
  packageName?: string;
  // ZarinPal payment information
  paymentInfo?: {
    authority?: string;
    refId?: number;
    cardPan?: string;
    cardHash?: string;
    fee?: number;
    paymentAmount?: number; // Amount in Tomans
    status?: 'pending' | 'completed' | 'failed' | 'cancelled';
    gateway?: 'zarinpal';
  };
}

export interface User {
  _id?: ObjectId;
  name: string;
  mobileNumber?: string;
  email?: string;
  password?: string;
  image?: string;
  provider?: 'credentials' | 'google';
  credits: number;
  plan: string;
  transactions?: Transaction[];
  createdAt: Date;
  updatedAt: Date;
}

export async function getUserByMobileNumber(mobileNumber: string) {
  const client = await clientPromise;
  const collection = client.db().collection('users');
  return collection.findOne({ mobileNumber }) as Promise<User | null>;
}

export async function getUserByEmail(email: string) {
  const client = await clientPromise;
  const collection = client.db().collection('users');
  return collection.findOne({ email }) as Promise<User | null>;
}

export async function createUser(userData: Omit<User, '_id' | 'createdAt' | 'updatedAt' | 'credits' | 'plan'>) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  // Check if user already exists
  if (userData.mobileNumber) {
    const existingUser = await getUserByMobileNumber(userData.mobileNumber);
    if (existingUser) {
      throw new Error('User with this mobile number already exists');
    }
  }

  // Hash password if provided
  let hashedPassword = undefined;
  if (userData.password) {
    const salt = await bcrypt.genSalt(10);
    hashedPassword = await bcrypt.hash(userData.password, salt);
  }

  // Create user with hashed password and default credits
  const newUser = {
    name: userData.name,
    mobileNumber: userData.mobileNumber,
    email: userData.email,
    password: hashedPassword,
    image: userData.image,
    provider: userData.provider || 'credentials',
    credits: 5, // Default free credits
    plan: 'free', // Default plan
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const result = await collection.insertOne(newUser);
  return { ...newUser, _id: result.insertedId };
}

export async function createGoogleUser(userData: {
  name: string;
  email: string;
  image?: string | null;
}): Promise<User> {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  // Check if user already exists by email
  const existingUser = await getUserByEmail(userData.email);
  if (existingUser) {
    return existingUser;
  }

  // Create Google user with default credits
  const newUser = {
    name: userData.name,
    email: userData.email,
    image: userData.image || undefined,
    provider: 'google' as const,
    credits: 5, // Default free credits
    plan: 'free', // Default plan
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const result = await collection.insertOne(newUser);
  return { ...newUser, _id: result.insertedId };
}

export async function verifyPassword(user: User, password: string) {
  if (!user.password) {
    return false;
  }
  return bcrypt.compare(password, user.password);
}

export async function getUserCredits(userId: string | ObjectId) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  let id = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    id = new ObjectIdClass(userId);
  }

  const user = await collection.findOne({ _id: id });
  return user ? user.credits : 0;
}

export async function updateUserCredits(userId: string | ObjectId, planId: string, totalPrizes: number) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  let id = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    id = new ObjectIdClass(userId);
  }

  // Define credits for each plan
  const planCredits = {
    free: 5,
    starter: 50,
    business: 200,
    enterprise: 500,
  };

  // Define plan names in Persian
  const planNames = {
    free: 'رایگان',
    starter: 'شروع',
    business: 'کسب‌وکار',
    enterprise: 'سازمانی',
  };

  // Get current user
  const user = await collection.findOne({ _id: id });
  if (!user) {
    throw new Error('User not found');
  }

  // Calculate new credits based on plan
  let newCredits = planCredits[planId] || 0;
  let creditsAdded = 0;

  // If user is upgrading from a plan, don't reset credits but add the difference
  if (user.plan && user.plan !== planId) {
    // Only add credits if upgrading to a higher plan
    if (planCredits[planId] > planCredits[user.plan]) {
      creditsAdded = planCredits[planId] - planCredits[user.plan];
      newCredits = user.credits + creditsAdded;
    } else {
      // If downgrading or same plan, keep existing credits
      newCredits = user.credits;
    }
  } else if (!user.plan || user.plan === 'free') {
    // First time selecting a paid plan
    if (planId !== 'free') {
      creditsAdded = planCredits[planId] - (user.credits || 0);
      newCredits = planCredits[planId];
    }
  }

  // Update user with new plan and credits
  await collection.updateOne(
    { _id: id },
    {
      $set: {
        plan: planId,
        credits: newCredits,
        updatedAt: new Date(),
      },
    }
  );

  // Record transaction for package purchase (only if credits were added)
  if (creditsAdded > 0 && planId !== 'free') {
    await recordTransaction(userId, {
      amount: creditsAdded,
      type: 'package_purchase',
      description: `خرید بسته ${planNames[planId]}`,
      packageName: planNames[planId],
    });
  }

  return newCredits;
}

export async function deductCredits(userId: string | ObjectId, amount: number, description?: string) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  let id = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    id = new ObjectIdClass(userId);
  }

  // Get current user
  const user = await collection.findOne({ _id: id });
  if (!user) {
    throw new Error('User not found');
  }

  // Check if user has enough credits
  if (user.credits < amount) {
    throw new Error('Not enough credits');
  }

  // Deduct credits
  await collection.updateOne(
    { _id: id },
    {
      $inc: { credits: -amount },
      $set: { updatedAt: new Date() },
    }
  );

  // Record transaction
  await recordTransaction(userId, {
    amount: -amount, // Negative amount for deduction
    type: 'usage',
    description: description || 'استفاده از اعتبار',
  });

  return user.credits - amount;
}

// Function to record a transaction
export async function recordTransaction(userId: string | ObjectId, transaction: Omit<Transaction, 'id' | 'date'>) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  let id = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    id = new ObjectIdClass(userId);
  }

  const newTransaction: Transaction = {
    id: new ObjectIdClass().toString(),
    date: new Date(),
    ...transaction,
  };

  await collection.updateOne(
    { _id: id },
    {
      $push: { transactions: newTransaction },
      $set: { updatedAt: new Date() },
    }
  );

  return newTransaction;
}

// Function to get user transactions
export async function getUserTransactions(userId: string | ObjectId) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  let id = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    id = new ObjectIdClass(userId);
  }

  const user = await collection.findOne({ _id: id });
  return user?.transactions || [];
}

// New function to add credits to a user account
export async function addCredits(
  userId: string | ObjectId,
  amount: number,
  description?: string,
  packageName?: string
) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  let id = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    id = new ObjectIdClass(userId);
  }

  // Get current user
  const user = await collection.findOne({ _id: id });
  if (!user) {
    throw new Error('User not found');
  }

  // Add credits
  await collection.updateOne(
    { _id: id },
    {
      $inc: { credits: amount },
      $set: { updatedAt: new Date() },
    }
  );

  // Record transaction
  const transactionType = packageName ? 'package_purchase' : 'purchase';
  await recordTransaction(userId, {
    amount,
    type: transactionType,
    description: description || (packageName ? `خرید بسته ${packageName}` : 'خرید اعتبار'),
    packageName,
  });

  // Return new credit balance
  return user.credits + amount;
}

// Function to add credits with payment information
export async function addCreditsWithPayment(
  userId: string | ObjectId,
  amount: number,
  description: string,
  paymentInfo: Transaction['paymentInfo']
) {
  const client = await clientPromise;
  const collection = client.db().collection('users');

  let id = userId;
  if (typeof userId === 'string' && ObjectIdClass.isValid(userId)) {
    id = new ObjectIdClass(userId);
  }

  // Get current user
  const user = await collection.findOne({ _id: id });
  if (!user) {
    throw new Error('User not found');
  }

  // Add credits
  await collection.updateOne(
    { _id: id },
    {
      $inc: { credits: amount },
      $set: { updatedAt: new Date() },
    }
  );

  // Record the payment transaction
  await recordTransaction(userId, {
    amount,
    type: 'payment_success',
    description,
    paymentInfo,
  });

  return user.credits + amount;
}

// Function to record payment attempt
export async function recordPaymentAttempt(
  userId: string | ObjectId,
  amount: number,
  authority: string,
  paymentAmount: number,
  description: string
) {
  return recordTransaction(userId, {
    amount,
    type: 'payment_attempt',
    description,
    paymentInfo: {
      authority,
      paymentAmount,
      status: 'pending',
      gateway: 'zarinpal',
    },
  });
}
