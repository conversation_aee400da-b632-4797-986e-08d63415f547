/**
 * Utility functions for session time formatting and calculations
 */

/**
 * Format duration in seconds to Persian time format
 * @param seconds - Duration in seconds
 * @returns Formatted Persian time string
 */
export function formatSessionTime(seconds: number): string {
  if (seconds < 60) {
    return `${seconds} ثانیه`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (remainingSeconds > 0) {
      return `${minutes} دقیقه ${remainingSeconds} ثانیه`;
    }
    return `${minutes} دقیقه`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (minutes > 0) {
      return `${hours} ساعت ${minutes} دقیقه`;
    }
    return `${hours} ساعت`;
  }
}

/**
 * Format duration in seconds to short Persian format
 * @param seconds - Duration in seconds
 * @returns Short formatted Persian time string
 */
export function formatSessionTimeShort(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}ث`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (remainingSeconds > 0) {
      return `${minutes}د ${remainingSeconds}ث`;
    }
    return `${minutes}د`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (minutes > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${hours}س`;
  }
}

/**
 * Calculate average session time from an array of sessions
 * @param sessions - Array of session objects with duration property
 * @returns Average duration in seconds
 */
export function calculateAverageSessionTime(sessions: Array<{ duration?: number; completed?: boolean }>): number {
  const completedSessions = sessions.filter(s => s.completed && s.duration);
  if (completedSessions.length === 0) return 0;
  
  const totalDuration = completedSessions.reduce((sum, session) => sum + (session.duration || 0), 0);
  return Math.round(totalDuration / completedSessions.length);
}

/**
 * Calculate session completion rate
 * @param sessions - Array of session objects
 * @returns Completion rate as percentage (0-100)
 */
export function calculateCompletionRate(sessions: Array<{ completed?: boolean }>): number {
  if (sessions.length === 0) return 0;
  
  const completedCount = sessions.filter(s => s.completed).length;
  return Math.round((completedCount / sessions.length) * 100);
}

/**
 * Get session time category for display
 * @param seconds - Duration in seconds
 * @returns Category string in Persian
 */
export function getSessionTimeCategory(seconds: number): string {
  if (seconds < 30) return 'خیلی کوتاه';
  if (seconds < 60) return 'کوتاه';
  if (seconds < 180) return 'متوسط';
  if (seconds < 300) return 'طولانی';
  return 'خیلی طولانی';
}

/**
 * Convert English numbers to Persian numbers
 * @param str - String containing English numbers
 * @returns String with Persian numbers
 */
export function toPersianNumbers(str: string): string {
  const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  return str.replace(/[0-9]/g, (match) => persianNumbers[parseInt(match)]);
}

/**
 * Format session time with Persian numbers
 * @param seconds - Duration in seconds
 * @returns Formatted time with Persian numbers
 */
export function formatSessionTimePersian(seconds: number): string {
  return toPersianNumbers(formatSessionTime(seconds));
}

/**
 * Format session time short with Persian numbers
 * @param seconds - Duration in seconds
 * @returns Short formatted time with Persian numbers
 */
export function formatSessionTimeShortPersian(seconds: number): string {
  return toPersianNumbers(formatSessionTimeShort(seconds));
}
