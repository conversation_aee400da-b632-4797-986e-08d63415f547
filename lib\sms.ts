// Import Melipayamak exactly as shown in their documentation
const MelipayamakApi = require("melipayamak");
import axios from "axios";

// Generate a random 6-digit OTP code
export function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Send OTP via SMS using Melipayamak
export async function sendOTP(
  phoneNumber: string,
  otpCode: string
): Promise<{ success: boolean; message: string }> {
  try {
    const username = process.env.MELIPAYAMAK_USERNAME;
    const password = process.env.MELIPAYAMAK_PASSWORD;
    const from = process.env.MELIPAYAMAK_FROM;

    console.log("Melipayamak credentials check:", {
      usernameExists: !!username,
      passwordExists: !!password,
      fromExists: !!from,
    });

    if (!username || !password || !from) {
      console.error("Missing Melipayamak credentials");
      return { success: false, message: "Missing SMS credentials" };
    }

    // Format the phone number correctly for Melipayamak
    // Remove the '+' if present and ensure it's just digits
    const formattedNumber = phoneNumber.replace(/^\+/, "").replace(/\D/g, "");
    console.log("Sending OTP to formatted number:", formattedNumber);

    // Initialize Melipayamak client exactly as in their documentation
    const api = new MelipayamakApi(username, password);
    const sms = api.sms();

    // Prepare message text
    const message = `کد تایید شما برای اینستاگرام گیمیفیکیشن: ${otpCode}`;

    console.log("Attempting to send OTP via Melipayamak");

    // Use their exact promise-based approach
    try {
      const result = await sms.send(formattedNumber, from, message);
      console.log("Melipayamak send result:", result);

      // Check if the response indicates success
      // The API returns an object with RetStatus: 1 and StrRetStatus: 'Ok' on success
      if (result) {
        // Handle object response format
        if (typeof result === "object" && result.RetStatus === 1) {
          return { success: true, message: "SMS sent successfully" };
        }
        // Handle numeric or string response format (for backward compatibility)
        else if (
          (typeof result === "string" && Number.parseInt(result) > 0) ||
          (typeof result === "number" && result > 0)
        ) {
          return { success: true, message: "SMS sent successfully" };
        } else {
          return {
            success: false,
            message: `SMS sending failed with response: ${JSON.stringify(
              result
            )}`,
          };
        }
      } else {
        return {
          success: false,
          message: "SMS sending failed with empty response",
        };
      }
    } catch (err) {
      console.error("Melipayamak send error:", err);
      return {
        success: false,
        message: `Melipayamak error: ${
          err instanceof Error ? err.message : String(err)
        }`,
      };
    }
  } catch (error) {
    console.error("Error sending SMS:", error);
    return {
      success: false,
      message: `Error sending SMS: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}

// Send congratulatory message via SMS using hypersmsc.ir
export async function sendCongratulationSMS(
  phoneNumber: string,
  message: string,
  prizeName?: string,
  gameName?: string,
  instagramPage?: string
): Promise<{ success: boolean; message: string }> {
  try {
    if (!phoneNumber) {
      return {
        success: false,
        message: "Mobile number not entered",
      };
    }

    // If prize details are provided, create a professional Persian message
    if (prizeName && gameName && instagramPage) {
      // Create a professional Persian message with the prize, game name, and Instagram page
      message = `تبریک! 🎉 شما جایزه "${prizeName}" را در بازی "${gameName}" برنده شدید. برای دریافت جایزه خود، لطفا با صفحه اینستاگرام "${instagramPage}" تماس بگیرید. با تشکر از مشارکت شما.`;
    } else if (!message) {
      // Fallback message if no custom message or details provided
      message = "تبریک! شما برنده شده‌اید. با تشکر از مشارکت شما.";
    }

    const username = process.env.SMS_USERNAME;
    const password = process.env.SMS_PASSWORD;

    console.log("HyperSMS credentials check:", {
      usernameExists: !!username,
      passwordExists: !!password,
    });

    if (!username || !password) {
      console.error("Missing HyperSMS credentials");
      return { success: false, message: "Missing SMS credentials" };
    }

    // Format the phone number correctly
    const formattedNumber = phoneNumber.replace(/^\+/, "").replace(/\D/g, "");

    const url = `https://hypersmsc.ir/api/json/sendgroupget?username=${username}&password=${password}&api=39&from=200032217400&to=${formattedNumber}&text=${encodeURIComponent(
      message
    )}`;

    try {
      const response = await axios({
        method: "GET",
        url: url,
        headers: {
          "Content-Type": "application/text; charset=utf-8",
        },
      });

      if (response.statusText !== "OK") {
        console.error("HyperSMS error response:", response.statusText);
        return {
          success: false,
          message: "Error sending congratulation message!",
        };
      }

      console.log("HyperSMS success response:", response.data);
      return { success: true, message: "Congratulation SMS sent successfully" };
    } catch (err) {
      console.error("HyperSMS send error:", err);
      return {
        success: false,
        message: `HyperSMS error: ${
          err instanceof Error ? err.message : String(err)
        }`,
      };
    }
  } catch (error) {
    console.error("Error sending congratulation SMS:", error);
    return {
      success: false,
      message: `Error sending congratulation SMS: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}
