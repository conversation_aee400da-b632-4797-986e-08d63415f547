"use client"

import { motion } from "framer-motion"
import { Compass, Plus } from "lucide-react"

import { Button } from "@/components/ui/button"

interface NoGamesPlaceholderProps {
  onCreateNew: () => void
}

export default function NoGamesPlaceholder({ onCreateNew }: NoGamesPlaceholderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center py-16 px-4 text-center"
    >
      <div
        className="w-20 h-20 rounded-full flex items-center justify-center mb-6"
        style={{ backgroundColor: "rgba(139, 92, 246, 0.1)" }}
      >
        <Compass className="h-10 w-10" style={{ color: "var(--primary)" }} />
      </div>
      <h3 className="text-xl font-bold mb-2" style={{ color: "var(--text-primary)" }}>
        No Games Found
      </h3>
      <p className="text-base mb-6 max-w-md" style={{ color: "var(--text-secondary)" }}>
        You haven't created any games yet, or none match your current filters. Create your first game to engage your
        Instagram followers!
      </p>
      <Button
        onClick={onCreateNew}
        className="flex items-center gap-2"
        style={{ backgroundColor: "var(--secondary)", color: "var(--button-text)" }}
      >
        <Plus className="h-4 w-4" />
        Create Your First Game
      </Button>
    </motion.div>
  )
}
