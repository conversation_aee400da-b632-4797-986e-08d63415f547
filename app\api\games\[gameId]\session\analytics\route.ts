import { type NextRequest, NextResponse } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: { gameId: string | Promise<string> } }
) {
  try {
    console.log('API: Getting session analytics');

    // Get the gameId from params
    const resolvedParams = await params;
    const gameId = resolvedParams.gameId;

    // Parse query parameters
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || '30days';

    console.log('API: Getting session analytics for gameId:', gameId, 'period:', period);

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db('instagram_gamification');

    // Find the game
    let game = null;
    let actualGameId = gameId;

    // Try to find by ObjectId first
    if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
      if (game) {
        actualGameId = gameId;
      }
    }

    // If not found, try to find by gameLink
    if (!game) {
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        return NextResponse.json({ error: 'Game not found' }, { status: 404 });
      }
    }

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();
    
    switch (period) {
      case '7days':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(now.getDate() - 90);
        break;
      case 'all':
        startDate = new Date(game.createdAt);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get sessions within the date range
    const sessions = game.gameInteractions?.sessions || [];
    const filteredSessions = sessions.filter((session: any) => 
      new Date(session.startTime) >= startDate
    );

    // Calculate analytics
    const totalSessions = filteredSessions.length;
    const completedSessions = filteredSessions.filter((s: any) => s.completed);
    const averageSessionTime = completedSessions.length > 0 
      ? Math.round(completedSessions.reduce((sum: number, s: any) => sum + (s.duration || 0), 0) / completedSessions.length)
      : 0;
    const completionRate = totalSessions > 0 
      ? Math.round((completedSessions.length / totalSessions) * 100)
      : 0;

    // Calculate session trends (daily data)
    const sessionTrends = generateSessionTrends(filteredSessions, startDate, now);

    // Calculate exit reasons distribution
    const exitReasons = {
      completed: filteredSessions.filter((s: any) => s.exitReason === 'completed').length,
      abandoned: filteredSessions.filter((s: any) => s.exitReason === 'abandoned').length,
      error: filteredSessions.filter((s: any) => s.exitReason === 'error').length,
    };

    // Calculate hourly distribution
    const hourlyDistribution = calculateHourlyDistribution(filteredSessions);

    const analytics = {
      totalSessions,
      averageSessionTime,
      completionRate,
      sessionTrends,
      exitReasons,
      hourlyDistribution,
      period,
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString(),
      },
    };

    console.log('API: Session analytics calculated successfully');

    return NextResponse.json({
      success: true,
      analytics,
    });

  } catch (error) {
    console.error('API Error getting session analytics:', error);
    return NextResponse.json(
      {
        error: 'Failed to get session analytics',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

function generateSessionTrends(sessions: any[], startDate: Date, endDate: Date) {
  const trends = {
    labels: [] as string[],
    sessions: [] as number[],
    averageDuration: [] as number[],
  };

  // Create daily buckets
  const current = new Date(startDate);
  const sessionsByDate: Record<string, any[]> = {};

  while (current <= endDate) {
    const dateStr = current.toISOString().split('T')[0];
    sessionsByDate[dateStr] = [];
    trends.labels.push(dateStr);
    current.setDate(current.getDate() + 1);
  }

  // Group sessions by date
  sessions.forEach((session: any) => {
    const dateStr = new Date(session.startTime).toISOString().split('T')[0];
    if (sessionsByDate[dateStr]) {
      sessionsByDate[dateStr].push(session);
    }
  });

  // Calculate daily metrics
  trends.labels.forEach((dateStr) => {
    const daySessions = sessionsByDate[dateStr] || [];
    const completedSessions = daySessions.filter((s: any) => s.completed && s.duration);
    
    trends.sessions.push(daySessions.length);
    trends.averageDuration.push(
      completedSessions.length > 0
        ? Math.round(completedSessions.reduce((sum: number, s: any) => sum + (s.duration || 0), 0) / completedSessions.length)
        : 0
    );
  });

  return trends;
}

function calculateHourlyDistribution(sessions: any[]) {
  const hourCounts: Record<number, number> = {};
  
  // Initialize all hours
  for (let i = 0; i < 24; i++) {
    hourCounts[i] = 0;
  }

  // Count sessions by hour
  sessions.forEach((session: any) => {
    const hour = new Date(session.startTime).getHours();
    hourCounts[hour]++;
  });

  return Object.entries(hourCounts).map(([hour, count]) => ({
    hour: parseInt(hour),
    count,
  }));
}
