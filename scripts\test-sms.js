// This is a simple script to test the Melipayamak SMS functionality
// Run it with: node scripts/test-sms.js

const MelipayamakApi = require("melipayamak")

async function testSMS() {
  try {
    // Replace these with your actual credentials
    const username = process.env.MELIPAYAMAK_USERNAME || "your-username"
    const password = process.env.MELIPAYAMAK_PASSWORD || "your-password"
    const from = process.env.MELIPAYAMAK_FROM || "your-from-number"

    // Replace with the phone number you want to test
    const to = "09123456789" // Replace with your phone number

    // console.log$$[^)]*$$;
    const api = new <PERSON>ipayamakApi(username, password)
    const sms = api.sms()

    // console.log$$[^)]*$$;
    const text = "This is a test message from Melipayamak"

    const result = await sms.send(to, from, text)
    // console.log$$[^)]*$$;

    // Check credit
    // console.log$$[^)]*$$;
    const credit = await sms.getCredit()
    // console.log$$[^)]*$$;
  } catch (error) {
    console.error("Error testing SMS:", error)
  }
}

testSMS()
