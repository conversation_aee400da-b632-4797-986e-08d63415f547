"use client"

import { Doughnut } from "react-chartjs-2"
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js"

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend)

export default function AudienceSourcesChart() {
  // Mock data for the chart
  const data = {
    labels: ["Instagram Stories", "Instagram Bio Link", "Direct Links", "WhatsApp Shares", "Other"],
    datasets: [
      {
        data: [68, 18, 8, 4, 2],
        backgroundColor: ["#8b5cf6", "#84cc16", "#f97316", "#ec4899", "#0ea5e9"],
        borderColor: ["#ffffff", "#ffffff", "#ffffff", "#ffffff", "#ffffff"],
        borderWidth: 2,
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "right" as const,
        labels: {
          boxWidth: 15,
          padding: 15,
        },
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const label = context.label || ""
            const value = context.raw || 0
            return `${label}: ${value}%`
          },
        },
      },
    },
    cutout: "60%",
  }

  return (
    <div className="w-full h-full">
      <Doughnut data={data} options={options} />
    </div>
  )
}
