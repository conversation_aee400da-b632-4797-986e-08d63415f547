'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, ArrowRight, Loader2, AlertCircle, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import type { WizardFormData } from '../GameWizard';

interface FinalStepProps {
  formData: WizardFormData;
  onFinish: () => void;
  isSaving: boolean;
  isError: boolean;
  errorMessage: string;
}

export default function FinalStep({ formData, onFinish, isSaving, isError, errorMessage }: FinalStepProps) {
  const [copied, setCopied] = useState(false);
  const [gameLink, setGameLink] = useState('');

  // Set the game link when the component mounts or when formData changes
  useEffect(() => {
    // console.log\(.+\);
    // console.log\(.+\);

    if (formData.gameLink) {
      setGameLink(formData.gameLink);
    }
  }, [formData.gameLink, formData.gameId]);

  const copyLink = () => {
    navigator.clipboard.writeText(gameLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const openGameLink = () => {
    if (gameLink) {
      window.open(gameLink, '_blank');
    }
  };

  return (
    <div className="py-4">
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ type: 'spring', stiffness: 200, damping: 10 }}
        className="flex justify-center mb-6"
      >
        {isError ? (
          <AlertCircle className="h-20 w-20 text-red-500" />
        ) : (
          <CheckCircle className="h-20 w-20" style={{ color: 'var(--secondary)' }} />
        )}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-center mb-8"
      >
        <h2 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
          {isError ? 'مشکلی پیش آمده' : 'همه چیز آماده است!'}
        </h2>
        <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
          {isError ? 'در ایجاد بازی شما خطایی رخ داده' : 'بازی شما با موفقیت ایجاد شد'}
        </p>
      </motion.div>

      {isError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>خطا</AlertTitle>
          <AlertDescription>{errorMessage || 'ایجاد بازی ناموفق بود. لطفاً دوباره تلاش کنید.'}</AlertDescription>
        </Alert>
      )}

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-6 max-w-md mx-auto"
      >
        {/* Game Summary */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
            خلاصه بازی
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span style={{ color: 'var(--text-secondary)' }}>نام بازی:</span>
              <span style={{ color: 'var(--text-primary)' }}>{formData.gameName}</span>
            </div>
            <div className="flex justify-between">
              <span style={{ color: 'var(--text-secondary)' }}>نوع بازی:</span>
              <span style={{ color: 'var(--text-primary)' }}>
                {formData.gameType === 'wheel' && 'چرخ شانس'}
                {formData.gameType === 'lever' && 'اهرم شانس'}
                {formData.gameType === 'envelope' && 'پاکت‌های سورپرایز'}
              </span>
            </div>
            <div className="flex justify-between">
              <span style={{ color: 'var(--text-secondary)' }}>Number of Prizes:</span>
              <span style={{ color: 'var(--text-primary)' }}>{formData.prizes.length}</span>
            </div>
            {formData.gameId && (
              <div className="flex justify-between">
                <span style={{ color: 'var(--text-secondary)' }}>Game ID:</span>
                <span style={{ color: 'var(--text-primary)' }} className="font-mono text-xs">
                  {formData.gameId}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Game Link */}
        <div>
          <label className="block text-sm font-medium mb-1.5" style={{ color: 'var(--text-primary)' }}>
            Game Link
          </label>
          <div className="flex">
            <input
              type="text"
              value={gameLink}
              readOnly
              className="w-full px-3 py-2 border rounded-l-md"
              style={{
                backgroundColor: 'var(--background)',
                color: 'var(--text-primary)',
                borderColor: 'var(--border)',
              }}
            />
            <button
              onClick={copyLink}
              className="px-3 py-2 border border-l-0 rounded-r-md flex items-center"
              style={{
                backgroundColor: copied ? 'var(--secondary)' : 'var(--card-bg)',
                color: copied ? 'white' : 'var(--text-primary)',
                borderColor: 'var(--border)',
              }}
              disabled={isSaving || !gameLink}
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-1" />
                  <span className="text-sm">Copied</span>
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-1" />
                  <span className="text-sm">Copy</span>
                </>
              )}
            </button>
          </div>
          <div className="flex justify-between items-center mt-2">
            <p className="text-xs text-gray-500">
              This is a permanent link that will work even if you change your domain.
            </p>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1 text-xs"
              onClick={openGameLink}
              disabled={!gameLink}
            >
              <ExternalLink className="h-3 w-3" />
              Test Game
            </Button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 p-4 rounded-md">
          <h3 className="font-medium mb-2 text-blue-700">How to Use</h3>
          <p className="text-sm text-blue-600 mb-2">
            This link will directly open your {formData.gameType} game. When someone clicks it, they'll see exactly the
            game type you've created.
          </p>
          <ol className="text-sm text-blue-600 list-decimal pl-5 space-y-1">
            <li>Create a new Instagram Story</li>
            <li>Add a "Link" sticker</li>
            <li>Paste your game link</li>
            <li>Add a call-to-action like "Tap to play and win!"</li>
          </ol>
        </div>

        {/* Dashboard Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="pt-4"
        >
          <Button
            onClick={onFinish}
            className="w-full flex items-center justify-center gap-2"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--button-text)',
            }}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Saving Game...
              </>
            ) : (
              <>
                Go to Dashboard
                <ArrowRight className="h-4 w-4" />
              </>
            )}
          </Button>
        </motion.div>
      </motion.div>
    </div>
  );
}
