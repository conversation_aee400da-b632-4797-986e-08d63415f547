"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";

export default function TestSMSPage() {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [credentialsStatus, setCredentialsStatus] = useState<any>(null);
  const [credentialsLoading, setCredentialsLoading] = useState(false);

  const checkCredentials = async () => {
    setCredentialsLoading(true);
    try {
      const response = await fetch("/api/test-sms");
      const data = await response.json();
      setCredentialsStatus(data);
    } catch (error) {
      console.error("Error checking credentials:", error);
      setCredentialsStatus({
        success: false,
        error: "Failed to check credentials",
      });
    } finally {
      setCredentialsLoading(false);
    }
  };

  const sendTestSMS = async (service: string) => {
    if (!phoneNumber) {
      setResult({ success: false, error: "Phone number is required" });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const response = await fetch("/api/test-sms", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phoneNumber,
          message,
          service,
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error("Error sending test SMS:", error);
      setResult({ success: false, error: "Failed to send test SMS" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10 max-w-3xl">
      <h1 className="text-3xl font-bold mb-6">SMS Testing Tool</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Check SMS Credentials</CardTitle>
          <CardDescription>
            Verify that your SMS service credentials are configured correctly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={checkCredentials} disabled={credentialsLoading}>
            {credentialsLoading ? "Checking..." : "Check Credentials"}
          </Button>

          {credentialsStatus && (
            <div className="mt-4 space-y-4">
              <Alert
                variant={credentialsStatus.success ? "default" : "destructive"}
              >
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Status</AlertTitle>
                <AlertDescription>
                  {credentialsStatus.success
                    ? "Credentials check completed"
                    : credentialsStatus.error}
                </AlertDescription>
              </Alert>

              {credentialsStatus.melipayamak && (
                <Alert
                  variant={
                    credentialsStatus.melipayamak.valid
                      ? "default"
                      : "destructive"
                  }
                >
                  {credentialsStatus.melipayamak.valid ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  <AlertTitle>Melipayamak (OTP Service)</AlertTitle>
                  <AlertDescription>
                    {credentialsStatus.melipayamak.valid
                      ? `Connected successfully. Username: ${credentialsStatus.melipayamak.credentials.username}, Credit: ${credentialsStatus.melipayamak.credit}`
                      : "Missing credentials. Please check your environment variables."}
                  </AlertDescription>
                </Alert>
              )}

              {credentialsStatus.hyperSMS && (
                <Alert
                  variant={
                    credentialsStatus.hyperSMS.valid ? "default" : "destructive"
                  }
                >
                  {credentialsStatus.hyperSMS.valid ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  <AlertTitle>HyperSMS (Congratulation Service)</AlertTitle>
                  <AlertDescription>
                    {credentialsStatus.hyperSMS.valid
                      ? "Credentials are configured"
                      : "Missing credentials. Please check your SMS_USERNAME and SMS_PASSWORD environment variables."}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Send Test SMS</CardTitle>
          <CardDescription>
            Test sending SMS messages using different services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="melipayamak">
            <TabsList className="mb-4">
              <TabsTrigger value="melipayamak">Melipayamak (OTP)</TabsTrigger>
              <TabsTrigger value="hypersms">
                HyperSMS (Congratulations)
              </TabsTrigger>
            </TabsList>

            <TabsContent value="melipayamak">
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="phone-melipayamak"
                    className="block text-sm font-medium mb-1"
                  >
                    Phone Number
                  </label>
                  <Input
                    id="phone-melipayamak"
                    placeholder="e.g., +989123456789"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                  />
                </div>
                <div>
                  <label
                    htmlFor="message-melipayamak"
                    className="block text-sm font-medium mb-1"
                  >
                    Message (Optional)
                  </label>
                  <Textarea
                    id="message-melipayamak"
                    placeholder="Enter a custom message or leave blank for default"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                  />
                </div>
                <Button
                  onClick={() => sendTestSMS("melipayamak")}
                  disabled={loading}
                >
                  {loading ? "Sending..." : "Send Test SMS via Melipayamak"}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="hypersms">
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="phone-hypersms"
                    className="block text-sm font-medium mb-1"
                  >
                    Phone Number
                  </label>
                  <Input
                    id="phone-hypersms"
                    placeholder="e.g., +989123456789"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                  />
                </div>
                <div>
                  <label
                    htmlFor="message-hypersms"
                    className="block text-sm font-medium mb-1"
                  >
                    Congratulation Message (Optional)
                  </label>
                  <Textarea
                    id="message-hypersms"
                    placeholder="Enter a custom congratulation message or leave blank for default"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                  />
                </div>
                <Button
                  onClick={() => sendTestSMS("hypersms")}
                  disabled={loading}
                >
                  {loading ? "Sending..." : "Send Test SMS via HyperSMS"}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex flex-col items-start">
          {result && (
            <Alert
              variant={result.success ? "default" : "destructive"}
              className="w-full"
            >
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
              <AlertDescription>
                {result.success ? result.message : result.error}
                {result.details && (
                  <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
