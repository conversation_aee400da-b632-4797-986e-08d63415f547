"use client"

import { useState } from "react"
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"

export default function AccountSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  // Mock subscription data
  const subscription = {
    plan: "Pro",
    status: "active",
    billingCycle: "monthly",
    nextBillingDate: "May 15, 2023",
    amount: "$29.99",
  }

  const handleUpgrade = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Upgrade initiated",
        description: "You'll be redirected to complete your upgrade.",
      })
    }, 1000)
  }

  const handleCancelSubscription = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Cancellation requested",
        description: "Your subscription will remain active until the end of your billing period.",
      })
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-1" style={{ color: "var(--text-primary)" }}>
          Account Settings
        </h2>
        <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
          Manage your subscription, billing, and account details
        </p>
      </div>

      {/* Current Plan */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Current Plan</CardTitle>
              <CardDescription>Your current subscription details</CardDescription>
            </div>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              Active
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center pb-4 border-b" style={{ borderColor: "var(--border)" }}>
              <div>
                <h3 className="font-medium" style={{ color: "var(--text-primary)" }}>
                  {subscription.plan} Plan
                </h3>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Billed {subscription.billingCycle}
                </p>
              </div>
              <div className="text-right">
                <p className="font-bold text-lg" style={{ color: "var(--text-primary)" }}>
                  {subscription.amount}
                </p>
                <p className="text-xs" style={{ color: "var(--text-secondary)" }}>
                  Next billing: {subscription.nextBillingDate}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium" style={{ color: "var(--text-primary)" }}>
                Plan Features
              </h4>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Unlimited games</span>
                </li>
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Advanced analytics</span>
                </li>
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Custom branding</span>
                </li>
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Priority support</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleCancelSubscription} disabled={isLoading}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Cancel Subscription
          </Button>
          <Button onClick={handleUpgrade} disabled={isLoading} style={{ backgroundColor: "var(--primary)" }}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Upgrade Plan
          </Button>
        </CardFooter>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>View and download your past invoices</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center pb-2 border-b" style={{ borderColor: "var(--border)" }}>
              <div>
                <p className="font-medium" style={{ color: "var(--text-primary)" }}>
                  April 15, 2023
                </p>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Pro Plan - Monthly
                </p>
              </div>
              <div className="flex items-center gap-4">
                <span className="font-medium" style={{ color: "var(--text-primary)" }}>
                  $29.99
                </span>
                <Button variant="outline" size="sm">
                  Download
                </Button>
              </div>
            </div>
            <div className="flex justify-between items-center pb-2 border-b" style={{ borderColor: "var(--border)" }}>
              <div>
                <p className="font-medium" style={{ color: "var(--text-primary)" }}>
                  March 15, 2023
                </p>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Pro Plan - Monthly
                </p>
              </div>
              <div className="flex items-center gap-4">
                <span className="font-medium" style={{ color: "var(--text-primary)" }}>
                  $29.99
                </span>
                <Button variant="outline" size="sm">
                  Download
                </Button>
              </div>
            </div>
            <div className="flex justify-between items-center pb-2 border-b" style={{ borderColor: "var(--border)" }}>
              <div>
                <p className="font-medium" style={{ color: "var(--text-primary)" }}>
                  February 15, 2023
                </p>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Pro Plan - Monthly
                </p>
              </div>
              <div className="flex items-center gap-4">
                <span className="font-medium" style={{ color: "var(--text-primary)" }}>
                  $29.99
                </span>
                <Button variant="outline" size="sm">
                  Download
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600">Danger Zone</CardTitle>
          <CardDescription>Irreversible account actions</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Delete Account</AlertTitle>
            <AlertDescription>
              Permanently delete your account and all associated data. This action cannot be undone.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Button variant="destructive">Delete Account</Button>
        </CardFooter>
      </Card>
    </div>
  )
}
