import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { generateGameLink } from "@/utils/url-helpers"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import clientPromise from "@/lib/mongodb"
import { updateUserCredits } from "@/lib/models/user"

export async function POST(request: NextRequest) {
  try {
    console.log("API: Creating new game")

    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      console.log("API: Authentication failed - user not logged in")
      return NextResponse.json({ error: "You must be logged in to create a game" }, { status: 401 })
    }

    // Get the user ID from the session
    const userId = session.user.id
    console.log("API: User ID from session:", userId)

    // Parse the request body
    const data = await request.json()
    console.log("API: Received game data:", data)

    // Validate required fields
    if (!data.name || !data.type || !data.instagramHandle) {
      console.log("API: Missing required fields")
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Validate prize quantities
    if (data.prizes) {
      for (const prize of data.prizes) {
        // Ensure quantity is a positive number
        if (!prize.quantity || typeof prize.quantity !== "number" || prize.quantity < 0) {
          prize.quantity = 100 // Default to 100 if not specified or invalid
        }

        // Initialize remainingQuantity to match quantity
        prize.remainingQuantity = prize.quantity
      }
    }

    // Calculate total prize quantity for credit deduction
    const totalPrizes = data.prizes.reduce((sum, prize) => sum + prize.quantity, 0)

    // Update user credits based on selected plan
    if (data.selectedPlanId) {
      try {
        await updateUserCredits(userId, data.selectedPlanId, totalPrizes)
        console.log("API: Updated user credits for plan:", data.selectedPlanId)
      } catch (error) {
        console.error("API: Error updating user credits:", error)
        return NextResponse.json({ error: "Failed to update user credits" }, { status: 500 })
      }
    }

    // Create the game without a link first (we'll add it after we have the ID)
    const gameData = {
      userId,
      name: data.name,
      type: data.type,
      instagramHandle: data.instagramHandle,
      pageCategory: data.pageCategory || "",
      followerCount: data.followerCount || "",
      prizes: data.prizes || [],
      colorScheme: data.colorScheme || "purple",
      selectedPlan: data.selectedPlanId || "free",
      gameLink: "", // Temporary empty link
      status: "active",
      plays: 0,
      conversions: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActive: new Date(),
    }

    // Save the game to the database
    console.log("API: Saving game to database")
    const client = await clientPromise
    const db = client.db()
    const gamesCollection = db.collection("games")

    const result = await gamesCollection.insertOne(gameData)
    const gameId = result.insertedId

    console.log("API: Game saved with ID:", gameId)

    // Now that we have the real database ID, generate the proper game link
    const gameLink = generateGameLink(data.type, data.instagramHandle, gameId.toString())
    console.log("API: Generated game link:", gameLink)

    // Update the game in the database with the correct link
    await gamesCollection.updateOne({ _id: gameId }, { $set: { gameLink, updatedAt: new Date() } })

    console.log("API: Game link updated in database")

    // Fetch the complete game data to return
    const game = await gamesCollection.findOne({ _id: gameId })

    if (!game) {
      throw new Error("Failed to retrieve the created game")
    }

    // Return the created game with the proper link
    console.log("API: Returning game with ID:", gameId, "and link:", gameLink)
    return NextResponse.json({
      success: true,
      game: {
        ...game,
        _id: gameId.toString(), // Convert ObjectId to string for JSON
      },
    })
  } catch (error) {
    console.error("API Error creating game:", error)
    return NextResponse.json({ error: "Failed to create game" }, { status: 500 })
  }
}
