"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { motion } from "framer-motion"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"

interface GameFilterSidebarProps {
  isOpen: boolean
  onClose: () => void
  filters: {
    status: string[]
    type: string[]
    dateRange: string
  }
  updateFilters: (filters: any) => void
  resetFilters: () => void
}

export default function GameFilterSidebar({
  isOpen,
  onClose,
  filters,
  updateFilters,
  resetFilters,
}: GameFilterSidebarProps) {
  // Toggle status filter
  const toggleStatus = (status: string) => {
    if (filters.status.includes(status)) {
      updateFilters({ status: filters.status.filter((s) => s !== status) })
    } else {
      updateFilters({ status: [...filters.status, status] })
    }
  }

  // Toggle type filter
  const toggleType = (type: string) => {
    if (filters.type.includes(type)) {
      updateFilters({ type: filters.type.filter((t) => t !== type) })
    } else {
      updateFilters({ type: [...filters.type, type] })
    }
  }

  return (
    <motion.div
      className={`border-r w-72 overflow-y-auto flex-shrink-0 bg-white z-10 ${isOpen ? "block" : "hidden"}`}
      style={{ borderColor: "var(--border)" }}
      initial={{ x: -280 }}
      animate={{ x: isOpen ? 0 : -280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-lg" style={{ color: "var(--text-primary)" }}>
            Filters
          </h3>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-6">
          {/* Status Filter */}
          <div>
            <h4 className="font-medium mb-2 text-sm" style={{ color: "var(--text-primary)" }}>
              Status
            </h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-active"
                  checked={filters.status.includes("active")}
                  onCheckedChange={() => toggleStatus("active")}
                />
                <Label htmlFor="status-active" className="text-sm">
                  Active
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-inactive"
                  checked={filters.status.includes("inactive")}
                  onCheckedChange={() => toggleStatus("inactive")}
                />
                <Label htmlFor="status-inactive" className="text-sm">
                  Inactive
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-draft"
                  checked={filters.status.includes("draft")}
                  onCheckedChange={() => toggleStatus("draft")}
                />
                <Label htmlFor="status-draft" className="text-sm">
                  Draft
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Game Type Filter */}
          <div>
            <h4 className="font-medium mb-2 text-sm" style={{ color: "var(--text-primary)" }}>
              Game Type
            </h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="type-wheel"
                  checked={filters.type.includes("wheel")}
                  onCheckedChange={() => toggleType("wheel")}
                />
                <Label htmlFor="type-wheel" className="text-sm">
                  Lucky Wheel
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="type-lever"
                  checked={filters.type.includes("lever")}
                  onCheckedChange={() => toggleType("lever")}
                />
                <Label htmlFor="type-lever" className="text-sm">
                  Chance Lever
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="type-envelope"
                  checked={filters.type.includes("envelope")}
                  onCheckedChange={() => toggleType("envelope")}
                />
                <Label htmlFor="type-envelope" className="text-sm">
                  Surprise Envelopes
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Date Range Filter */}
          <div>
            <h4 className="font-medium mb-2 text-sm" style={{ color: "var(--text-primary)" }}>
              Date Created
            </h4>
            <RadioGroup
              value={filters.dateRange}
              onValueChange={(value) => updateFilters({ dateRange: value })}
              className="space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="all" id="date-all" />
                <Label htmlFor="date-all" className="text-sm flex items-center gap-1">
                  <Clock className="h-3.5 w-3.5" />
                  All Time
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="30days" id="date-30" />
                <Label htmlFor="date-30" className="text-sm flex items-center gap-1">
                  <Calendar className="h-3.5 w-3.5" />
                  Last 30 Days
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="90days" id="date-90" />
                <Label htmlFor="date-90" className="text-sm flex items-center gap-1">
                  <Calendar className="h-3.5 w-3.5" />
                  Last 90 Days
                </Label>
              </div>
            </RadioGroup>
          </div>
        </div>

        <div className="mt-8 space-y-2">
          <Button variant="outline" className="w-full" onClick={resetFilters}>
            Reset Filters
          </Button>
          <Button
            className="w-full"
            style={{ backgroundColor: "var(--primary)", color: "var(--button-text)" }}
            onClick={onClose}
          >
            Apply Filters
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
