"use client"

import type React from "react"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Lock } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface SettingsSidebarProps {
  activeTab: string
  setActiveTab: (tab: string) => void
}

interface SettingsNavItemProps {
  icon: React.ElementType
  label: string
  value: string
  active: boolean
  onClick: () => void
}

function SettingsNavItem({ icon: Icon, label, active, onClick }: SettingsNavItemProps) {
  return (
    <Button
      variant="ghost"
      className={`w-full justify-start ${active ? "bg-primary/10" : ""}`}
      onClick={onClick}
      style={{ color: active ? "var(--primary)" : "var(--text-primary)" }}
    >
      <Icon className="h-5 w-5 mr-2" />
      {label}
    </Button>
  )
}

export default function SettingsSidebar({ activeTab, setActiveTab }: SettingsSidebarProps) {
  const navItems = [
    { icon: User, label: "Profile", value: "profile" },
    { icon: <PERSON><PERSON><PERSON>, label: "Account", value: "account" },
    { icon: Bell, label: "Notifications", value: "notifications" },
    { icon: Palette, label: "Appearance", value: "appearance" },
    { icon: Link, label: "Integrations", value: "integrations" },
    { icon: Shield, label: "Security", value: "security" },
    { icon: Lock, label: "Privacy", value: "privacy" },
  ]

  return (
    <div className="space-y-1 border rounded-lg p-3" style={{ borderColor: "var(--border)" }}>
      {navItems.map((item) => (
        <SettingsNavItem
          key={item.value}
          icon={item.icon}
          label={item.label}
          value={item.value}
          active={activeTab === item.value}
          onClick={() => setActiveTab(item.value)}
        />
      ))}
    </div>
  )
}
