import NextAuth, { type NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import { getUserByMobileNumber, getUserByEmail, verifyPassword, createGoogleUser } from '@/lib/models/user';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        mobileNumber: { label: 'Mobile Number', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.mobileNumber || !credentials?.password) {
          return null;
        }

        try {
          const user = await getUserByMobileNumber(credentials.mobileNumber);

          if (!user) {
            return null;
          }

          const isValid = await verifyPassword(user, credentials.password);

          if (!isValid) {
            return null;
          }

          return {
            id: user._id.toString(),
            name: user.name,
            email: user.email || undefined,
            mobileNumber: user.mobileNumber,
          };
        } catch (error) {
          console.error('Error in authorize:', error);
          return null;
        }
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
          scope: 'openid email profile',
        },
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          console.log('Google sign in attempt for user:', user.email);

          // Check if user already exists by email
          let existingUser = await getUserByEmail(user.email!);

          if (!existingUser) {
            console.log('Creating new Google user for:', user.email);
            // Create new Google user
            existingUser = await createGoogleUser({
              name: user.name!,
              email: user.email!,
              image: user.image,
            });
            console.log('Created Google user with ID:', existingUser._id);
          } else {
            console.log('Found existing Google user with ID:', existingUser._id);
          }

          // Update user object with database ID for JWT callback
          user.id = existingUser._id!.toString();
          user.mobileNumber = existingUser.mobileNumber;
          user.provider = 'google';

          console.log('Updated user object:', {
            id: user.id,
            email: user.email,
            provider: user.provider,
          });

          return true;
        } catch (error) {
          console.error('Error in Google sign in:', error);
          return false;
        }
      }

      return true;
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.mobileNumber = user.mobileNumber;
        token.provider = user.provider || account?.provider;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.mobileNumber = token.mobileNumber as string;
        session.user.provider = token.provider as string;

        // Debug logging
        console.log('Session callback - token:', {
          id: token.id,
          email: token.email,
          provider: token.provider,
        });
        console.log('Session callback - session.user:', session.user);
      }
      return session;
    },
  },
  pages: {
    signIn: '/signin',
    signOut: '/',
    error: '/signin',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
