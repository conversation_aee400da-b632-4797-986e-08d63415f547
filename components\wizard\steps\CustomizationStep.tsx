'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Palette, Plus, Minus, Trash2, Package, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { WizardFormData } from '../GameWizard';
import WheelGame from '@/components/game/games/WheelGame';

interface CustomizationStepProps {
  formData: WizardFormData;
  updateFormData: (data: Partial<WizardFormData>) => void;
}

const colorSchemes = [
  { id: 'purple', name: 'بنفش', primary: '#8b5cf6', secondary: '#F9A8D4' },
  { id: 'blue', name: 'آبی', primary: '#3b82f6', secondary: '#93c5fd' },
  { id: 'green', name: 'سبز', primary: '#84cc16', secondary: '#bef264' },
  { id: 'orange', name: 'نارنجی', primary: '#f97316', secondary: '#fdba74' },
];

const prizeTypes = ['کد تخفیف', 'آیتم رایگان', 'ارسال رایگان', 'پیشنهاد ویژه', 'پیام تشکر'];

export default function CustomizationStep({ formData, updateFormData }: CustomizationStepProps) {
  const [totalProbability, setTotalProbability] = useState(
    formData.prizes.reduce((sum, prize) => sum + prize.probability, 0)
  );
  const [showProbabilityInfo, setShowProbabilityInfo] = useState(false);

  // Ensure all prizes have quantity and remainingQuantity fields
  useEffect(() => {
    const updatedPrizes = formData.prizes.map((prize) => ({
      ...prize,
      quantity: prize.quantity || 100,
      remainingQuantity: prize.remainingQuantity || prize.quantity || 100,
    }));

    if (JSON.stringify(updatedPrizes) !== JSON.stringify(formData.prizes)) {
      updateFormData({ prizes: updatedPrizes });
    }
  }, []);

  const handlePrizeChange = (index: number, field: string, value: any) => {
    const updatedPrizes = [...formData.prizes];

    // Update the specific field
    updatedPrizes[index] = {
      ...updatedPrizes[index],
      [field]: value,
    };

    // If updating quantity, also update remainingQuantity to match
    if (field === 'quantity') {
      updatedPrizes[index].remainingQuantity = value;
    }

    // Recalculate total probability
    const newTotal = updatedPrizes.reduce((sum, prize) => sum + prize.probability, 0);
    setTotalProbability(newTotal);

    updateFormData({ prizes: updatedPrizes });
  };

  const addPrize = () => {
    if (formData.prizes.length >= 6) return; // Limit to 6 prizes

    const newPrize = {
      type: 'Discount',
      description: '',
      probability: 0,
      quantity: 100,
      remainingQuantity: 100,
      icon: 'QuestionMark',
    };

    updateFormData({ prizes: [...formData.prizes, newPrize] });
  };

  const removePrize = (index: number) => {
    if (formData.prizes.length <= 1) return; // Keep at least one prize

    const updatedPrizes = formData.prizes.filter((_, i) => i !== index);

    // Recalculate total probability
    const newTotal = updatedPrizes.reduce((sum, prize) => sum + prize.probability, 0);
    setTotalProbability(newTotal);

    updateFormData({ prizes: updatedPrizes });
  };

  // Distribute remaining probability evenly
  const distributeRemainingProbability = () => {
    if (totalProbability === 100) return; // Already at 100%

    const remaining = 100 - totalProbability;
    const updatedPrizes = [...formData.prizes];

    // Count prizes with non-zero probability
    const prizesWithProbability = updatedPrizes.filter((prize) => prize.probability > 0).length;
    const prizesWithoutProbability = updatedPrizes.length - prizesWithProbability;

    // Determine which prizes to distribute to
    const targetPrizes =
      prizesWithoutProbability > 0
        ? updatedPrizes.filter((prize) => prize.probability === 0) // Distribute to prizes with 0 probability
        : updatedPrizes; // Distribute to all prizes

    const distributionPerPrize = remaining / targetPrizes.length;

    // Update probabilities
    const finalPrizes = updatedPrizes.map((prize) => {
      if (targetPrizes.includes(prize)) {
        return {
          ...prize,
          probability: Math.round((prize.probability + distributionPerPrize) * 100) / 100,
        };
      }
      return prize;
    });

    // Ensure total is exactly 100%
    const newTotal = finalPrizes.reduce((sum, prize) => sum + prize.probability, 0);
    if (newTotal !== 100 && finalPrizes.length > 0) {
      const diff = 100 - newTotal;
      finalPrizes[0].probability = Math.round((finalPrizes[0].probability + diff) * 100) / 100;
    }

    setTotalProbability(100);
    updateFormData({ prizes: finalPrizes });
  };

  return (
    <div className="py-4">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
        <div
          className="inline-flex items-center justify-center h-16 w-16 rounded-full mb-4"
          style={{ backgroundColor: 'rgba(139, 92, 246, 0.1)' }}
        >
          <Palette className="h-8 w-8" style={{ color: 'var(--primary)' }} />
        </div>
        <h2 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
          بیایید بازی‌تان را بسازیم!
        </h2>
        <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
          بازی خود را متناسب با برند و اهدافتان سفارشی کنید
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-6"
      >
        {/* Game Name */}
        <div>
          <label className="block text-sm font-medium mb-1.5" style={{ color: 'var(--text-primary)' }}>
            نام کمپین/بازی*
          </label>
          <input
            type="text"
            value={formData.gameName}
            onChange={(e) => updateFormData({ gameName: e.target.value })}
            className="w-full px-3 py-2 border rounded-md"
            style={{
              backgroundColor: 'var(--background)',
              color: 'var(--text-primary)',
              borderColor: 'var(--border)',
            }}
            placeholder="Summer Sale Spin & Win"
            required
          />
        </div>

        {/* Color Scheme */}
        {/* Color Scheme */}
        <div>
          <label className="block text-sm font-medium mb-1.5" style={{ color: 'var(--text-primary)' }}>
            Color Scheme
          </label>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
            {colorSchemes.map((scheme) => (
              <div
                key={scheme.id}
                className={`cursor-pointer rounded-md p-3 border-2 transition-all ${
                  formData.colorScheme === scheme.id
                    ? 'border-opacity-100 border-4' // انتخاب شده: پررنگ‌تر بودن حاشیه
                    : 'border-opacity-30'
                }`}
                style={{
                  borderColor: scheme.primary,
                  backgroundColor: `${scheme.primary}10`,
                }}
                onClick={() => updateFormData({ colorScheme: scheme.id })}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded-full"
                    style={{
                      background: `linear-gradient(to right, ${scheme.primary}, ${scheme.secondary})`,
                    }}
                  />
                  <span style={{ color: 'var(--text-primary)' }}>{scheme.name}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Prizes */}
        <div>
          <div className="flex justify-between items-center mb-1.5">
            <div className="flex items-center gap-2">
              <label className="block text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                Prizes
              </label>
              <button
                type="button"
                className="text-gray-500 hover:text-gray-700"
                onClick={() => setShowProbabilityInfo(!showProbabilityInfo)}
              >
                <Info className="h-4 w-4" />
              </button>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-xs" style={{ color: totalProbability === 100 ? 'green' : 'red' }}>
                Total Probability: {totalProbability}% {totalProbability === 100 ? '✓' : '(should be 100%)'}
              </div>
              {totalProbability !== 100 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="text-xs py-1 h-auto"
                  onClick={distributeRemainingProbability}
                >
                  Auto-Fix
                </Button>
              )}
            </div>
          </div>

          {showProbabilityInfo && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-100 rounded-md text-sm text-blue-800 flex items-start gap-2">
              <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium mb-1">About Prize Distribution</p>
                <p>
                  When a prize runs out, its probability will be automatically redistributed among the remaining prizes.
                </p>
              </div>
            </div>
          )}

          <div className="space-y-3 mb-3">
            {formData.prizes.map((prize, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 * index }}
                className="flex flex-col sm:flex-row gap-3 p-3 rounded-md"
                style={{ backgroundColor: 'var(--card-bg)' }}
              >
                <div className="flex-1">
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                    Prize Type
                  </label>
                  <select
                    value={prize.type}
                    onChange={(e) => handlePrizeChange(index, 'type', e.target.value)}
                    className="w-full px-2 py-1.5 border rounded-md text-sm"
                    style={{
                      backgroundColor: 'var(--background)',
                      color: 'var(--text-primary)',
                      borderColor: 'var(--border)',
                    }}
                  >
                    {prizeTypes.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex-1">
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                    Description
                  </label>
                  <input
                    type="text"
                    value={prize.description}
                    onChange={(e) => handlePrizeChange(index, 'description', e.target.value)}
                    className="w-full px-2 py-1.5 border rounded-md text-sm"
                    style={{
                      backgroundColor: 'var(--background)',
                      color: 'var(--text-primary)',
                      borderColor: 'var(--border)',
                    }}
                    placeholder="e.g. 10% Off"
                  />
                </div>

                {formData.gameType === 'lever' && (
                  <div className="flex-1">
                    <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                      Icon
                    </label>
                    <select
                      value={prize.icon || 'QuestionMark'}
                      onChange={(e) => handlePrizeChange(index, 'icon', e.target.value)}
                      className="w-full px-2 py-1.5 border rounded-md text-sm"
                      style={{
                        backgroundColor: 'var(--background)',
                        color: 'var(--text-primary)',
                        borderColor: 'var(--border)',
                      }}
                    >
                      <option value="Star">Star</option>
                      <option value="Heart">Heart</option>
                      <option value="Gift">Gift</option>
                      <option value="Trophy">Trophy</option>
                      <option value="Diamond">Diamond</option>
                      <option value="Sparkles">Sparkles</option>
                      <option value="Zap">Lightning</option>
                      <option value="DollarSign">Dollar</option>
                      <option value="QuestionMark">Question Mark</option>
                    </select>
                  </div>
                )}

                {/* Quantity Field */}
                <div className="flex-1">
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                    Quantity
                  </label>
                  <div className="flex items-center">
                    <button
                      type="button"
                      onClick={() => handlePrizeChange(index, 'quantity', Math.max(1, prize.quantity - 10))}
                      className="px-2 py-1.5 border rounded-l-md"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <Minus className="h-3 w-3" />
                    </button>
                    <input
                      type="number"
                      min="1"
                      value={prize.quantity}
                      onChange={(e) => handlePrizeChange(index, 'quantity', Number.parseInt(e.target.value) || 1)}
                      className="w-full px-2 py-1.5 border-y text-center text-sm"
                      style={{
                        backgroundColor: 'var(--background)',
                        color: 'var(--text-primary)',
                        borderColor: 'var(--border)',
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => handlePrizeChange(index, 'quantity', prize.quantity + 10)}
                      className="px-2 py-1.5 border rounded-r-md"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <Plus className="h-3 w-3" />
                    </button>
                  </div>
                  <div className="text-xs mt-1 flex items-center" style={{ color: 'var(--text-secondary)' }}>
                    <Package className="h-3 w-3 mr-1" />
                    <span>Total available: {prize.quantity}</span>
                  </div>
                </div>

                {/* Probability Field */}
                <div className="w-full sm:w-32">
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                    Win %
                  </label>
                  <div className="flex items-center">
                    <button
                      type="button"
                      onClick={() => handlePrizeChange(index, 'probability', Math.max(0, prize.probability - 5))}
                      className="px-2 py-1.5 border rounded-l-md"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <Minus className="h-3 w-3" />
                    </button>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={prize.probability}
                      onChange={(e) => handlePrizeChange(index, 'probability', Number.parseInt(e.target.value) || 0)}
                      className="w-full px-2 py-1.5 border-y text-center text-sm"
                      style={{
                        backgroundColor: 'var(--background)',
                        color: 'var(--text-primary)',
                        borderColor: 'var(--border)',
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => handlePrizeChange(index, 'probability', Math.min(100, prize.probability + 5))}
                      className="px-2 py-1.5 border rounded-r-md"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <Plus className="h-3 w-3" />
                    </button>
                  </div>
                </div>

                <button
                  type="button"
                  onClick={() => removePrize(index)}
                  className="self-end sm:self-center px-2 py-1.5 text-red-500 rounded-md hover:bg-red-50"
                  disabled={formData.prizes.length <= 1}
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </motion.div>
            ))}
          </div>

          <Button
            type="button"
            variant="outline"
            onClick={addPrize}
            className="w-full flex items-center justify-center gap-2"
            disabled={formData.prizes.length >= 6}
          >
            <Plus className="h-4 w-4" />
            Add Prize
          </Button>
        </div>

        {/* Game Preview */}
        <div>
          <label className="block text-sm font-medium mb-1.5" style={{ color: 'var(--text-primary)' }}>
            Preview
          </label>
          <div
            className="h-48 rounded-md overflow-hidden relative flex items-center justify-center"
            style={{ backgroundColor: 'var(--card-bg)' }}
          >
            {formData.gameType === 'wheel' && (
              <div
                style={{
                  transform: 'scale(0.625)', // مقیاس 0.625 برای کاهش اندازه به 160x160 پیکسل
                  transformOrigin: 'center center', // مقیاس‌بندی از مرکز
                  width: '256px', // اندازه اصلی WheelGame
                  height: '256px', // اندازه اصلی WheelGame
                  position: 'relative', // برای تنظیم موقعیت
                  top: '-15px', // جابه‌جایی به سمت بالا برای قرارگیری دقیق در وسط
                }}
              >
                <WheelGame
                  prizes={formData.prizes.map((prize) => ({
                    name: prize.description || prize.type,
                    probability: prize.probability,
                    isAvailable: prize.remainingQuantity > 0,
                  }))}
                  primaryColor={colorSchemes.find((c) => c.id === formData.colorScheme)?.primary || '#8b5cf6'}
                  secondaryColor={colorSchemes.find((c) => c.id === formData.colorScheme)?.secondary || '#F9A8D4'}
                  isPlaying={false}
                  onComplete={() => {}}
                />
              </div>
            )}

            {formData.gameType === 'lever' && (
              <div className="flex items-center justify-center w-full h-full">
                <div style={{ transform: 'scale(0.7)' }}>
                  <div className="relative flex flex-col items-center justify-center">
                    <div
                      className="relative w-64 h-48 rounded-lg overflow-hidden"
                      style={{
                        backgroundColor: colorSchemes.find((c) => c.id === formData.colorScheme)?.primary || '#8b5cf6',
                      }}
                    >
                      <div className="absolute top-4 left-0 right-0 flex justify-center">
                        <div className="flex gap-2 p-4 bg-white rounded-md shadow-inner">
                          {[0, 1, 2].map((index) => (
                            <div
                              key={index}
                              className="w-12 h-16 bg-gray-100 rounded flex items-center justify-center text-2xl font-bold"
                              style={{
                                borderColor:
                                  colorSchemes.find((c) => c.id === formData.colorScheme)?.secondary || '#F9A8D4',
                                borderWidth: '1px',
                                borderStyle: 'solid',
                              }}
                            >
                              ?
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="absolute bottom-0 right-8 w-8 h-16 bg-gray-800 rounded-t-lg" />
                      <motion.div
                        className="absolute bottom-16 right-8 w-8 h-24 flex flex-col items-center"
                        animate={{
                          rotateZ: [0, -3, 3, -2, 0],
                          y: [0, -2, 2, -1, 0],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Number.POSITIVE_INFINITY,
                          repeatType: 'reverse',
                          ease: 'easeInOut',
                          repeatDelay: 1,
                        }}
                      >
                        <div
                          className="w-8 h-8 rounded-full shadow-md"
                          style={{
                            backgroundColor:
                              colorSchemes.find((c) => c.id === formData.colorScheme)?.secondary || '#F9A8D4',
                          }}
                        ></div>
                        <div
                          className="w-4 h-24 shadow-sm"
                          style={{
                            backgroundColor:
                              colorSchemes.find((c) => c.id === formData.colorScheme)?.secondary || '#F9A8D4',
                          }}
                        ></div>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {formData.gameType === 'envelope' && (
              <div className="flex items-center justify-center gap-4">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="w-20 h-24 rounded-md flex items-center justify-center"
                    style={{
                      backgroundColor:
                        i % 2 === 0
                          ? colorSchemes.find((c) => c.id === formData.colorScheme)?.primary || '#8b5cf6'
                          : colorSchemes.find((c) => c.id === formData.colorScheme)?.secondary || '#F9A8D4',
                    }}
                    whileHover={{ scale: 1.1 }}
                    animate={{ rotate: [0, i % 2 === 0 ? 5 : -5, 0] }}
                    transition={{
                      repeat: Number.POSITIVE_INFINITY,
                      duration: 2,
                      delay: i * 0.3,
                    }}
                  >
                    <span className="text-white font-bold text-xl">?</span>
                  </motion.div>
                ))}
              </div>
            )}

            {!formData.gameType && (
              <div className="text-center">
                <p style={{ color: 'var(--text-secondary)' }}>Select a game type to see preview</p>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
