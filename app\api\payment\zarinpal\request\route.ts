import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { createPayment } from '@/lib/models/payment';

// ZarinPal configuration
const ZARINPAL_MERCHANT_ID = process.env.ZARINPAL_MERCHANT_ID || 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx';
const ZARINPAL_SANDBOX = process.env.NODE_ENV !== 'production';
const ZARINPAL_BASE_URL = ZARINPAL_SANDBOX
  ? 'https://sandbox.zarinpal.com/pg/rest/WebGate'
  : 'https://api.zarinpal.com/pg/rest/WebGate';

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { amount, description = 'خرید اعتبارات بازی' } = body;

    // Validate amount
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json({ error: 'Invalid amount' }, { status: 400 });
    }

    // Convert amount to Rials (ZarinPal expects Rials, but we store in Tomans)
    const amountInRials = amount * 10;

    // Prepare callback URL
    const callbackUrl = `${process.env.NEXTAUTH_URL}/api/payment/zarinpal/verify`;

    // Prepare ZarinPal request
    const zarinpalRequest = {
      merchant_id: ZARINPAL_MERCHANT_ID,
      amount: amountInRials,
      description: description,
      callback_url: callbackUrl,
      metadata: {
        email: session.user.email || '',
        mobile: session.user.phone || '',
        user_id: session.user.id,
        credits_amount: Math.floor(amount / 2000), // Calculate credits based on price per credit
      },
    };

    console.log('ZarinPal Request:', zarinpalRequest);

    // Send request to ZarinPal
    const response = await fetch(`${ZARINPAL_BASE_URL}/PaymentRequest.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(zarinpalRequest),
    });

    const zarinpalResponse = await response.json();
    console.log('ZarinPal Response:', zarinpalResponse);

    if (zarinpalResponse.Status === 100) {
      // Success - save payment to database
      const creditsAmount = Math.floor(amount / 2000); // Calculate credits based on price per credit

      try {
        await createPayment({
          userId: session.user.id,
          authority: zarinpalResponse.Authority,
          amount: amount, // Amount in Tomans
          creditsAmount: creditsAmount,
          status: 'pending',
          description: description,
        });
      } catch (error) {
        console.error('Error saving payment to database:', error);
        // Continue anyway, as the payment request was successful
      }

      // Redirect user to payment gateway
      const paymentUrl = ZARINPAL_SANDBOX
        ? `https://sandbox.zarinpal.com/pg/StartPay/${zarinpalResponse.Authority}`
        : `https://www.zarinpal.com/pg/StartPay/${zarinpalResponse.Authority}`;

      return NextResponse.json({
        success: true,
        authority: zarinpalResponse.Authority,
        payment_url: paymentUrl,
      });
    } else {
      console.error('ZarinPal Error:', zarinpalResponse);
      return NextResponse.json(
        {
          error: 'Payment request failed',
          details: zarinpalResponse.Status,
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Payment request error:', error);
    return NextResponse.json({ error: 'Failed to create payment request' }, { status: 500 });
  }
}
