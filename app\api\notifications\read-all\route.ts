import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { markAllNotificationsAsRead } from "@/lib/models/notification"

// Mark all notifications as read for the current user
export async function PUT() {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id

    // Mark all as read
    const updatedCount = await markAllNotificationsAsRead(userId)

    return NextResponse.json({
      success: true,
      updatedCount,
    })
  } catch (error) {
    console.error("Error marking all notifications as read:", error)
    return NextResponse.json({ error: "Failed to mark notifications as read", details: error.message }, { status: 500 })
  }
}
