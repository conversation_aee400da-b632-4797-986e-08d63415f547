import { type NextRequest, NextResponse } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function POST(
  request: NextRequest,
  { params }: { params: { gameId: string | Promise<string> } }
) {
  try {
    console.log('API: Recording play time');

    // Get the gameId from params
    const resolvedParams = await params;
    const gameId = resolvedParams.gameId;

    // Parse request body
    const body = await request.json();
    const { playDuration, phoneNumber, sessionId, exitReason = 'completed' } = body;

    if (!playDuration || playDuration <= 0) {
      return NextResponse.json({ error: 'Valid play duration is required' }, { status: 400 });
    }

    console.log('API: Recording play time for gameId:', gameId, 'duration:', playDuration, 'seconds');

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db('instagram_gamification');

    // Find the game first
    let actualGameId = gameId;
    let game = null;

    // Try to find by ObjectId first
    if (ObjectId.isValid(gameId)) {
      game = await db.collection('games').findOne({ _id: new ObjectId(gameId) });
      if (game) {
        actualGameId = gameId;
      }
    }

    // If not found, try to find by gameLink
    if (!game) {
      game = await db.collection('games').findOne({
        gameLink: { $regex: gameId, $options: 'i' },
      });

      if (game && game._id) {
        actualGameId = game._id.toString();
      } else {
        console.log('API: Game not found for play time recording');
        return NextResponse.json({ error: 'Game not found' }, { status: 404 });
      }
    }

    // Create session record
    const sessionRecord = {
      sessionId: sessionId || `playtime_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      startTime: new Date(Date.now() - playDuration * 1000), // Calculate start time
      endTime: new Date(),
      duration: playDuration,
      phoneNumber: phoneNumber || null,
      completed: exitReason === 'completed',
      exitReason: exitReason,
    };

    console.log('API: Adding session record:', sessionRecord);

    // Add session to the game
    const result = await db.collection('games').updateOne(
      { _id: new ObjectId(actualGameId) },
      {
        $push: { 'gameInteractions.sessions': sessionRecord },
        $set: { lastActive: new Date(), updatedAt: new Date() },
      }
    );

    if (result.modifiedCount === 0) {
      return NextResponse.json({ error: 'Failed to record play time' }, { status: 500 });
    }

    // Recalculate session metrics
    await recalculateSessionMetrics(db, actualGameId);

    console.log('API: Play time recorded successfully');

    return NextResponse.json({
      success: true,
      message: 'Play time recorded successfully',
      sessionId: sessionRecord.sessionId,
      duration: playDuration,
    });

  } catch (error) {
    console.error('API Error recording play time:', error);
    return NextResponse.json(
      {
        error: 'Failed to record play time',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper function to recalculate session metrics
async function recalculateSessionMetrics(db: any, gameId: string) {
  try {
    console.log('API: Recalculating session metrics for game:', gameId);

    // Get all sessions for this game
    const game = await db.collection('games').findOne(
      { _id: new ObjectId(gameId) },
      { projection: { 'gameInteractions.sessions': 1 } }
    );

    if (!game || !game.gameInteractions?.sessions) {
      console.log('API: No sessions found for metrics calculation');
      return;
    }

    const sessions = game.gameInteractions.sessions;
    const completedSessions = sessions.filter((s: any) => s.completed && s.duration);
    
    const totalSessions = sessions.length;
    const averageSessionTime = completedSessions.length > 0 
      ? Math.round(completedSessions.reduce((sum: number, s: any) => sum + (s.duration || 0), 0) / completedSessions.length)
      : 0;
    const completionRate = totalSessions > 0 
      ? Math.round((completedSessions.length / totalSessions) * 100)
      : 0;

    console.log('API: Calculated metrics - total:', totalSessions, 'avg time:', averageSessionTime, 'completion:', completionRate);

    // Update session metrics
    await db.collection('games').updateOne(
      { _id: new ObjectId(gameId) },
      {
        $set: {
          'sessionMetrics.totalSessions': totalSessions,
          'sessionMetrics.averageSessionTime': averageSessionTime,
          'sessionMetrics.completionRate': completionRate,
          'sessionMetrics.lastCalculated': new Date(),
          updatedAt: new Date(),
        },
      }
    );

    console.log('API: Session metrics updated successfully');
  } catch (error) {
    console.error('API: Error recalculating session metrics:', error);
  }
}
