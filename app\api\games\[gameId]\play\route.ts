import { type NextRequest, NextResponse } from "next/server";
import { incrementGamePlays, getGameById } from "@/lib/models/game";

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ gameId: string }> }
) {
  try {
    // Await params before destructuring gameId
    const { gameId } = await context.params;
    console.log("API: Recording play for game ID:", gameId);

    // Get the game to check the user ID
    const game = await getGameById(gameId);

    if (!game) {
      console.log("API: Game not found");
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    // Increment the play count
    const success = await incrementGamePlays(gameId);

    if (!success) {
      console.log("API: Failed to increment play count");
      return NextResponse.json(
        { error: "Failed to record play" },
        { status: 500 }
      );
    }

    console.log("API: Play recorded successfully");
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("API Error recording play:", error);
    return NextResponse.json(
      { error: "Failed to record play" },
      { status: 500 }
    );
  }
}
