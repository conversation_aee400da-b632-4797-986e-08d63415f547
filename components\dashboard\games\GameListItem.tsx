"use client"

import { <PERSON>, <PERSON>, MoreH<PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import type { Game } from "@/types/game"

interface GameListItemProps {
  game: Game
  isSelected: boolean
  onSelect: () => void
  onViewDetails: () => void
}

export default function GameListItem({ game, isSelected, onSelect, onViewDetails }: GameListItemProps) {
  // Calculate conversion rate
  const conversionRate = Math.round((game.conversions / game.plays) * 100)

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" })
  }

  return (
    <tr className="border-b hover:bg-gray-50" style={{ borderColor: "var(--border)" }}>
      <td className="p-3">
        <Checkbox checked={isSelected} onCheckedChange={onSelect} aria-label={`Select ${game.name}`} />
      </td>
      <td className="p-3" style={{ color: "var(--text-primary)" }}>
        <div className="font-medium">{game.name}</div>
      </td>
      <td className="p-3 capitalize" style={{ color: "var(--text-primary)" }}>
        {game.type}
      </td>
      <td className="p-3" style={{ color: "var(--text-primary)" }}>
        {game.plays.toLocaleString()}
      </td>
      <td className="p-3" style={{ color: "var(--text-primary)" }}>
        {game.conversions.toLocaleString()}
      </td>
      <td className="p-3" style={{ color: "var(--text-primary)" }}>
        {conversionRate}%
      </td>
      <td className="p-3">
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            game.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
          }`}
        >
          {game.status}
        </span>
      </td>
      <td className="p-3 text-sm" style={{ color: "var(--text-secondary)" }}>
        {formatDate(game.createdAt)}
      </td>
      <td className="p-3">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={onViewDetails} aria-label="View details">
            <Eye className="h-4 w-4" style={{ color: "var(--primary)" }} />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" aria-label="Edit game">
            <Edit className="h-4 w-4" style={{ color: "var(--text-secondary)" }} />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem className="text-red-500 cursor-pointer">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </td>
    </tr>
  )
}
