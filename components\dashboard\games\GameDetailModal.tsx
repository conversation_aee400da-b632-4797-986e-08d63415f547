'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { formatSessionTimePersian } from '@/lib/utils/sessionUtils';
import {
  BarChart3,
  Check,
  Copy,
  Edit,
  Eye,
  Share2,
  X,
  Download,
  Info,
  Minus,
  Plus,
  Trash2,
  Package,
  Loader2,
  ExternalLink,
  QrCode,
  Facebook,
  Twitter,
  Linkedin,
  MessageCircle,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import type { Game } from '@/types/game';
import GamePerformanceChart from './GamePerformanceChart';
import GamePrizeDistribution from './GamePrizeDistribution';
import { useToast } from '@/components/ui/use-toast';
import GamePreview from './GamePreview';

interface GameDetailModalProps {
  game: Game;
  onClose: () => void;
  onGameDeleted?: () => void;
  onUpdate?: (updatedGame: Game) => void;
}

export default function GameDetailModal({ game, onClose, onGameDeleted, onUpdate }: GameDetailModalProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isActive, setIsActive] = useState(game.status === 'active');
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);
  const [copied, setCopied] = useState(false);
  const [recentActivity, setRecentActivity] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [analyticsPeriod, setAnalyticsPeriod] = useState('30days');
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editedGame, setEditedGame] = useState<Game | null>(null);
  const [totalProbability, setTotalProbability] = useState(100);

  // New state for modals and actions
  const [showShareModal, setShowShareModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isViewingLive, setIsViewingLive] = useState(false);

  const { toast } = useToast();

  // Calculate real average session time for this game
  const calculateGameAverageSessionTime = () => {
    console.log('Calculating session time for game:', game.id);
    console.log('Game interactions:', game.gameInteractions);

    // Method 1: Use session data if available
    if (game.gameInteractions?.sessions && game.gameInteractions.sessions.length > 0) {
      console.log('Found sessions:', game.gameInteractions.sessions.length);
      const completedSessions = game.gameInteractions.sessions.filter((s: any) => s.completed && s.duration);
      console.log('Completed sessions:', completedSessions.length);

      if (completedSessions.length > 0) {
        const totalDuration = completedSessions.reduce((sum: number, s: any) => sum + (s.duration || 0), 0);
        const avgTime = Math.round(totalDuration / completedSessions.length);
        console.log('Average session time from sessions:', avgTime);
        return avgTime;
      }
    }

    // Method 2: Use sessionMetrics if available
    if (game.sessionMetrics?.averageSessionTime && game.sessionMetrics.averageSessionTime > 0) {
      console.log('Using sessionMetrics:', game.sessionMetrics.averageSessionTime);
      return game.sessionMetrics.averageSessionTime;
    }

    // Method 3: Estimate from play interactions (fallback)
    if (game.gameInteractions?.plays && game.gameInteractions.plays.length > 0) {
      console.log('Estimating from plays:', game.gameInteractions.plays.length);
      // Estimate average time based on conversion rate and play count
      const conversionRate = game.plays > 0 ? (game.conversions / game.plays) * 100 : 0;
      // Higher conversion rate suggests users spend more time
      const estimatedTime = Math.max(30, Math.min(300, 60 + conversionRate * 3));
      console.log('Estimated time from conversion rate:', estimatedTime);
      return Math.round(estimatedTime);
    }

    console.log('No session data found, returning 0');
    return 0;
  };

  const gameAverageSessionTime = calculateGameAverageSessionTime();

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate conversion rate
  const conversionRate = game.plays > 0 ? Math.round((game.conversions / game.plays) * 100) : 0;

  // Generate game URL
  const getGameUrl = () => {
    return `${window.location.origin}/play/${game.id}`;
  };

  // Handle game link copy
  const copyGameLink = () => {
    navigator.clipboard.writeText(getGameUrl());
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Handle Edit Game button - Navigate to Settings tab
  const handleEditGame = () => {
    setActiveTab('settings');
  };

  // Handle View Live button
  const handleViewLive = async () => {
    setIsViewingLive(true);
    try {
      const gameUrl = getGameUrl();
      window.open(gameUrl, '_blank');

      toast({
        title: 'بازی باز شد',
        description: 'بازی در تب جدید باز شد',
      });
    } catch (error) {
      toast({
        title: 'خطا',
        description: 'خطا در باز کردن بازی',
        variant: 'destructive',
      });
    } finally {
      setIsViewingLive(false);
    }
  };

  // Handle Full Analytics button - Navigate to Analytics tab
  const handleFullAnalytics = () => {
    setActiveTab('analytics');
  };

  // Handle Share button
  const handleShare = () => {
    setShowShareModal(true);
  };

  // Handle QR Code button
  const handleQRCode = async () => {
    setShowQRModal(true);
    setIsGeneratingQR(true);

    try {
      const gameUrl = getGameUrl();
      // Generate QR code using a free API service
      const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(gameUrl)}`;
      setQrCodeUrl(qrApiUrl);
    } catch (error) {
      toast({
        title: 'خطا',
        description: 'خطا در تولید کد QR',
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingQR(false);
    }
  };

  // Handle social media sharing
  const handleSocialShare = (platform: string) => {
    const gameUrl = getGameUrl();
    const text = `بازی ${game.name} را امتحان کنید!`;

    let shareUrl = '';

    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(
          gameUrl
        )}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(gameUrl)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(gameUrl)}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + gameUrl)}`;
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  // Handle copy link in share modal
  const handleCopyLink = () => {
    navigator.clipboard.writeText(getGameUrl());
    toast({
      title: 'کپی شد',
      description: 'لینک بازی کپی شد',
    });
  };

  // Update the useEffect to sync isActive state with game prop
  useEffect(() => {
    setIsActive(game.status === 'active');
  }, [game.status]);

  // Fix the toggleGameStatus function
  const toggleGameStatus = async () => {
    const newStatus = !isActive;
    const previousStatus = isActive;

    // Optimistically update the UI
    setIsActive(newStatus);

    try {
      // The game.id is already a string from the API
      const gameId = game.id;

      console.log(
        'Updating game status for ID:',
        gameId,
        'Type:',
        typeof gameId,
        'to status:',
        newStatus ? 'active' : 'inactive'
      );

      const response = await fetch(`/api/dashboard/games/${gameId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus ? 'active' : 'inactive' }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to update game status`);
      }

      const data = await response.json();
      console.log('Status update response:', data);

      // Show success message
      toast({
        title: 'موفقیت',
        description: `وضعیت بازی به ${newStatus ? 'فعال' : 'غیرفعال'} تغییر یافت`,
      });

      // Update the game data if onUpdate callback is provided
      if (onUpdate) {
        onUpdate({
          ...game,
          status: newStatus ? 'active' : 'inactive',
        });
      }
    } catch (error) {
      console.error('Error updating game status:', error);

      // Revert the state if the API call fails
      setIsActive(previousStatus);

      // Show error message with more details
      toast({
        title: 'خطا',
        description: `به‌روزرسانی وضعیت بازی با شکست مواجه شد: ${
          error instanceof Error ? error.message : 'خطای ناشناخته'
        }`,
        variant: 'destructive',
      });
    }
  };

  // Handle game deletion
  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/dashboard/games/${game.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete game');
      }

      toast({
        title: 'موفقیت',
        description: 'بازی با موفقیت حذف شد',
      });

      onClose(); // Close the modal after successful deletion
    } catch (error) {
      console.error('Error deleting game:', error);
      toast({
        title: 'خطا',
        description: 'حذف بازی با شکست مواجه شد',
        variant: 'destructive',
      });
    } finally {
      setShowConfirmDelete(false); // Reset confirmation state
    }
  };

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    if (!game.id) return;

    setIsLoadingAnalytics(true);

    // Create default analytics data
    const defaultAnalyticsData = {
      demographics: {
        mobileUsers: 82,
        desktopUsers: 18,
        newPlayers: 68,
        returningPlayers: 32,
      },
      timeMetrics: {
        avgTimeOnGame: '1m 12s',
        avgTimeToConvert: '45s',
        avgSessionTime: '2m 30s',
        sessionCompletionRate: 85,
        peakPlayHours: '6PM - 9PM',
        mostActiveDay: 'Saturday',
      },
      referralSources: {
        instagramStories: 72,
        instagramBio: 18,
        directLink: 7,
        other: 3,
      },
    };

    try {
      const response = await fetch(`/api/dashboard/games/${game.id}/analytics?period=${analyticsPeriod}`);

      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data);
      } else {
        // console.log$$[^)]*$$;
        setAnalyticsData(defaultAnalyticsData);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setAnalyticsData(defaultAnalyticsData);
    } finally {
      setIsLoadingAnalytics(false);
    }
  };

  // Handle analytics period change
  const handlePeriodChange = (period: string) => {
    setAnalyticsPeriod(period);
  };

  // Fetch recent activity for this game
  useEffect(() => {
    const fetchRecentActivity = async () => {
      if (!game.gameInteractions) {
        setIsLoading(false);
        return;
      }

      try {
        // Format the activity from gameInteractions
        const activity = [];

        // Add plays
        if (game.gameInteractions.plays && game.gameInteractions.plays.length > 0) {
          const recentPlays = game.gameInteractions.plays.slice(-3).map((play) => ({
            id: Math.random().toString(36).substring(2),
            type: 'play',
            timestamp: new Date(play.timestamp),
            phoneNumber: play.phoneNumber || 'Anonymous',
          }));
          activity.push(...recentPlays);
        }

        // Add conversions
        if (game.gameInteractions.conversions && game.gameInteractions.conversions.length > 0) {
          const recentConversions = game.gameInteractions.conversions.slice(-3).map((conversion) => ({
            id: Math.random().toString(36).substring(2),
            type: 'conversion',
            timestamp: new Date(conversion.timestamp),
            phoneNumber: conversion.phoneNumber,
            prize: conversion.prize,
          }));
          activity.push(...recentConversions);
        }

        // Sort by timestamp (most recent first)
        activity.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

        // Format for display
        const formattedActivity = activity.slice(0, 3).map((item) => ({
          id: item.id,
          type: item.type,
          time: formatTimeAgo(item.timestamp),
          phoneNumber: item.phoneNumber,
          prize: item.prize,
        }));

        setRecentActivity(formattedActivity);
      } catch (error) {
        console.error('Error processing game activity:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentActivity();
  }, [game]);

  // Fetch analytics data when tab changes or period changes
  useEffect(() => {
    if (activeTab === 'analytics') {
      fetchAnalyticsData();
    }
  }, [activeTab, analyticsPeriod, game.id]);

  useEffect(() => {
    // Initialize the editedGame state with the current game data
    setEditedGame({ ...game });
    setHasChanges(false);

    // Calculate initial total probability
    if (game.prizes) {
      const total = game.prizes.reduce((sum, prize) => sum + prize.probability, 0);
      setTotalProbability(total);
    }
  }, [game]);

  // Helper function to format time ago
  function formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      return `${diffSecs} ثانیه پیش`;
    } else if (diffMins < 60) {
      return `${diffMins} دقیقه پیش`;
    } else if (diffHours < 24) {
      return `${diffHours} ساعت پیش`;
    } else {
      return `${diffDays} روز پیش`;
    }
  }

  // Helper function to format percentages
  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  // Helper function to safely get demographic percentages
  const getDemographicPercentage = (value: number, total: number) => {
    if (!total) return '0%';
    return formatPercentage((value / total) * 100);
  };

  // Helper function to safely get referral percentages
  const getReferralPercentage = (value: number, total: number) => {
    if (!total) return '0%';
    return formatPercentage((value / total) * 100);
  };

  const handleFormChange = (field: string, value: any) => {
    if (!editedGame) return;

    setEditedGame((prev) => ({
      ...prev!,
      [field]: value,
    }));
    setHasChanges(true);
  };

  const handlePrizeChange = (index: number, field: string, value: any) => {
    if (!editedGame || !editedGame.prizes) return;

    const updatedPrizes = [...editedGame.prizes];
    updatedPrizes[index] = {
      ...updatedPrizes[index],
      [field]: value,
    };

    // If updating quantity, also update remainingQuantity if it's larger than the new quantity
    if (field === 'quantity') {
      if (updatedPrizes[index].remainingQuantity > value) {
        updatedPrizes[index].remainingQuantity = value;
      }
    }

    // Recalculate total probability
    const newTotal = updatedPrizes.reduce((sum, prize) => sum + prize.probability, 0);
    setTotalProbability(newTotal);

    setEditedGame((prev) => ({
      ...prev!,
      prizes: updatedPrizes,
    }));
    setHasChanges(true);
  };

  const addPrize = () => {
    if (!editedGame || !editedGame.prizes) return;
    if (editedGame.prizes.length >= 6) return; // Limit to 6 prizes

    const newPrize = {
      type: 'کد تخفیف',
      description: 'جایزه جدید',
      probability: 0,
      quantity: 100,
      remainingQuantity: 100,
    };

    setEditedGame((prev) => ({
      ...prev!,
      prizes: [...prev!.prizes, newPrize],
    }));
    setHasChanges(true);
  };

  const removePrize = (index: number) => {
    if (!editedGame || !editedGame.prizes) return;
    if (editedGame.prizes.length <= 1) return; // Keep at least one prize

    const updatedPrizes = editedGame.prizes.filter((_, i) => i !== index);

    // Recalculate total probability
    const newTotal = updatedPrizes.reduce((sum, prize) => sum + prize.probability, 0);
    setTotalProbability(newTotal);

    setEditedGame((prev) => ({
      ...prev!,
      prizes: updatedPrizes,
    }));
    setHasChanges(true);
  };

  const distributeRemainingProbability = () => {
    if (!editedGame || !editedGame.prizes) return;
    if (totalProbability === 100) return; // Already at 100%

    const remaining = 100 - totalProbability;
    const updatedPrizes = [...editedGame.prizes];

    // Count prizes with non-zero probability
    const prizesWithProbability = updatedPrizes.filter((prize) => prize.probability > 0).length;
    const prizesWithoutProbability = updatedPrizes.length - prizesWithProbability;

    // Determine which prizes to distribute to
    const targetPrizes =
      prizesWithoutProbability > 0
        ? updatedPrizes.filter((prize) => prize.probability === 0) // Distribute to prizes with 0 probability
        : updatedPrizes; // Distribute to all prizes

    const distributionPerPrize = remaining / targetPrizes.length;

    // Update probabilities
    const finalPrizes = updatedPrizes.map((prize) => {
      if (targetPrizes.includes(prize)) {
        return {
          ...prize,
          probability: Math.round((prize.probability + distributionPerPrize) * 100) / 100,
        };
      }
      return prize;
    });

    // Ensure total is exactly 100%
    const newTotal = finalPrizes.reduce((sum, prize) => sum + prize.probability, 0);
    if (newTotal !== 100 && finalPrizes.length > 0) {
      const diff = 100 - newTotal;
      finalPrizes[0].probability = Math.round((finalPrizes[0].probability + diff) * 100) / 100;
    }

    setTotalProbability(100);
    setEditedGame((prev) => ({
      ...prev!,
      prizes: finalPrizes,
    }));
    setHasChanges(true);
  };

  const saveChanges = async () => {
    if (!editedGame || !hasChanges) return;

    setIsUpdating(true);

    try {
      const response = await fetch(`/api/dashboard/games/${game.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editedGame.name,
          prizes: editedGame.prizes,
          colorScheme: editedGame.colorScheme,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update game');
      }

      const updatedGame = await response.json();

      // Update the game in the parent component
      toast({
        title: 'موفقیت',
        description: 'بازی با موفقیت به‌روزرسانی شد',
      });

      // Close the modal and refresh data
      onClose();
    } catch (error) {
      console.error('Error updating game:', error);
      toast({
        title: 'خطا',
        description: 'به‌روزرسانی بازی با شکست مواجه شد',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
      >
        {/* Modal Header */}
        <div
          className="p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10"
          style={{ borderColor: 'var(--border)' }}
        >
          <div className="flex items-center gap-3">
            <div
              className="w-10 h-10 rounded-full flex items-center justify-center"
              style={{
                backgroundColor:
                  game.type === 'wheel'
                    ? 'rgba(139, 92, 246, 0.2)'
                    : game.type === 'lever'
                    ? 'rgba(249, 115, 22, 0.2)'
                    : 'rgba(132, 204, 22, 0.2)',
              }}
            >
              {game.type === 'wheel' ? (
                <div className="h-5 w-5 rounded-full border-2 border-purple-500 border-t-transparent animate-spin-slow" />
              ) : game.type === 'lever' ? (
                <div className="h-5 w-1.5 bg-orange-500" />
              ) : (
                <div className="h-4 w-4 bg-green-500 rounded-sm" />
              )}
            </div>
            <div>
              <h3 className="font-semibold text-lg" style={{ color: 'var(--text-primary)' }}>
                {game.name}
              </h3>
              <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                بازی {game.type === 'wheel' ? 'چرخ شانس' : game.type === 'lever' ? 'اهرم شانس' : 'جعبه هدیه'} • ایجاد
                شده در {formatDate(game.createdAt)}
              </p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Tabs Navigation */}
        <div className="border-b" style={{ borderColor: 'var(--border)' }}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="px-4">
              <TabsList className="grid grid-cols-3 w-full max-w-md">
                <TabsTrigger value="overview">نمای کلی</TabsTrigger>
                <TabsTrigger value="analytics">تحلیل‌ها</TabsTrigger>
                <TabsTrigger value="settings">تنظیمات</TabsTrigger>
              </TabsList>
            </div>
          </Tabs>
        </div>

        {/* Modal Content - Scrollable */}
        <div className="flex-1 overflow-y-auto">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="p-4 md:p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Game Stats */}
                <div>
                  <h4 className="font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                    نمای کلی عملکرد
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div
                      className="p-4 rounded-lg border"
                      style={{
                        borderColor: 'var(--border)',
                        backgroundColor: 'var(--card-bg)',
                      }}
                    >
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        کل بازی‌ها
                      </p>
                      <p className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                        {game.plays.toLocaleString('fa-IR')}
                      </p>
                    </div>
                    <div
                      className="p-4 rounded-lg border"
                      style={{
                        borderColor: 'var(--border)',
                        backgroundColor: 'var(--card-bg)',
                      }}
                    >
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        تبدیل‌ها
                      </p>
                      <p className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                        {game.conversions.toLocaleString('fa-IR')}
                      </p>
                    </div>
                    <div
                      className="p-4 rounded-lg border"
                      style={{
                        borderColor: 'var(--border)',
                        backgroundColor: 'var(--card-bg)',
                      }}
                    >
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        نرخ تبدیل
                      </p>
                      <p className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                        {conversionRate}%
                      </p>
                    </div>
                    <div
                      className="p-4 rounded-lg border"
                      style={{
                        borderColor: 'var(--border)',
                        backgroundColor: 'var(--card-bg)',
                      }}
                    >
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        میانگین زمان جلسه
                      </p>
                      <p className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                        {gameAverageSessionTime > 0 ? formatSessionTimePersian(gameAverageSessionTime) : '0 ثانیه'}
                      </p>
                    </div>
                  </div>

                  {/* Game Link */}
                  <div className="mt-6">
                    <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                      لینک بازی
                    </h4>
                    <div className="flex">
                      <input
                        type="text"
                        value={game.gameLink || `https://instagameify.com/play/${game.id}`}
                        readOnly
                        className="flex-1 px-3 py-2 border rounded-l-md text-sm"
                        style={{
                          borderColor: 'var(--border)',
                          backgroundColor: 'var(--background)',
                          color: 'var(--text-primary)',
                        }}
                      />
                      <Button
                        onClick={copyGameLink}
                        className="rounded-l-none"
                        style={{
                          backgroundColor: copied ? 'var(--secondary)' : 'var(--primary)',
                          color: 'var(--button-text)',
                        }}
                      >
                        {copied ? (
                          <>
                            <Check className="h-4 w-4 ml-1" />
                            کپی شد
                          </>
                        ) : (
                          <>
                            <Copy className="h-4 w-4 ml-1" />
                            کپی
                          </>
                        )}
                      </Button>
                    </div>
                    <div className="flex gap-2 mt-2">
                      <Button variant="outline" size="sm" className="text-xs h-8 bg-transparent" onClick={handleQRCode}>
                        <QrCode className="h-3.5 w-3.5 ml-1" />
                        کد QR
                      </Button>
                      <Button variant="outline" size="sm" className="text-xs h-8 bg-transparent" onClick={handleShare}>
                        <Share2 className="h-3.5 w-3.5 ml-1" />
                        اشتراک‌گذاری
                      </Button>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div className="mt-6">
                    <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                      فعالیت‌های اخیر
                    </h4>
                    <div className="border rounded-md overflow-hidden" style={{ borderColor: 'var(--border)' }}>
                      {isLoading ? (
                        <div className="p-4 text-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                          <p className="text-sm mt-2">در حال بارگذاری فعالیت‌ها...</p>
                        </div>
                      ) : recentActivity.length > 0 ? (
                        recentActivity.map((activity, index) => (
                          <div
                            key={activity.id}
                            className="p-3 border-b last:border-b-0 flex items-center justify-between"
                            style={{ borderColor: 'var(--border)' }}
                          >
                            <div>
                              <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                {activity.type === 'play' ? 'بازی انجام شد' : 'تبدیل'}
                              </p>
                              <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                                {activity.time}
                              </p>
                            </div>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                activity.type === 'conversion'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}
                            >
                              {activity.type === 'conversion' ? 'تبدیل شد' : 'بازی کرد'}
                            </span>
                          </div>
                        ))
                      ) : (
                        <div className="p-4 text-center">
                          <p className="text-sm">فعالیت اخیری وجود ندارد</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Game Preview */}
                <div>
                  <h4 className="font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                    پیش‌نمایش بازی
                  </h4>
                  <div
                    className="border rounded-lg overflow-hidden h-80 flex items-center justify-center"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--card-bg)',
                    }}
                  >
                    <GamePreview gameType={game.type} gameId={game.id} />
                  </div>

                  {/* Quick Actions */}
                  <div className="mt-6 flex flex-wrap gap-2">
                    <Button
                      className="flex-1"
                      style={{
                        backgroundColor: 'var(--primary)',
                        color: 'var(--button-text)',
                      }}
                      onClick={handleEditGame}
                    >
                      <Edit className="h-4 w-4 ml-2" />
                      ویرایش بازی
                    </Button>
                    <Button
                      variant="outline"
                      className="flex-1 bg-transparent"
                      onClick={handleViewLive}
                      disabled={isViewingLive}
                    >
                      {isViewingLive ? (
                        <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                      ) : (
                        <Eye className="h-4 w-4 ml-2" />
                      )}
                      مشاهده زنده
                    </Button>
                    <Button variant="outline" className="flex-1 bg-transparent" onClick={handleFullAnalytics}>
                      <BarChart3 className="h-4 w-4 ml-2" />
                      تحلیل‌های کامل
                    </Button>
                  </div>

                  {/* Game Status */}
                  <div
                    className="mt-6 p-4 rounded-lg border"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--card-bg)',
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h5 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                          وضعیت بازی
                        </h5>
                        <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
                          {isActive ? 'بازی شما فعال است و بازی‌ها را می‌پذیرد' : 'بازی شما در حال حاضر متوقف است'}
                        </p>
                      </div>
                      <div className="flex items-center gap-3 mr-4">
                        <div className="flex flex-col items-center gap-1">
                          <Switch
                            id="game-status"
                            checked={isActive}
                            onCheckedChange={toggleGameStatus}
                            className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                          />
                          <Label
                            htmlFor="game-status"
                            className={`text-xs font-medium ${isActive ? 'text-green-600' : 'text-gray-500'}`}
                          >
                            {isActive ? 'فعال' : 'غیرفعال'}
                          </Label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Analytics Tab */}
          {activeTab === 'analytics' && (
            <div className="p-4 md:p-6">
              <div className="grid grid-cols-1 gap-6">
                {/* Time Period Selector */}
                <div className="flex justify-end">
                  <div className="flex border rounded-md" style={{ borderColor: 'var(--border)' }}>
                    <Button
                      variant={analyticsPeriod === '7days' ? 'default' : 'ghost'}
                      size="sm"
                      className="text-xs"
                      onClick={() => handlePeriodChange('7days')}
                    >
                      ۷ روز گذشته
                    </Button>
                    <Button
                      variant={analyticsPeriod === '30days' ? 'default' : 'ghost'}
                      size="sm"
                      className="text-xs"
                      onClick={() => handlePeriodChange('30days')}
                    >
                      ۳۰ روز گذشته
                    </Button>
                  </div>
                </div>

                {/* Performance Chart */}
                <div>
                  <h4 className="font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                    عملکرد در طول زمان
                  </h4>
                  <div
                    className="border rounded-lg p-4"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--card-bg)',
                    }}
                  >
                    <div className="h-80">
                      <GamePerformanceChart gameId={game.id} period={analyticsPeriod} />
                    </div>
                  </div>
                </div>

                {/* Prize Distribution */}
                <div>
                  <h4 className="font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                    توزیع جوایز
                  </h4>
                  <div
                    className="border rounded-lg p-4"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--card-bg)',
                    }}
                  >
                    <div className="h-64">
                      <GamePrizeDistribution prizes={game.prizes || []} gameId={game.id} />
                    </div>
                  </div>
                </div>

                {/* Detailed Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {isLoadingAnalytics ? (
                    <div className="col-span-3 flex justify-center items-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : (
                    <>
                      <div
                        className="border rounded-lg p-4"
                        style={{
                          borderColor: 'var(--border)',
                          backgroundColor: 'var(--card-bg)',
                        }}
                      >
                        <h5 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                          جمعیت‌شناسی کاربران
                        </h5>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              کاربران موبایل
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.demographics
                                ? getDemographicPercentage(
                                    analyticsData.demographics.mobileUsers,
                                    analyticsData.demographics.mobileUsers + analyticsData.demographics.desktopUsers
                                  )
                                : '82%'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Desktop Users
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.demographics
                                ? getDemographicPercentage(
                                    analyticsData.demographics.desktopUsers,
                                    analyticsData.demographics.mobileUsers + analyticsData.demographics.desktopUsers
                                  )
                                : '18%'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              New Players
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.demographics
                                ? getDemographicPercentage(
                                    analyticsData.demographics.newPlayers,
                                    analyticsData.demographics.newPlayers + analyticsData.demographics.returningPlayers
                                  )
                                : '68%'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Returning Players
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.demographics
                                ? getDemographicPercentage(
                                    analyticsData.demographics.returningPlayers,
                                    analyticsData.demographics.newPlayers + analyticsData.demographics.returningPlayers
                                  )
                                : '32%'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div
                        className="border rounded-lg p-4"
                        style={{
                          borderColor: 'var(--border)',
                          backgroundColor: 'var(--card-bg)',
                        }}
                      >
                        <h5 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                          معیارهای زمانی
                        </h5>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Avg. Time on Game
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.timeMetrics?.avgTimeOnGame || '1m 12s'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Avg. Time to Convert
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.timeMetrics?.avgTimeToConvert || '45s'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              میانگین زمان جلسه
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {gameAverageSessionTime > 0
                                ? formatSessionTimePersian(gameAverageSessionTime)
                                : '0 ثانیه'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Peak Play Hours
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.timeMetrics?.peakPlayHours || '6PM - 9PM'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Most Active Day
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.timeMetrics?.mostActiveDay || 'Saturday'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div
                        className="border rounded-lg p-4"
                        style={{
                          borderColor: 'var(--border)',
                          backgroundColor: 'var(--card-bg)',
                        }}
                      >
                        <h5 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                          منابع ارجاع
                        </h5>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Instagram Stories
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.referralSources
                                ? getReferralPercentage(
                                    analyticsData.referralSources.instagramStories,
                                    analyticsData.referralSources.instagramStories +
                                      analyticsData.referralSources.instagramBio +
                                      analyticsData.referralSources.directLink +
                                      analyticsData.referralSources.other
                                  )
                                : '72%'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Instagram Bio
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.referralSources
                                ? getReferralPercentage(
                                    analyticsData.referralSources.instagramBio,
                                    analyticsData.referralSources.instagramStories +
                                      analyticsData.referralSources.instagramBio +
                                      analyticsData.referralSources.directLink +
                                      analyticsData.referralSources.other
                                  )
                                : '18%'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Direct Link
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.referralSources
                                ? getReferralPercentage(
                                    analyticsData.referralSources.directLink,
                                    analyticsData.referralSources.instagramStories +
                                      analyticsData.referralSources.instagramBio +
                                      analyticsData.referralSources.directLink +
                                      analyticsData.referralSources.other
                                  )
                                : '7%'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Other
                            </span>
                            <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {analyticsData?.referralSources
                                ? getReferralPercentage(
                                    analyticsData.referralSources.other,
                                    analyticsData.referralSources.instagramStories +
                                      analyticsData.referralSources.instagramBio +
                                      analyticsData.referralSources.directLink +
                                      analyticsData.referralSources.other
                                  )
                                : '3%'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>

                {/* Export Options */}
                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 ml-2" />
                    خروجی CSV
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 ml-2" />
                    خروجی PDF
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="p-4 md:p-6">
              <div className="grid grid-cols-1 gap-6">
                {/* Basic Settings */}
                <div>
                  <h4 className="font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                    تنظیمات پایه
                  </h4>
                  <div
                    className="border rounded-lg p-4"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--card-bg)',
                    }}
                  >
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                          نام بازی
                        </label>
                        <input
                          type="text"
                          value={editedGame?.name || game.name}
                          onChange={(e) => handleFormChange('name', e.target.value)}
                          className="w-full px-3 py-2 border rounded-md"
                          style={{
                            borderColor: 'var(--border)',
                            backgroundColor: 'var(--background)',
                            color: 'var(--text-primary)',
                          }}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
                          وضعیت بازی
                        </label>
                        <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                          <div>
                            <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                              {isActive ? 'فعال' : 'غیرفعال'}
                            </p>
                            <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                              {isActive ? 'بازی در حال اجرا است' : 'بازی متوقف است'}
                            </p>
                          </div>
                          <Switch
                            id="settings-status"
                            checked={isActive}
                            onCheckedChange={toggleGameStatus}
                            className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Color Scheme */}
                <div>
                  <h4 className="font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                    طرح رنگی
                  </h4>
                  <div
                    className="border rounded-lg p-4"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--card-bg)',
                    }}
                  >
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                      {[
                        {
                          id: 'purple',
                          name: 'بنفش',
                          primary: '#8b5cf6',
                          secondary: '#F9A8D4',
                        },
                        {
                          id: 'blue',
                          name: 'آبی',
                          primary: '#3b82f6',
                          secondary: '#93c5fd',
                        },
                        {
                          id: 'green',
                          name: 'سبز',
                          primary: '#84cc16',
                          secondary: '#bef264',
                        },
                        {
                          id: 'orange',
                          name: 'نارنجی',
                          primary: '#f97316',
                          secondary: '#fdba74',
                        },
                      ].map((scheme) => (
                        <div
                          key={scheme.id}
                          className={`cursor-pointer rounded-md p-3 border-2 transition-all ${
                            (editedGame?.colorScheme || game.colorScheme) === scheme.id
                              ? 'border-opacity-100 border-4'
                              : 'border-opacity-30'
                          }`}
                          style={{
                            borderColor: scheme.primary,
                            backgroundColor: `${scheme.primary}10`,
                          }}
                          onClick={() => handleFormChange('colorScheme', scheme.id)}
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className="w-6 h-6 rounded-full"
                              style={{
                                background: `linear-gradient(to right, ${scheme.primary}, ${scheme.secondary})`,
                              }}
                            />
                            <span style={{ color: 'var(--text-primary)' }}>{scheme.name}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Prizes */}
                <div>
                  <div className="flex justify-between items-center mb-1.5">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                        جوایز
                      </h4>
                      <button type="button" className="text-gray-500 hover:text-gray-700" onClick={() => {}}>
                        <Info className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className="text-xs"
                        style={{
                          color: totalProbability === 100 ? 'green' : 'red',
                        }}
                      >
                        مجموع احتمال: {totalProbability}% {totalProbability === 100 ? '✓' : '(باید ۱۰۰% باشد)'}
                      </div>
                      {totalProbability !== 100 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="text-xs py-1 h-auto bg-transparent"
                          onClick={distributeRemainingProbability}
                        >
                          تصحیح خودکار
                        </Button>
                      )}
                    </div>
                  </div>

                  <div
                    className="border rounded-lg p-4"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--card-bg)',
                    }}
                  >
                    <div className="space-y-3 mb-3">
                      {editedGame?.prizes?.map((prize, index) => (
                        <div
                          key={index}
                          className="flex flex-col sm:flex-row gap-3 p-3 rounded-md"
                          style={{ backgroundColor: 'var(--background)' }}
                        >
                          <div className="flex-1">
                            <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                              نوع جایزه
                            </label>
                            <select
                              value={prize.type}
                              onChange={(e) => handlePrizeChange(index, 'type', e.target.value)}
                              className="w-full px-2 py-1.5 border rounded-md text-sm"
                              style={{
                                backgroundColor: 'var(--background)',
                                color: 'var(--text-primary)',
                                borderColor: 'var(--border)',
                              }}
                            >
                              {['کد تخفیف', 'کالای رایگان', 'ارسال رایگان', 'پیشنهاد ویژه', 'پیام تشکر'].map((type) => (
                                <option key={type} value={type}>
                                  {type}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div className="flex-1">
                            <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                              توضیحات
                            </label>
                            <input
                              type="text"
                              value={prize.description}
                              onChange={(e) => handlePrizeChange(index, 'description', e.target.value)}
                              className="w-full px-2 py-1.5 border rounded-md text-sm"
                              style={{
                                backgroundColor: 'var(--background)',
                                color: 'var(--text-primary)',
                                borderColor: 'var(--border)',
                              }}
                              placeholder="مثال: ۱۰% تخفیف"
                            />
                          </div>

                          {/* Quantity Field */}
                          <div className="flex-1">
                            <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                              تعداد
                            </label>
                            <div className="flex items-center">
                              <button
                                type="button"
                                onClick={() => handlePrizeChange(index, 'quantity', Math.max(1, prize.quantity - 10))}
                                className="px-2 py-1.5 border rounded-l-md"
                                style={{ borderColor: 'var(--border)' }}
                              >
                                <Minus className="h-3 w-3" />
                              </button>
                              <input
                                type="number"
                                min="1"
                                value={prize.quantity}
                                onChange={(e) =>
                                  handlePrizeChange(index, 'quantity', Number.parseInt(e.target.value) || 1)
                                }
                                className="w-full px-2 py-1.5 border-y text-center text-sm"
                                style={{
                                  backgroundColor: 'var(--background)',
                                  color: 'var(--text-primary)',
                                  borderColor: 'var(--border)',
                                }}
                              />
                              <button
                                type="button"
                                onClick={() => handlePrizeChange(index, 'quantity', prize.quantity + 10)}
                                className="px-2 py-1.5 border rounded-r-md"
                                style={{ borderColor: 'var(--border)' }}
                              >
                                <Plus className="h-3 w-3" />
                              </button>
                            </div>
                            <div className="text-xs mt-1 flex items-center" style={{ color: 'var(--text-secondary)' }}>
                              <Package className="h-3 w-3 ml-1" />
                              <span>
                                باقی‌مانده: {prize.remainingQuantity} / {prize.quantity}
                              </span>
                            </div>
                          </div>

                          {/* Probability Field */}
                          <div className="w-full sm:w-32">
                            <label className="text-xs mb-1 block" style={{ color: 'var(--text-secondary)' }}>
                              احتمال برد %
                            </label>
                            <div className="flex items-center">
                              <button
                                type="button"
                                onClick={() =>
                                  handlePrizeChange(index, 'probability', Math.max(0, prize.probability - 5))
                                }
                                className="px-2 py-1.5 border rounded-l-md"
                                style={{ borderColor: 'var(--border)' }}
                              >
                                <Minus className="h-3 w-3" />
                              </button>
                              <input
                                type="number"
                                min="0"
                                max="100"
                                value={prize.probability}
                                onChange={(e) =>
                                  handlePrizeChange(index, 'probability', Number.parseInt(e.target.value) || 0)
                                }
                                className="w-full px-2 py-1.5 border-y text-center text-sm"
                                style={{
                                  backgroundColor: 'var(--background)',
                                  color: 'var(--text-primary)',
                                  borderColor: 'var(--border)',
                                }}
                              />
                              <button
                                type="button"
                                onClick={() =>
                                  handlePrizeChange(index, 'probability', Math.min(100, prize.probability + 5))
                                }
                                className="px-2 py-1.5 border rounded-r-md"
                                style={{ borderColor: 'var(--border)' }}
                              >
                                <Plus className="h-3 w-3" />
                              </button>
                            </div>
                          </div>

                          <button
                            type="button"
                            onClick={() => removePrize(index)}
                            className="self-end sm:self-center px-2 py-1.5 text-red-500 rounded-md hover:bg-red-50"
                            disabled={(editedGame?.prizes?.length || 0) <= 1}
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={addPrize}
                      className="w-full flex items-center justify-center gap-2 bg-transparent"
                      disabled={(editedGame?.prizes?.length || 0) >= 6}
                    >
                      <Plus className="h-4 w-4" />
                      افزودن جایزه
                    </Button>
                  </div>
                </div>

                {/* Danger Zone */}
                <div>
                  <h4 className="font-medium mb-4 text-red-500">منطقه خطر</h4>
                  <div className="border border-red-200 rounded-lg p-4 bg-red-50">
                    <div className="space-y-4">
                      <div>
                        <h5 className="font-medium text-red-700">حذف بازی</h5>
                        <p className="text-sm text-red-600 mb-2">
                          پس از حذف بازی، امکان بازگردانی وجود ندارد. لطفاً مطمئن باشید.
                        </p>
                        {showConfirmDelete ? (
                          <div className="flex gap-2">
                            <Button variant="destructive" size="sm" onClick={handleDelete}>
                              بله، بازی را حذف کن
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => setShowConfirmDelete(false)}>
                              لغو
                            </Button>
                          </div>
                        ) : (
                          <Button variant="destructive" size="sm" onClick={() => setShowConfirmDelete(true)}>
                            حذف بازی
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div
          className="p-4 border-t flex justify-between items-center sticky bottom-0 bg-white z-10"
          style={{ borderColor: 'var(--border)' }}
        >
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isActive ? 'bg-green-500' : 'bg-gray-400'}`}></div>
            <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
              {isActive ? 'فعال' : 'غیرفعال'}
            </span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              بستن
            </Button>
            <Button
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--button-text)',
              }}
              disabled={!hasChanges || isUpdating}
              onClick={saveChanges}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                  در حال ذخیره...
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 ml-2" />
                  ذخیره تغییرات
                </>
              )}
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Share Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="bg-white rounded-lg p-6 w-full max-w-md mx-4"
            style={{ backgroundColor: 'var(--card-bg)' }}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
                اشتراک‌گذاری بازی
              </h3>
              <Button variant="ghost" size="sm" onClick={() => setShowShareModal(false)} className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Social Media Buttons */}
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  className="flex items-center justify-center gap-2 h-12"
                  onClick={() => handleSocialShare('twitter')}
                >
                  <Twitter className="h-5 w-5 text-blue-400" />
                  Twitter
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center justify-center gap-2 h-12"
                  onClick={() => handleSocialShare('facebook')}
                >
                  <Facebook className="h-5 w-5 text-blue-600" />
                  Facebook
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center justify-center gap-2 h-12"
                  onClick={() => handleSocialShare('linkedin')}
                >
                  <Linkedin className="h-5 w-5 text-blue-700" />
                  LinkedIn
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center justify-center gap-2 h-12"
                  onClick={() => handleSocialShare('whatsapp')}
                >
                  <MessageCircle className="h-5 w-5 text-green-600" />
                  WhatsApp
                </Button>
              </div>

              {/* Copy Link */}
              <div className="border-t pt-4">
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={getGameUrl()}
                    readOnly
                    className="flex-1 px-3 py-2 border rounded-md text-sm"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--input-bg)',
                      color: 'var(--text-primary)',
                    }}
                  />
                  <Button onClick={handleCopyLink} size="sm">
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* QR Code Modal */}
      {showQRModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="bg-white rounded-lg p-6 w-full max-w-md mx-4"
            style={{ backgroundColor: 'var(--card-bg)' }}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
                کد QR بازی
              </h3>
              <Button variant="ghost" size="sm" onClick={() => setShowQRModal(false)} className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-center space-y-4">
              {isGeneratingQR ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <>
                  <div className="flex justify-center">
                    <img
                      src={qrCodeUrl}
                      alt="QR Code"
                      className="border rounded-lg"
                      style={{ borderColor: 'var(--border)' }}
                    />
                  </div>
                  <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                    کد QR را اسکن کنید تا بازی باز شود
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button
                      variant="outline"
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = qrCodeUrl;
                        link.download = `qr-code-${game.name}.png`;
                        link.click();
                      }}
                    >
                      <Download className="h-4 w-4 ml-2" />
                      دانلود
                    </Button>
                    <Button onClick={handleCopyLink}>
                      <Copy className="h-4 w-4 ml-2" />
                      کپی لینک
                    </Button>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
