'use client';

import type React from 'react';

import { motion } from 'framer-motion';
import { ArrowDown, ArrowUp, Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface StatsCardProps {
  title: string;
  value: string;
  icon: React.ElementType;
  trend: number;
  color: string;
  tooltip?: string;
}

export default function StatsCard({ title, value, icon: Icon, trend, color, tooltip }: StatsCardProps) {
  return (
    <TooltipProvider>
      <motion.div
        whileHover={{ y: -5 }}
        className="rounded-lg border p-4 shadow-sm"
        style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
      >
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                {title}
              </p>
              {tooltip && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3.5 w-3.5 cursor-help" style={{ color: 'var(--text-secondary)' }} />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs text-sm">{tooltip}</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <p className="text-2xl font-bold mt-1" style={{ color: 'var(--text-primary)' }}>
              {value}
            </p>
          </div>
          <div
            className="w-10 h-10 rounded-full flex items-center justify-center"
            style={{ backgroundColor: `${color}20` }}
          >
            <Icon className="h-5 w-5" style={{ color: color }} />
          </div>
        </div>

        <div className="mt-4 flex items-center">
          {trend > 0 ? (
            <div className="flex items-center text-green-500 text-xs">
              <ArrowUp className="h-3 w-3 ml-1" />
              <span>{trend}%</span>
            </div>
          ) : trend < 0 ? (
            <div className="flex items-center text-red-500 text-xs">
              <ArrowDown className="h-3 w-3 ml-1" />
              <span>{Math.abs(trend)}%</span>
            </div>
          ) : (
            <div className="flex items-center text-gray-500 text-xs">
              <span>بدون تغییر</span>
            </div>
          )}
          <span className="text-xs mr-2" style={{ color: 'var(--text-secondary)' }}>
            نسبت به دوره قبل
          </span>
        </div>
      </motion.div>
    </TooltipProvider>
  );
}
