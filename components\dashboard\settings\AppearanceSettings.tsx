"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, Loader2, Monitor, Moon, Sun } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"

export default function AppearanceSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [theme, setTheme] = useState("light")
  const [language, setLanguage] = useState("english")
  const [colorScheme, setColorScheme] = useState("purple")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Appearance settings updated",
        description: "Your appearance preferences have been saved successfully.",
      })
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-1" style={{ color: "var(--text-primary)" }}>
          Appearance Settings
        </h2>
        <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
          Customize how the dashboard looks and feels
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Theme Selection */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Theme
          </h3>
          <Separator />

          <RadioGroup value={theme} onValueChange={setTheme} className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <RadioGroupItem value="light" id="light" className="sr-only" />
              <Label
                htmlFor="light"
                className={`flex flex-col items-center gap-2 rounded-md border-2 p-4 hover:bg-accent cursor-pointer ${
                  theme === "light" ? "border-primary" : "border-muted"
                }`}
              >
                <Sun className="h-6 w-6" />
                <span>Light</span>
                {theme === "light" && (
                  <div className="absolute top-2 right-2">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                )}
              </Label>
            </div>

            <div>
              <RadioGroupItem value="dark" id="dark" className="sr-only" />
              <Label
                htmlFor="dark"
                className={`flex flex-col items-center gap-2 rounded-md border-2 p-4 hover:bg-accent cursor-pointer ${
                  theme === "dark" ? "border-primary" : "border-muted"
                }`}
              >
                <Moon className="h-6 w-6" />
                <span>Dark</span>
                {theme === "dark" && (
                  <div className="absolute top-2 right-2">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                )}
              </Label>
            </div>

            <div>
              <RadioGroupItem value="system" id="system" className="sr-only" />
              <Label
                htmlFor="system"
                className={`flex flex-col items-center gap-2 rounded-md border-2 p-4 hover:bg-accent cursor-pointer ${
                  theme === "system" ? "border-primary" : "border-muted"
                }`}
              >
                <Monitor className="h-6 w-6" />
                <span>System</span>
                {theme === "system" && (
                  <div className="absolute top-2 right-2">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                )}
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* Color Scheme */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Color Scheme
          </h3>
          <Separator />

          <RadioGroup
            value={colorScheme}
            onValueChange={setColorScheme}
            className="grid grid-cols-2 md:grid-cols-4 gap-4"
          >
            <div>
              <RadioGroupItem value="purple" id="purple" className="sr-only" />
              <Label
                htmlFor="purple"
                className={`flex flex-col items-center gap-2 rounded-md border-2 p-4 hover:bg-accent cursor-pointer ${
                  colorScheme === "purple" ? "border-primary" : "border-muted"
                }`}
              >
                <div className="h-8 w-8 rounded-full bg-[#8b5cf6]"></div>
                <span>Purple</span>
                {colorScheme === "purple" && (
                  <div className="absolute top-2 right-2">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                )}
              </Label>
            </div>

            <div>
              <RadioGroupItem value="blue" id="blue" className="sr-only" />
              <Label
                htmlFor="blue"
                className={`flex flex-col items-center gap-2 rounded-md border-2 p-4 hover:bg-accent cursor-pointer ${
                  colorScheme === "blue" ? "border-primary" : "border-muted"
                }`}
              >
                <div className="h-8 w-8 rounded-full bg-[#3b82f6]"></div>
                <span>Blue</span>
                {colorScheme === "blue" && (
                  <div className="absolute top-2 right-2">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                )}
              </Label>
            </div>

            <div>
              <RadioGroupItem value="green" id="green" className="sr-only" />
              <Label
                htmlFor="green"
                className={`flex flex-col items-center gap-2 rounded-md border-2 p-4 hover:bg-accent cursor-pointer ${
                  colorScheme === "green" ? "border-primary" : "border-muted"
                }`}
              >
                <div className="h-8 w-8 rounded-full bg-[#10b981]"></div>
                <span>Green</span>
                {colorScheme === "green" && (
                  <div className="absolute top-2 right-2">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                )}
              </Label>
            </div>

            <div>
              <RadioGroupItem value="orange" id="orange" className="sr-only" />
              <Label
                htmlFor="orange"
                className={`flex flex-col items-center gap-2 rounded-md border-2 p-4 hover:bg-accent cursor-pointer ${
                  colorScheme === "orange" ? "border-primary" : "border-muted"
                }`}
              >
                <div className="h-8 w-8 rounded-full bg-[#f97316]"></div>
                <span>Orange</span>
                {colorScheme === "orange" && (
                  <div className="absolute top-2 right-2">
                    <Check className="h-4 w-4 text-primary" />
                  </div>
                )}
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* Language */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Language
          </h3>
          <Separator />

          <div className="max-w-xs">
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="english">English</SelectItem>
                <SelectItem value="spanish">Spanish</SelectItem>
                <SelectItem value="french">French</SelectItem>
                <SelectItem value="german">German</SelectItem>
                <SelectItem value="japanese">Japanese</SelectItem>
                <SelectItem value="chinese">Chinese</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" type="button">
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading} style={{ backgroundColor: "var(--primary)" }}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
