import { NextResponse } from 'next/server';
import { zarinPalService } from '@/lib/zarinpal-service';

export async function GET() {
  try {
    // Test ZarinPal configuration
    const config = zarinPalService.getConfig();
    
    return NextResponse.json({
      success: true,
      message: 'ZarinPal service is configured correctly',
      config: {
        isSandbox: config.isSandbox,
        callbackUrl: config.callbackUrl,
        endpoints: config.endpoints,
      },
    });
  } catch (error) {
    console.error('ZarinPal test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'ZarinPal configuration test failed',
    }, { status: 500 });
  }
}

export async function POST() {
  try {
    // Test payment request
    const testPayment = await zarinPalService.requestPayment(
      1000, // 1000 Tomans
      'تست پرداخت زرین‌پال',
      {
        mobile: '09123456789',
        email: '<EMAIL>',
        order_id: 'test_' + Date.now(),
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Test payment request created successfully',
      authority: testPayment.authority,
      paymentUrl: testPayment.paymentUrl,
    });
  } catch (error) {
    console.error('ZarinPal payment test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Test payment request failed',
    }, { status: 500 });
  }
}
