import type { Game } from "@/types/game"

// Mock data for games
export const mockGames: Game[] = [
  {
    id: "1",
    name: "Summer Sale Spin & Win",
    type: "wheel",
    plays: 1243,
    conversions: 89,
    createdAt: "2023-06-15",
    lastActive: "2023-07-01",
    status: "active",
  },
  {
    id: "2",
    name: "New Collection Lucky Draw",
    type: "envelope",
    plays: 876,
    conversions: 52,
    createdAt: "2023-05-20",
    lastActive: "2023-06-28",
    status: "active",
  },
  {
    id: "3",
    name: "Holiday Special",
    type: "lever",
    plays: 2145,
    conversions: 134,
    createdAt: "2023-04-10",
    lastActive: "2023-06-15",
    status: "inactive",
  },
  {
    id: "4",
    name: "Flash Sale Giveaway",
    type: "wheel",
    plays: 567,
    conversions: 43,
    createdAt: "2023-07-05",
    lastActive: "2023-07-10",
    status: "active",
  },
  {
    id: "5",
    name: "Weekend Special",
    type: "envelope",
    plays: 321,
    conversions: 28,
    createdAt: "2023-07-01",
    lastActive: "2023-07-03",
    status: "inactive",
  },
  {
    id: "6",
    name: "<PERSON><PERSON><PERSON> Rewards",
    type: "lever",
    plays: 789,
    conversions: 67,
    createdAt: "2023-06-25",
    lastActive: "2023-07-08",
    status: "active",
  },
  {
    id: "7",
    name: "Back to School Surprise",
    type: "envelope",
    plays: 432,
    conversions: 38,
    createdAt: "2023-07-15",
    lastActive: "2023-07-20",
    status: "active",
  },
]
