"use client"

import type React from "react"

import { useState } from "react"
import { Loader2 } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"

export default function NotificationSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [notifications, setNotifications] = useState({
    // Email notifications
    emailGamePlays: true,
    emailConversions: true,
    emailWeeklySummary: true,
    emailNewFeatures: true,
    emailTips: false,
    emailMarketing: false,

    // In-app notifications
    appGamePlays: true,
    appConversions: true,
    appNewFeatures: true,
    appTips: true,

    // SMS notifications
    smsGamePlays: false,
    smsConversions: false,
    smsWeeklySummary: false,
  })

  const handleToggle = (key: keyof typeof notifications) => {
    setNotifications((prev) => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Notification preferences updated",
        description: "Your notification settings have been saved successfully.",
      })
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-1" style={{ color: "var(--text-primary)" }}>
          Notification Settings
        </h2>
        <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
          Control how and when you receive notifications
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Notifications */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Email Notifications
          </h3>
          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailGamePlays" className="font-medium">
                  Game Plays
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive emails when someone plays your games
                </p>
              </div>
              <Switch
                id="emailGamePlays"
                checked={notifications.emailGamePlays}
                onCheckedChange={() => handleToggle("emailGamePlays")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailConversions" className="font-medium">
                  Conversions
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive emails when someone converts after playing
                </p>
              </div>
              <Switch
                id="emailConversions"
                checked={notifications.emailConversions}
                onCheckedChange={() => handleToggle("emailConversions")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailWeeklySummary" className="font-medium">
                  Weekly Summary
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive a weekly summary of your game performance
                </p>
              </div>
              <Switch
                id="emailWeeklySummary"
                checked={notifications.emailWeeklySummary}
                onCheckedChange={() => handleToggle("emailWeeklySummary")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailNewFeatures" className="font-medium">
                  New Features
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Get notified about new platform features and updates
                </p>
              </div>
              <Switch
                id="emailNewFeatures"
                checked={notifications.emailNewFeatures}
                onCheckedChange={() => handleToggle("emailNewFeatures")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailTips" className="font-medium">
                  Tips & Tutorials
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive tips and tutorials to improve your games
                </p>
              </div>
              <Switch
                id="emailTips"
                checked={notifications.emailTips}
                onCheckedChange={() => handleToggle("emailTips")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailMarketing" className="font-medium">
                  Marketing & Promotions
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive marketing emails and special offers
                </p>
              </div>
              <Switch
                id="emailMarketing"
                checked={notifications.emailMarketing}
                onCheckedChange={() => handleToggle("emailMarketing")}
              />
            </div>
          </div>
        </div>

        {/* In-app Notifications */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            In-app Notifications
          </h3>
          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="appGamePlays" className="font-medium">
                  Game Plays
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive in-app notifications when someone plays your games
                </p>
              </div>
              <Switch
                id="appGamePlays"
                checked={notifications.appGamePlays}
                onCheckedChange={() => handleToggle("appGamePlays")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="appConversions" className="font-medium">
                  Conversions
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive in-app notifications when someone converts after playing
                </p>
              </div>
              <Switch
                id="appConversions"
                checked={notifications.appConversions}
                onCheckedChange={() => handleToggle("appConversions")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="appNewFeatures" className="font-medium">
                  New Features
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Get notified about new platform features and updates
                </p>
              </div>
              <Switch
                id="appNewFeatures"
                checked={notifications.appNewFeatures}
                onCheckedChange={() => handleToggle("appNewFeatures")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="appTips" className="font-medium">
                  Tips & Tutorials
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive tips and tutorials to improve your games
                </p>
              </div>
              <Switch id="appTips" checked={notifications.appTips} onCheckedChange={() => handleToggle("appTips")} />
            </div>
          </div>
        </div>

        {/* SMS Notifications */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            SMS Notifications
          </h3>
          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="smsGamePlays" className="font-medium">
                  Game Plays
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive SMS when someone plays your games
                </p>
              </div>
              <Switch
                id="smsGamePlays"
                checked={notifications.smsGamePlays}
                onCheckedChange={() => handleToggle("smsGamePlays")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="smsConversions" className="font-medium">
                  Conversions
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive SMS when someone converts after playing
                </p>
              </div>
              <Switch
                id="smsConversions"
                checked={notifications.smsConversions}
                onCheckedChange={() => handleToggle("smsConversions")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="smsWeeklySummary" className="font-medium">
                  Weekly Summary
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Receive a weekly SMS summary of your game performance
                </p>
              </div>
              <Switch
                id="smsWeeklySummary"
                checked={notifications.smsWeeklySummary}
                onCheckedChange={() => handleToggle("smsWeeklySummary")}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" type="button">
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading} style={{ backgroundColor: "var(--primary)" }}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
