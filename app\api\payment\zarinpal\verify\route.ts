import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { addCredits } from '@/lib/models/user';
import { getPaymentByAuthority, updatePaymentStatus } from '@/lib/models/payment';

// ZarinPal configuration
const ZARINPAL_MERCHANT_ID = process.env.ZARINPAL_MERCHANT_ID || 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx';
const ZARINPAL_SANDBOX = process.env.NODE_ENV !== 'production';
const ZARINPAL_BASE_URL = ZARINPAL_SANDBOX
  ? 'https://sandbox.zarinpal.com/pg/rest/WebGate'
  : 'https://payment.zarinpal.com/pg/v4/payment';

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const authority = url.searchParams.get('Authority');
    const status = url.searchParams.get('Status');

    console.log('Payment verification - Authority:', authority, 'Status:', status);

    if (!authority) {
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard/credits?payment=failed&error=no_authority`);
    }

    if (status !== 'OK') {
      // Update payment status to cancelled if we have the authority
      if (authority) {
        try {
          await updatePaymentStatus(authority, 'cancelled');
        } catch (error) {
          console.error('Error updating payment status to cancelled:', error);
        }
      }
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard/credits?payment=cancelled`);
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/auth/signin?callbackUrl=/dashboard/credits`);
    }

    // Get payment details from database
    const payment = await getPaymentByAuthority(authority);
    if (!payment) {
      console.error('Payment not found for authority:', authority);
      return NextResponse.redirect(
        `${process.env.NEXTAUTH_URL}/dashboard/credits?payment=failed&error=payment_not_found`
      );
    }

    // Check if payment belongs to current user
    if (payment.userId.toString() !== session.user.id) {
      console.error('Payment user mismatch:', payment.userId, session.user.id);
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard/credits?payment=failed&error=user_mismatch`);
    }

    // Convert amount to Rials for ZarinPal verification
    const amountInRials = payment.amount * 10;

    // Verify payment with ZarinPal
    const verifyRequest = {
      merchant_id: ZARINPAL_MERCHANT_ID,
      authority: authority,
      amount: amountInRials,
    };

    console.log('ZarinPal Verify Request:', verifyRequest);

    const response = await fetch(`${ZARINPAL_BASE_URL}/verify.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(verifyRequest),
    });

    const verifyResponse = await response.json();
    console.log('ZarinPal Verify Response:', verifyResponse);

    if (verifyResponse?.data?.code === 100 || verifyResponse?.data?.code === 101) {
      // Payment successful
      try {
        // Update payment status
        await updatePaymentStatus(authority, 'completed', verifyResponse?.data?.ref_id || '');

        // Add credits to user account
        const newCreditBalance = await addCredits(
          session.user.id,
          payment.creditsAmount,
          `خرید اعتبار از طریق زرین پال - کد پیگیری: ${verifyResponse?.data?.ref_id || ''}`
        );

        console.log(
          `Added ${payment.creditsAmount} credits to user ${session.user.id}. New balance: ${newCreditBalance}`
        );

        return NextResponse.redirect(
          `${process.env.NEXTAUTH_URL}/dashboard/credits?payment=success&credits=${payment.creditsAmount}&ref_id=${
            verifyResponse?.data?.ref_id || ''
          }`
        );
      } catch (error) {
        console.error('Error processing successful payment:', error);
        // Update payment status to failed
        await updatePaymentStatus(authority, 'failed');
        return NextResponse.redirect(
          `${process.env.NEXTAUTH_URL}/dashboard/credits?payment=failed&error=credit_add_failed`
        );
      }
    } else {
      console.error('Payment verification failed:', verifyResponse);
      // Update payment status to failed
      await updatePaymentStatus(authority, 'failed');
      return NextResponse.redirect(
        `${process.env.NEXTAUTH_URL}/dashboard/credits?payment=failed&error=verification_failed&status=${verifyResponse.Status}`
      );
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard/credits?payment=failed&error=server_error`);
  }
}

export async function POST(request: Request) {
  // Handle POST requests the same way as GET for ZarinPal callbacks
  return GET(request);
}
