import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { getNotificationById, markNotificationAsRead, deleteNotification } from "@/lib/models/notification"

// Mark a notification as read
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id
    const notificationId = params.id

    // Get the notification to verify ownership
    const notification = await getNotificationById(notificationId)

    if (!notification) {
      return NextResponse.json({ error: "Notification not found" }, { status: 404 })
    }

    // Verify the notification belongs to the current user
    if (notification.userId.toString() !== userId.toString()) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    // Mark as read
    const success = await markNotificationAsRead(notificationId)

    if (!success) {
      return NextResponse.json({ error: "Failed to update notification" }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating notification:", error)
    return NextResponse.json({ error: "Failed to update notification", details: error.message }, { status: 500 })
  }
}

// Delete a notification
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id
    const notificationId = params.id

    // Get the notification to verify ownership
    const notification = await getNotificationById(notificationId)

    if (!notification) {
      return NextResponse.json({ error: "Notification not found" }, { status: 404 })
    }

    // Verify the notification belongs to the current user
    if (notification.userId.toString() !== userId.toString()) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    // Delete notification
    const success = await deleteNotification(notificationId)

    if (!success) {
      return NextResponse.json({ error: "Failed to delete notification" }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting notification:", error)
    return NextResponse.json({ error: "Failed to delete notification", details: error.message }, { status: 500 })
  }
}
