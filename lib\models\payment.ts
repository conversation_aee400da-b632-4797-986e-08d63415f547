import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export interface Payment {
  _id?: ObjectId;
  userId: string | ObjectId;
  authority: string;
  amount: number; // Amount in Tomans
  creditsAmount: number; // Number of credits to be added
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  zarinpalRefId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function createPayment(paymentData: Omit<Payment, '_id' | 'createdAt' | 'updatedAt'>): Promise<string> {
  const client = await clientPromise;
  const collection = client.db().collection('payments');

  const payment: Payment = {
    ...paymentData,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const result = await collection.insertOne(payment);
  return result.insertedId.toString();
}

export async function getPaymentByAuthority(authority: string): Promise<Payment | null> {
  const client = await clientPromise;
  const collection = client.db().collection('payments');

  const payment = await collection.findOne({ authority });
  return payment as Payment | null;
}

export async function updatePaymentStatus(
  authority: string,
  status: Payment['status'],
  zarinpalRefId?: string
): Promise<boolean> {
  const client = await clientPromise;
  const collection = client.db().collection('payments');

  const updateData: any = {
    status,
    updatedAt: new Date(),
  };

  if (zarinpalRefId) {
    updateData.zarinpalRefId = zarinpalRefId;
  }

  const result = await collection.updateOne(
    { authority },
    { $set: updateData }
  );

  return result.modifiedCount > 0;
}

export async function getUserPayments(userId: string | ObjectId): Promise<Payment[]> {
  const client = await clientPromise;
  const collection = client.db().collection('payments');

  let id = userId;
  if (typeof userId === 'string' && ObjectId.isValid(userId)) {
    id = new ObjectId(userId);
  }

  const payments = await collection
    .find({ userId: id })
    .sort({ createdAt: -1 })
    .toArray();

  return payments as Payment[];
}
