import { type NextRequest, NextResponse } from "next/server"
import { createUser } from "@/lib/models/user"
import { sendWelcomeNotification } from "@/lib/models/notification"

export async function POST(req: NextRequest) {
  try {
    const { name, mobileNumber, password } = await req.json()

    // Validate input
    if (!name || !mobileNumber || !password) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Create user
    const user = await createUser({
      name,
      mobileNumber,
      password,
    })

    // Send welcome notification
    await sendWelcomeNotification(user._id, user.name)

    // Return success without exposing password
    const { password: _, ...userWithoutPassword } = user

    return NextResponse.json({ message: "User created successfully", user: userWithoutPassword }, { status: 201 })
  } catch (error) {
    console.error("Error in signup:", error)

    if (error instanceof Error) {
      if (error.message === "User with this mobile number already exists") {
        return NextResponse.json({ error: error.message }, { status: 409 })
      }
    }

    return NextResponse.json({ error: "An error occurred during signup" }, { status: 500 })
  }
}
