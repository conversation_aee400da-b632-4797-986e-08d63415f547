"use client"

import { Bar } from "react-chartjs-2"
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js"

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

export default function AudienceDemographicsChart() {
  // Mock data for the chart
  const labels = ["13-17", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"]

  const data = {
    labels,
    datasets: [
      {
        label: "Male",
        data: [5, 18, 24, 16, 8, 4, 2],
        backgroundColor: "rgba(59, 130, 246, 0.6)",
      },
      {
        label: "Female",
        data: [7, 22, 28, 14, 6, 3, 1],
        backgroundColor: "rgba(236, 72, 153, 0.6)",
      },
      {
        label: "Other",
        data: [1, 3, 4, 2, 1, 0.5, 0.2],
        backgroundColor: "rgba(132, 204, 22, 0.6)",
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      tooltip: {
        mode: "index" as const,
        intersect: false,
      },
    },
    scales: {
      x: {
        stacked: false,
        grid: {
          display: false,
        },
      },
      y: {
        stacked: false,
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          callback: (value: any) => value + "%",
        },
      },
    },
  }

  return (
    <div className="w-full h-full">
      <Bar data={data} options={options} />
    </div>
  )
}
