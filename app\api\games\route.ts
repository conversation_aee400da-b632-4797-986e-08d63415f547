import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { getUserGames } from "@/lib/models/game"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "You must be logged in to view games" }, { status: 401 })
    }

    // Get the user ID from the session
    const userId = session.user.id

    // Get all games for the user
    const games = await getUserGames(userId)

    return NextResponse.json({ games })
  } catch (error) {
    console.error("Error fetching games:", error)
    return NextResponse.json({ error: "Failed to fetch games" }, { status: 500 })
  }
}
