"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { toast } from "@/hooks/use-toast"

export default function NotificationPreferences() {
  const [preferences, setPreferences] = useState({
    gameUpdates: true,
    accountAlerts: true,
    marketingMessages: true,
    systemAnnouncements: true,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState("")

  useEffect(() => {
    const fetchPreferences = async () => {
      try {
        setIsLoading(true)
        const response = await fetch("/api/notifications/preferences")

        if (!response.ok) {
          throw new Error("Failed to fetch notification preferences")
        }

        const data = await response.json()
        setPreferences({
          gameUpdates: data.gameUpdates,
          accountAlerts: data.accountAlerts,
          marketingMessages: data.marketingMessages,
          systemAnnouncements: data.systemAnnouncements,
        })
      } catch (err) {
        setError("Failed to load preferences. Please try again.")
        console.error("Error fetching notification preferences:", err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPreferences()
  }, [])

  const handleSavePreferences = async () => {
    try {
      setIsSaving(true)
      const response = await fetch("/api/notifications/preferences", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(preferences),
      })

      if (!response.ok) {
        throw new Error("Failed to update notification preferences")
      }

      toast({
        title: "Preferences updated",
        description: "Your notification preferences have been saved.",
      })
    } catch (err) {
      setError("Failed to save preferences. Please try again.")
      console.error("Error updating notification preferences:", err)
      toast({
        title: "Error",
        description: "Failed to save notification preferences.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Preferences</CardTitle>
        <CardDescription>Choose what types of notifications you want to receive</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4">{error}</div>
        )}

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="gameUpdates">Game Updates</Label>
            <p className="text-sm text-muted-foreground">
              Receive notifications about your game activities and performance
            </p>
          </div>
          <Switch
            id="gameUpdates"
            checked={preferences.gameUpdates}
            onCheckedChange={(checked) => setPreferences({ ...preferences, gameUpdates: checked })}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="accountAlerts">Account Alerts</Label>
            <p className="text-sm text-muted-foreground">Important alerts about your account, security, and billing</p>
          </div>
          <Switch
            id="accountAlerts"
            checked={preferences.accountAlerts}
            onCheckedChange={(checked) => setPreferences({ ...preferences, accountAlerts: checked })}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="marketingMessages">Marketing Messages</Label>
            <p className="text-sm text-muted-foreground">Promotional offers, discounts, and new features</p>
          </div>
          <Switch
            id="marketingMessages"
            checked={preferences.marketingMessages}
            onCheckedChange={(checked) => setPreferences({ ...preferences, marketingMessages: checked })}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="systemAnnouncements">System Announcements</Label>
            <p className="text-sm text-muted-foreground">Platform updates, maintenance notices, and service changes</p>
          </div>
          <Switch
            id="systemAnnouncements"
            checked={preferences.systemAnnouncements}
            onCheckedChange={(checked) => setPreferences({ ...preferences, systemAnnouncements: checked })}
          />
        </div>

        <Button className="w-full mt-4" onClick={handleSavePreferences} disabled={isSaving}>
          {isSaving ? "Saving..." : "Save Preferences"}
        </Button>
      </CardContent>
    </Card>
  )
}
