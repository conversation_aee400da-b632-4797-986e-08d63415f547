'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Receipt, CreditCard, User, Package, Info, Loader2 } from 'lucide-react';
import { formatPrice, PRICE_PER_CREDIT_TOMANS } from '@/lib/pricing-config';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import type { WizardFormData } from '../GameWizard';

interface InvoiceStepProps {
  formData: WizardFormData;
  updateFormData: (data: Partial<WizardFormData>) => void;
  onBack: () => void;
  onNext: () => void;
}

// Plan configurations (should match PlanSelectionStep)
const plans = [
  {
    id: 'free',
    name: 'رایگان',
    credits: 0,
    price: 0,
    description: 'برای تست و آزمایش',
  },
  {
    id: 'starter',
    name: 'شروع',
    credits: 50,
    price: 50 * PRICE_PER_CREDIT_TOMANS,
    description: 'برای کسب‌وکارهای کوچک',
  },
  {
    id: 'business',
    name: 'کسب‌وکار',
    credits: 200,
    price: 200 * PRICE_PER_CREDIT_TOMANS,
    description: 'برای کسب‌وکارهای متوسط',
  },
  {
    id: 'enterprise',
    name: 'سازمانی',
    credits: 500,
    price: 500 * PRICE_PER_CREDIT_TOMANS,
    description: 'برای کمپین‌های بزرگ',
  },
];

export default function InvoiceStep({ formData, onBack, onNext }: InvoiceStepProps) {
  const { toast } = useToast();
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Get selected plan details
  const selectedPlan = plans.find((plan) => plan.id === formData.selectedPlanId) || plans[0];

  // Calculate total prizes needed
  const totalPrizes = formData.prizes.reduce((sum, prize) => sum + prize.quantity, 0);

  // Generate invoice number
  const invoiceNumber = `INV-${Date.now().toString().slice(-8)}`;
  const invoiceDate = new Date().toLocaleDateString('fa-IR');

  // Handle payment process
  const handlePayment = async () => {
    // If it's a free plan, just proceed to next step
    if (selectedPlan.id === 'free') {
      onNext();
      return;
    }

    setIsProcessingPayment(true);

    try {
      // Prepare game data for payment
      const gameData = {
        name: formData.gameName,
        type: formData.gameType,
        instagramHandle: formData.instagramHandle,
        pageCategory: formData.pageCategory,
        followerCount: formData.followerCount,
        prizes: formData.prizes,
        colorScheme: formData.colorScheme,
      };

      // Create payment request
      const response = await fetch('/api/payment/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'plan',
          planId: selectedPlan.id,
          gameData: gameData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment request');
      }

      const data = await response.json();

      if (data.success) {
        if (data.paymentUrl) {
          // Paid plan - redirect to payment gateway
          toast({
            title: 'انتقال به درگاه پرداخت',
            description: 'در حال انتقال به درگاه پرداخت زرین‌پال...',
          });

          // Redirect to ZarinPal payment gateway
          window.location.href = data.paymentUrl;
        } else if (data.gameId && data.gameLink) {
          // Free plan - game created successfully
          toast({
            title: 'موفقیت!',
            description: 'بازی رایگان شما با موفقیت ایجاد شد',
          });

          // Update form data with game info and proceed to final step
          formData.gameId = data.gameId;
          formData.gameLink = data.gameLink;
          formData.isSaved = true;

          // Proceed to final step
          onNext();
        } else {
          throw new Error('Invalid response from server');
        }
      } else {
        throw new Error(data.error || 'Payment request failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast({
        title: 'خطا در ایجاد درخواست پرداخت',
        description: error instanceof Error ? error.message : 'لطفاً دوباره تلاش کنید.',
        variant: 'destructive',
      });
      setIsProcessingPayment(false);
    }
  };

  return (
    <div className="py-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-2xl mx-auto"
      >
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{ backgroundColor: 'var(--secondary)' }}
          >
            <Receipt className="w-8 h-8 text-white" />
          </motion.div>
          <h2 className="text-2xl font-bold mb-2">فاکتور خرید</h2>
          <p className="text-gray-600">بررسی جزئیات سفارش قبل از پرداخت</p>
        </div>

        {/* Invoice Card */}
        <Card className="mb-6 border border-gray-200 shadow-sm">
          <CardHeader className="pb-6 border-b border-gray-100">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="flex items-center gap-3 text-gray-900">
                  <Receipt className="w-5 h-5 text-gray-600" />
                  <div>
                    <div className="text-xl font-semibold">فاکتور شماره {invoiceNumber}</div>
                    <p className="text-sm text-gray-500 font-normal mt-1">تاریخ: {invoiceDate}</p>
                  </div>
                </CardTitle>
              </div>
              <Badge
                variant="outline"
                className={`px-3 py-1 text-xs font-medium ${
                  selectedPlan.id === 'free' ? 'text-green-700 border-green-300' : 'text-blue-700 border-blue-300'
                }`}
              >
                {selectedPlan.id === 'free' ? 'رایگان' : 'پرداختی'}
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="space-y-8 p-6">
            {/* Customer Info */}
            <div>
              <h3 className="font-medium mb-4 flex items-center gap-2 text-gray-900 text-sm uppercase tracking-wide">
                <User className="w-4 h-4 text-gray-500" />
                اطلاعات مشتری
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600 text-sm">نام کاربری اینستاگرام</span>
                  <span className="font-medium text-gray-900">@{formData.instagramHandle}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600 text-sm">دسته‌بندی صفحه</span>
                  <span className="font-medium text-gray-900">{formData.pageCategory}</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-gray-600 text-sm">تعداد فالوور</span>
                  <span className="font-medium text-gray-900">{formData.followerCount}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Game Info */}
            <div>
              <h3 className="font-medium mb-4 flex items-center gap-2 text-gray-900 text-sm uppercase tracking-wide">
                <Package className="w-4 h-4 text-gray-500" />
                جزئیات بازی
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600 text-sm">نام بازی</span>
                  <span className="font-medium text-gray-900">{formData.gameName}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600 text-sm">نوع بازی</span>
                  <span className="font-medium text-gray-900">
                    {formData.gameType === 'wheel' && 'چرخ شانس'}
                    {formData.gameType === 'lever' && 'اهرم شانس'}
                    {formData.gameType === 'envelope' && 'پاکت شانس'}
                  </span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-gray-600 text-sm">تعداد کل جوایز</span>
                  <span className="font-medium text-gray-900">{totalPrizes} عدد</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Plan Details */}
            <div>
              <h3 className="font-medium mb-4 flex items-center gap-2 text-gray-900 text-sm uppercase tracking-wide">
                <CreditCard className="w-4 h-4 text-gray-500" />
                جزئیات پلن
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600 text-sm">نام پلن</span>
                  <span className="font-medium text-gray-900">{selectedPlan.name}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600 text-sm">اعتبار شامل</span>
                  <span className="font-medium text-gray-900">
                    {selectedPlan.credits === 0 ? 'محدود به ۵ اعتبار' : `${selectedPlan.credits} اعتبار`}
                  </span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-gray-600 text-sm">اعتبار مورد نیاز</span>
                  <span className="font-medium text-gray-900">{totalPrizes} اعتبار</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Price Summary */}
            <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
              <h3 className="font-medium mb-4 text-gray-900 text-sm uppercase tracking-wide">خلاصه قیمت</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3">
                  <span className="text-gray-600">پلن {selectedPlan.name}</span>
                  <span className="font-medium text-gray-900">
                    {selectedPlan.price === 0 ? 'رایگان' : formatPrice(selectedPlan.price)}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between items-center py-4 bg-white rounded-lg px-4 border border-gray-200">
                  <span className="text-lg font-semibold text-gray-900">مجموع قابل پرداخت</span>
                  <span className="text-xl font-bold text-gray-900">
                    {selectedPlan.price === 0 ? 'رایگان' : formatPrice(selectedPlan.price)}
                  </span>
                </div>
              </div>
            </div>

            {/* Credit Warning */}
            {selectedPlan.credits > 0 && selectedPlan.credits < totalPrizes && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  توجه: پلن انتخابی شما {selectedPlan.credits} اعتبار دارد اما شما {totalPrizes} اعتبار نیاز دارید.
                  لطفاً پلن مناسب‌تری انتخاب کنید یا تعداد جوایز را کاهش دهید.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Payment Actions */}
        <div className="flex justify-between items-center mt-8">
          <Button variant="outline" onClick={onBack} disabled={isProcessingPayment} className="flex items-center gap-2">
            بازگشت
          </Button>

          <Button
            onClick={handlePayment}
            disabled={isProcessingPayment || (selectedPlan.credits > 0 && selectedPlan.credits < totalPrizes)}
            className="flex items-center gap-2"
            style={{
              backgroundColor: 'var(--button-bg)',
              color: 'var(--button-text)',
            }}
          >
            {isProcessingPayment ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin ml-2" />
                در حال پردازش...
              </>
            ) : selectedPlan.id === 'free' ? (
              'ایجاد بازی رایگان'
            ) : (
              <>
                <CreditCard className="h-4 w-4 ml-2" />
                پرداخت {formatPrice(selectedPlan.price)}
              </>
            )}
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
