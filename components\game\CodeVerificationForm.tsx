"use client";

import type React from "react";
import { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, AlertCircle, CheckCircle, Bug } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface CodeVerificationFormProps {
  onVerify: () => void;
  phoneNumber: string;
  onBack: () => void;
  gameId?: string;
}

export default function CodeVerificationForm({
  onVerify,
  phoneNumber,
  onBack,
  gameId,
}: CodeVerificationFormProps) {
  const [code, setCode] = useState(["", "", "", "", "", ""]);
  const [error, setError] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes in seconds
  const [canResend, setCanResend] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [bypassMode, setBypassMode] = useState(false);
  const [otpFromServer, setOtpFromServer] = useState<string | null>(null);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Focus the first input on mount
  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  // Timer for OTP expiration
  useEffect(() => {
    if (timeLeft <= 0) {
      setCanResend(true);
      return;
    }

    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [timeLeft]);

  // Auto-verify when all 6 digits are filled
  useEffect(() => {
    if (
      code.every((digit) => digit !== "") &&
      code.length === 6 &&
      !isVerifying
    ) {
      handleVerify();
    }
  }, [code]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const handleInputChange = (index: number, value: string) => {
    // Allow only single-digit numbers or empty string
    if (value && !/^[0-9]?$/.test(value)) return;

    const newCode = [...code];
    newCode[index] = value;

    setCode(newCode);
    setError("");

    // Auto-focus next input if value is entered
    if (value && index < 5 && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1].focus();
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    // Handle backspace to go to previous input
    if (
      e.key === "Backspace" &&
      !code[index] &&
      index > 0 &&
      inputRefs.current[index - 1]
    ) {
      inputRefs.current[index - 1].focus();
    }

    // Handle Enter key press
    if (e.key === "Enter") {
      handleVerify();
    }
  };

  const handleVerify = async () => {
    if (isVerifying) return;

    const fullCode = code.join("");
    // console.log$$[^)]*$$;
    // console.log$$[^)]*$$;

    if (fullCode.length !== 6) {
      setError("Please enter all 6 digits of the verification code");
      return;
    }

    setError("");
    setIsVerifying(true);

    try {
      const response = await fetch("/api/otp/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phoneNumber,
          otpCode: fullCode,
          gameId,
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        setError(data.error || "Invalid verification code");
        setIsVerifying(false);
        return;
      }

      // Check if we're in bypass mode
      if (data.bypassMode) {
        setBypassMode(true);
      }

      setIsVerifying(false);
      setIsSuccess(true);

      setTimeout(() => {
        onVerify();
      }, 1000);
    } catch (error) {
      console.error("Error verifying OTP:", error);
      setError("Failed to verify code. Please try again.");
      setIsVerifying(false);
    }
  };

  const handleResendCode = async () => {
    if (!canResend) return;

    setIsResending(true);
    setError("");

    try {
      const response = await fetch("/api/otp/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ phoneNumber }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        setError(data.error || "Failed to resend verification code");
        setIsResending(false);
        return;
      }

      // Check if we're in bypass mode and got an OTP for testing
      if (data.bypassMode && data.otpForTesting) {
        setBypassMode(true);
        setOtpFromServer(data.otpForTesting);

        // Auto-fill the code fields with the OTP from server
        const otpDigits = data.otpForTesting.split("");
        setCode(otpDigits);
      } else if (data.otpForTesting) {
        setOtpFromServer(data.otpForTesting);
      }

      setTimeLeft(300);
      setCanResend(false);
      setIsResending(false);
    } catch (error) {
      console.error("Error resending OTP:", error);
      setError("Failed to resend code. Please try again.");
      setIsResending(false);
    }
  };

  const formatPhoneNumber = (phone: string) => {
    if (phone.length <= 4) return phone;
    if (phone.length <= 7) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
    return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
  };

  // Auto-fill the code if we have an OTP from the server
  const handleAutoFill = () => {
    if (!otpFromServer) return;

    const otpDigits = otpFromServer.split("");
    setCode(otpDigits);
  };

  return (
    <motion.div
      className="bg-white/80 backdrop-blur-sm p-5 sm:p-6 rounded-lg shadow-xl w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <Button
        variant="ghost"
        size="sm"
        className="mb-4"
        onClick={onBack}
        disabled={isVerifying || isSuccess}
      >
        <ArrowLeft className="h-4 w-4 mr-1" />
        Back
      </Button>

      {bypassMode && (
        <div className="bg-amber-100 border border-amber-300 rounded-md p-2 mb-4 flex items-center gap-2">
          <Bug className="h-4 w-4 text-amber-600" />
          <p className="text-xs text-amber-800">
            Development Mode: SMS verification is bypassed. OTP codes are not
            actually sent.
          </p>
        </div>
      )}

      <h2 className="text-2xl font-bold text-center mb-2">Verification Code</h2>
      <p className="text-center text-gray-600 mb-6">
        We've sent a 6-digit code to {formatPhoneNumber(phoneNumber)}
      </p>

      <div className="space-y-6">
        <div className="flex justify-center gap-2">
          {code.map((digit, index) => (
            <Input
              key={index}
              type="text"
              inputMode="numeric"
              maxLength={1}
              className="w-12 h-12 text-center text-xl"
              value={digit}
              onChange={(e) => handleInputChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              ref={(el) => (inputRefs.current[index] = el)}
              disabled={isVerifying || isSuccess}
            />
          ))}
        </div>

        {error && (
          <div className="flex items-center justify-center text-red-500 text-sm">
            <AlertCircle className="h-4 w-4 mr-1" />
            <span>{error}</span>
          </div>
        )}

        {isSuccess && (
          <div className="flex items-center justify-center text-green-500 text-sm">
            <CheckCircle className="h-4 w-4 mr-1" />
            <span>Code verified successfully!</span>
          </div>
        )}

        {otpFromServer && !isSuccess && (
          <div className="flex flex-col items-center justify-center">
            <p className="text-amber-600 text-sm mb-1">
              Development OTP code: {otpFromServer}
            </p>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAutoFill}
              className="text-xs"
            >
              Auto-fill Code
            </Button>
          </div>
        )}

        <div className="flex justify-between items-center text-sm">
          <span
            className={`${timeLeft <= 60 ? "text-red-500" : "text-gray-500"}`}
          >
            Code expires in: {formatTime(timeLeft)}
          </span>
          {canResend && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleResendCode}
              disabled={isResending}
              className="text-primary"
            >
              {isResending ? "Sending..." : "Resend Code"}
            </Button>
          )}
        </div>

        <Button
          className="w-full"
          onClick={handleVerify}
          disabled={
            code.some((digit) => digit === "") || isVerifying || isSuccess
          }
        >
          {isVerifying
            ? "Verifying..."
            : isSuccess
            ? "Verified!"
            : "Verify Code"}
        </Button>
      </div>
    </motion.div>
  );
}
