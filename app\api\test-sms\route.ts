import { NextResponse } from "next/server";
const MelipayamakApi = require("melipayamak");
import axios from "axios";

export async function GET() {
  try {
    const username = process.env.MELIPAYAMAK_USERNAME;
    const password = process.env.MELIPAYAMAK_PASSWORD;
    const from = process.env.MELIPAYAMAK_FROM;

    if (!username || !password || !from) {
      return NextResponse.json({
        success: false,
        error: "Missing Melipayamak credentials",
        credentials: {
          usernameExists: !!username,
          passwordExists: !!password,
          fromExists: !!from,
        },
      });
    }

    // Test the connection to Melipayamak
    const api = new <PERSON>ipayamakApi(username, password);
    const sms = api.sms();

    // Get account credit
    let creditResult;
    try {
      creditResult = await sms.getCredit();
      // console.log$$[^)]*$$;
    } catch (err) {
      console.error("Error getting credit:", err);
      return NextResponse.json({
        success: false,
        error: "Failed to get account credit",
        details: err instanceof Error ? err.message : String(err),
      });
    }

    // Check HyperSMS credentials
    const smsUsername = process.env.SMS_USERNAME;
    const smsPassword = process.env.SMS_PASSWORD;

    return NextResponse.json({
      success: true,
      message: "SMS credentials check",
      melipayamak: {
        valid: true,
        credit: creditResult,
        credentials: {
          username,
          from,
        },
      },
      hyperSMS: {
        valid: !!smsUsername && !!smsPassword,
        credentials: {
          usernameExists: !!smsUsername,
          passwordExists: !!smsPassword,
        },
      },
    });
  } catch (error) {
    console.error("Error testing SMS services:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to test SMS services",
      details: error instanceof Error ? error.message : String(error),
    });
  }
}

export async function POST(request: Request) {
  try {
    const { phoneNumber, message, service } = await request.json();

    if (!phoneNumber) {
      return NextResponse.json(
        {
          success: false,
          error: "Phone number is required",
        },
        { status: 400 }
      );
    }

    // Format the phone number
    const formattedNumber = phoneNumber.replace(/^\+/, "").replace(/\D/g, "");

    // Test Melipayamak service
    if (service === "melipayamak" || !service) {
      const username = process.env.MELIPAYAMAK_USERNAME;
      const password = process.env.MELIPAYAMAK_PASSWORD;
      const from = process.env.MELIPAYAMAK_FROM;

      if (!username || !password || !from) {
        return NextResponse.json({
          success: false,
          error: "Missing Melipayamak credentials",
        });
      }

      // Initialize Melipayamak client
      const api = new MelipayamakApi(username, password);
      const sms = api.sms();

      // Send a test message
      const testMessage =
        message || "This is a test message from your game application.";

      try {
        const result = await sms.send(formattedNumber, from, testMessage);
        // console.log$$[^)]*$$;

        return NextResponse.json({
          success: true,
          message: "Test SMS sent via Melipayamak",
          result,
          details: {
            to: formattedNumber,
            from,
            message: testMessage,
          },
        });
      } catch (err) {
        console.error("Error sending test SMS via Melipayamak:", err);
        return NextResponse.json({
          success: false,
          error: "Failed to send test SMS via Melipayamak",
          details: err instanceof Error ? err.message : String(err),
        });
      }
    }
    // Test HyperSMS service
    else if (service === "hypersms") {
      const username = process.env.SMS_USERNAME;
      const password = process.env.SMS_PASSWORD;

      if (!username || !password) {
        return NextResponse.json({
          success: false,
          error: "Missing HyperSMS credentials",
        });
      }

      const testMessage =
        message ||
        "This is a test congratulation message from your game application.";
      const url = `https://hypersmsc.ir/api/json/sendgroupget?username=${username}&password=${password}&api=39&from=200032217400&to=${formattedNumber}&text=${encodeURIComponent(
        testMessage
      )}`;

      try {
        const response = await axios({
          method: "GET",
          url: url,
          headers: {
            "Content-Type": "application/text; charset=utf-8",
          },
        });

        if (response.statusText !== "OK") {
          return NextResponse.json({
            success: false,
            error: "Failed to send test SMS via HyperSMS",
            details: response.statusText,
          });
        }

        return NextResponse.json({
          success: true,
          message: "Test SMS sent via HyperSMS",
          result: response.data,
          details: {
            to: formattedNumber,
            message: testMessage,
          },
        });
      } catch (err) {
        console.error("Error sending test SMS via HyperSMS:", err);
        return NextResponse.json({
          success: false,
          error: "Failed to send test SMS via HyperSMS",
          details: err instanceof Error ? err.message : String(err),
        });
      }
    } else {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid SMS service specified",
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error in test SMS endpoint:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to process request",
      details: error instanceof Error ? error.message : String(error),
    });
  }
}
