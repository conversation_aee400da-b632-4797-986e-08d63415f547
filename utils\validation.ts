/**
 * Validates a full name using regex
 * Only allows letters and spaces
 */
export function validateFullName(fullName: string): boolean {
  const nameRegex = /^[A-Za-z\s]+$/
  return nameRegex.test(fullName) && fullName.trim().length > 0
}

/**
 * Validates a mobile number using regex
 * Accepts formats like:
 * - International: +1234567890, +98 ************
 * - Persian: 09123456789, 0912-345-6789
 * - Standard: 1234567890, ************
 */
export function validateMobileNumber(mobileNumber: string): boolean {
  // Remove all spaces, dashes, and other separators
  const cleanNumber = mobileNumber.replace(/[\s\-$$$$]/g, "")

  // Persian mobile number format (starts with 09)
  const persianRegex = /^(0|98|\+98)?9\d{9}$/

  // International format
  const internationalRegex = /^(\+?\d{1,3})?(\d{10,12})$/

  return persianRegex.test(cleanNumber) || internationalRegex.test(cleanNumber)
}

/**
 * Validates password complexity
 * Requires at least 8 characters with at least one letter and one number
 */
export function validatePassword(password: string): boolean {
  // At least 8 characters, at least one letter and one number
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/
  return passwordRegex.test(password)
}

/**
 * Formats a Persian phone number for display
 * Converts formats like 09123456789 to 0912-345-6789
 */
export function formatPersianPhoneNumber(phoneNumber: string): string {
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, "")

  // Check if it's a Persian mobile number
  if (digits.startsWith("0") && digits.length === 11) {
    return `${digits.substring(0, 4)}-${digits.substring(4, 7)}-${digits.substring(7)}`
  }

  // If it starts with +98 or 98, format as Persian number
  if ((digits.startsWith("98") && digits.length === 12) || (digits.startsWith("98") && digits.length === 13)) {
    const withoutPrefix = digits.substring(digits.startsWith("+") ? 3 : 2)
    return `0${withoutPrefix.substring(0, 3)}-${withoutPrefix.substring(3, 6)}-${withoutPrefix.substring(6)}`
  }

  // Return as is if not matching Persian format
  return phoneNumber
}
