'use client';

import { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import { signIn as nextAuthSignIn, signOut as nextAuthSignOut, useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface AuthContextType {
  user: any;
  status: 'loading' | 'authenticated' | 'unauthenticated';
  signIn: (mobileNumber: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signUp: (name: string, mobileNumber: string, password: string) => Promise<void>;
  signUpWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    if (session?.user) {
      setUser(session.user);
    } else {
      setUser(null);
    }
  }, [session]);

  const signIn = async (mobileNumber: string, password: string) => {
    try {
      const result = await nextAuthSignIn('credentials', {
        mobileNumber,
        password,
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      router.push('/dashboard');
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signUp = async (name: string, mobileNumber: string, password: string) => {
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          mobileNumber,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'An error occurred during signup');
      }

      // Sign in after successful signup
      const result = await nextAuthSignIn('credentials', {
        mobileNumber,
        password,
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      // Redirect to create-game page instead of dashboard after signup
      router.push('/create-game');
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    try {
      const result = await nextAuthSignIn('google', {
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      router.push('/dashboard');
    } catch (error) {
      console.error('Google sign in error:', error);
      throw error;
    }
  };

  const signUpWithGoogle = async () => {
    try {
      const result = await nextAuthSignIn('google', {
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      // Redirect to create-game page for new Google users
      router.push('/create-game');
    } catch (error) {
      console.error('Google sign up error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await nextAuthSignOut({ redirect: false });
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ user, status, signIn, signInWithGoogle, signUp, signUpWithGoogle, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
