'use client';

import type React from 'react';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Eye, EyeOff, XCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { validateMobileNumber, validatePassword, validateFullName } from '@/utils/validation';
import { useAuth } from '@/lib/auth-context';
import GoogleSignInButton from './GoogleSignInButton';

export default function SignupForm() {
  const router = useRouter();
  const { signUp, signUpWithGoogle } = useAuth();
  const [formData, setFormData] = useState({
    fullName: '',
    mobileNumber: '',
    password: '',
    confirmPassword: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({
    fullName: '',
    mobileNumber: '',
    password: '',
    confirmPassword: '',
    general: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing again
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...errors };

    // Validate full name
    if (!validateFullName(formData.fullName)) {
      newErrors.fullName = 'لطفاً نام معتبر وارد کنید (فقط حروف و فاصله)';
      valid = false;
    }

    // Validate mobile number
    if (!validateMobileNumber(formData.mobileNumber)) {
      newErrors.mobileNumber = 'لطفاً شماره موبایل معتبر وارد کنید';
      valid = false;
    }

    // Validate password
    if (!validatePassword(formData.password)) {
      newErrors.password = 'رمز عبور باید حداقل ۸ کاراکتر با حروف و اعداد باشد';
      valid = false;
    }

    // Validate password confirmation
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'رمزهای عبور مطابقت ندارند';
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors((prev) => ({ ...prev, general: '' }));

    try {
      await signUp(formData.fullName, formData.mobileNumber, formData.password);
      // Redirect is handled by the auth hook
    } catch (error) {
      console.error('Error submitting form:', error);
      setErrors((prev) => ({
        ...prev,
        general: error instanceof Error ? error.message : 'خطایی در هنگام ثبت نام رخ داد',
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  const inputVariants = {
    focus: { scale: 1.02, transition: { duration: 0.2 } },
    blur: { scale: 1, transition: { duration: 0.2 } },
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {errors.general && (
        <div className="bg-red-50 p-3 rounded-md border border-red-200">
          <div className="flex items-center text-sm text-red-600">
            <XCircle className="h-4 w-4 ml-1.5 flex-shrink-0" />
            <span>{errors.general}</span>
          </div>
        </div>
      )}

      {/* Full Name Field */}
      <div>
        <label className="text-sm font-medium block mb-1.5" style={{ color: 'var(--text-primary)' }}>
          نام کامل
        </label>
        <div className="relative">
          <motion.input
            type="text"
            name="fullName"
            value={formData.fullName}
            onChange={handleChange}
            whileFocus="focus"
            variants={inputVariants}
            className="w-full px-3 py-2 border rounded-md"
            style={{
              backgroundColor: 'var(--background)',
              color: 'var(--text-primary)',
              borderColor: errors.fullName ? '#F97316' : 'var(--border)',
            }}
            placeholder="نام کامل خود را وارد کنید"
            required
          />
        </div>
        {errors.fullName && (
          <div className="flex items-center mt-1.5 text-sm" style={{ color: '#F97316' }}>
            <XCircle className="h-4 w-4 ml-1.5 flex-shrink-0" />
            <span>{errors.fullName}</span>
          </div>
        )}
      </div>

      {/* Mobile Number Field */}
      <div>
        <label className="text-sm font-medium block mb-1.5" style={{ color: 'var(--text-primary)' }}>
          شماره موبایل
        </label>
        <div className="relative">
          <motion.input
            type="tel"
            name="mobileNumber"
            autoComplete="tel"
            value={formData.mobileNumber}
            onChange={handleChange}
            whileFocus="focus"
            variants={inputVariants}
            className="w-full px-3 py-2 border rounded-md"
            style={{
              backgroundColor: 'var(--background)',
              color: 'var(--text-primary)',
              borderColor: errors.mobileNumber ? '#F97316' : 'var(--border)',
            }}
            placeholder="شماره موبایل خود را وارد کنید"
            required
          />
        </div>
        {errors.mobileNumber && (
          <div className="flex items-center mt-1.5 text-sm" style={{ color: '#F97316' }}>
            <XCircle className="h-4 w-4 ml-1.5 flex-shrink-0" />
            <span>{errors.mobileNumber}</span>
          </div>
        )}
      </div>

      {/* Password Field */}
      <div>
        <label className="text-sm font-medium block mb-1.5" style={{ color: 'var(--text-primary)' }}>
          رمز عبور
        </label>
        <div className="relative">
          <motion.input
            type={showPassword ? 'text' : 'password'}
            name="password"
            value={formData.password}
            onChange={handleChange}
            whileFocus="focus"
            variants={inputVariants}
            className="w-full px-3 py-2 border rounded-md pl-10"
            style={{
              backgroundColor: 'var(--background)',
              color: 'var(--text-primary)',
              borderColor: errors.password ? '#F97316' : 'var(--border)',
            }}
            placeholder="رمز عبور ایجاد کنید"
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 flex items-center justify-center focus:outline-none"
            style={{ color: 'var(--text-secondary)' }}
            aria-label={showPassword ? 'مخفی کردن رمز عبور' : 'نمایش رمز عبور'}
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
        {errors.password && (
          <div className="flex items-center mt-1.5 text-sm" style={{ color: '#F97316' }}>
            <XCircle className="h-4 w-4 ml-1.5 flex-shrink-0" />
            <span>{errors.password}</span>
          </div>
        )}
      </div>

      {/* Confirm Password Field */}
      <div>
        <label className="text-sm font-medium block mb-1.5" style={{ color: 'var(--text-primary)' }}>
          تأیید رمز عبور
        </label>
        <div className="relative">
          <motion.input
            type={showConfirmPassword ? 'text' : 'password'}
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            whileFocus="focus"
            variants={inputVariants}
            className="w-full px-3 py-2 border rounded-md pl-10"
            style={{
              backgroundColor: 'var(--background)',
              color: 'var(--text-primary)',
              borderColor: errors.confirmPassword ? '#F97316' : 'var(--border)',
            }}
            placeholder="رمز عبور خود را تأیید کنید"
            required
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 flex items-center justify-center focus:outline-none"
            style={{ color: 'var(--text-secondary)' }}
            aria-label={showConfirmPassword ? 'مخفی کردن رمز عبور' : 'نمایش رمز عبور'}
          >
            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
        {errors.confirmPassword && (
          <div className="flex items-center mt-1.5 text-sm" style={{ color: '#F97316' }}>
            <XCircle className="h-4 w-4 ml-1.5 flex-shrink-0" />
            <span>{errors.confirmPassword}</span>
          </div>
        )}
      </div>

      <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="pt-2">
        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting}
          style={{
            backgroundColor: 'var(--button-bg)',
            color: 'var(--button-text)',
          }}
        >
          {isSubmitting ? 'در حال ایجاد حساب...' : 'ایجاد حساب'}
        </Button>
      </motion.div>

      {/* Divider */}
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t" style={{ borderColor: 'var(--border)' }} />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2" style={{ backgroundColor: 'var(--background)', color: 'var(--text-secondary)' }}>
            یا
          </span>
        </div>
      </div>

      {/* Google Sign Up Button */}
      <GoogleSignInButton onClick={signUpWithGoogle}>ثبت نام با گوگل</GoogleSignInButton>
    </form>
  );
}
