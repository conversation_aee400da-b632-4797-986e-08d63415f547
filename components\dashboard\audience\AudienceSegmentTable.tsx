"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, Users } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function AudienceSegmentTable() {
  const [segments] = useState([
    {
      id: "1",
      name: "High Spenders",
      description: "Users who have spent over $100 in the last 30 days",
      size: 1234,
      createdAt: "2023-06-15",
      lastUpdated: "2023-07-01",
    },
    {
      id: "2",
      name: "New Followers",
      description: "Users who started following in the last 7 days",
      size: 3456,
      createdAt: "2023-07-10",
      lastUpdated: "2023-07-10",
    },
    {
      id: "3",
      name: "Frequent Players",
      description: "Users who have played games more than 5 times",
      size: 2345,
      createdAt: "2023-05-20",
      lastUpdated: "2023-06-28",
    },
    {
      id: "4",
      name: "Inactive Users",
      description: "Users who haven't engaged in the last 30 days",
      size: 4567,
      createdAt: "2023-04-10",
      lastUpdated: "2023-06-15",
    },
  ])

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" })
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b" style={{ borderColor: "var(--border)" }}>
            <th className="p-3 text-left w-10">
              <Checkbox aria-label="Select all segments" />
            </th>
            <th className="text-left p-3 text-sm font-medium" style={{ color: "var(--text-secondary)" }}>
              Segment Name
            </th>
            <th className="text-left p-3 text-sm font-medium" style={{ color: "var(--text-secondary)" }}>
              Description
            </th>
            <th className="text-left p-3 text-sm font-medium" style={{ color: "var(--text-secondary)" }}>
              Size
            </th>
            <th className="text-left p-3 text-sm font-medium" style={{ color: "var(--text-secondary)" }}>
              Created
            </th>
            <th className="text-left p-3 text-sm font-medium" style={{ color: "var(--text-secondary)" }}>
              Last Updated
            </th>
            <th className="text-right p-3 text-sm font-medium" style={{ color: "var(--text-secondary)" }}>
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          {segments.map((segment) => (
            <tr key={segment.id} className="border-b hover:bg-gray-50" style={{ borderColor: "var(--border)" }}>
              <td className="p-3">
                <Checkbox aria-label={`Select ${segment.name}`} />
              </td>
              <td className="p-3">
                <div className="flex items-center gap-2">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: "rgba(139, 92, 246, 0.1)" }}
                  >
                    <Users className="h-4 w-4" style={{ color: "var(--primary)" }} />
                  </div>
                  <span className="font-medium" style={{ color: "var(--text-primary)" }}>
                    {segment.name}
                  </span>
                </div>
              </td>
              <td className="p-3" style={{ color: "var(--text-primary)" }}>
                {segment.description}
              </td>
              <td className="p-3" style={{ color: "var(--text-primary)" }}>
                {segment.size.toLocaleString()}
              </td>
              <td className="p-3" style={{ color: "var(--text-primary)" }}>
                {formatDate(segment.createdAt)}
              </td>
              <td className="p-3" style={{ color: "var(--text-primary)" }}>
                {formatDate(segment.lastUpdated)}
              </td>
              <td className="p-3 text-right">
                <div className="flex items-center justify-end gap-2">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Edit className="h-4 w-4" style={{ color: "var(--primary)" }} />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      <DropdownMenuItem>Export Segment</DropdownMenuItem>
                      <DropdownMenuItem>Create Campaign</DropdownMenuItem>
                      <DropdownMenuItem className="text-red-500">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
