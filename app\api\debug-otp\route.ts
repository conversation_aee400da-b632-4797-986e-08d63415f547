import { NextResponse } from "next/server"
import { listAllOTPs } from "@/lib/otp-store"

// This endpoint is for debugging only and should be disabled in production
export async function GET() {
  // Only allow in development mode
  if (process.env.NODE_ENV !== "development") {
    return NextResponse.json({ error: "Not available in production" }, { status: 403 })
  }

  const allOTPs = listAllOTPs()

  return NextResponse.json({
    message: "Current OTP store contents",
    otps: allOTPs,
    count: allOTPs.length,
  })
}
