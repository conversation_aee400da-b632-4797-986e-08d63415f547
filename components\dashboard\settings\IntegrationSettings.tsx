"use client"

import type React from "react"

import { useState } from "react"
import { AlertCircle, Copy, ExternalLink, Instagram, Loader2, RefreshCw, Twitter } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"

export default function IntegrationSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [integrations, setIntegrations] = useState({
    instagram: true,
    twitter: false,
    facebook: false,
    tiktok: false,
  })

  const handleToggle = (key: keyof typeof integrations) => {
    setIntegrations((prev) => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  const handleCopyApiKey = () => {
    navigator.clipboard.writeText("sk_test_example_api_key_12345")
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard.",
    })
  }

  const handleRegenerateApiKey = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "API key regenerated",
        description: "Your new API key has been generated successfully.",
      })
    }, 1000)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Integration settings updated",
        description: "Your integration settings have been saved successfully.",
      })
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-1" style={{ color: "var(--text-primary)" }}>
          Integration Settings
        </h2>
        <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
          Connect your social media accounts and manage API access
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Social Media Integrations */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Social Media Integrations
          </h3>
          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-pink-100 p-2 rounded-md">
                  <Instagram className="h-5 w-5 text-pink-600" />
                </div>
                <div>
                  <Label htmlFor="instagram" className="font-medium">
                    Instagram
                  </Label>
                  <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                    {integrations.instagram ? "Connected as @myshop" : "Not connected"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {integrations.instagram ? (
                  <Button variant="outline" size="sm">
                    Disconnect
                  </Button>
                ) : (
                  <Button size="sm" style={{ backgroundColor: "var(--primary)" }}>
                    Connect
                  </Button>
                )}
                <Switch
                  id="instagram"
                  checked={integrations.instagram}
                  onCheckedChange={() => handleToggle("instagram")}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-blue-100 p-2 rounded-md">
                  <Twitter className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <Label htmlFor="twitter" className="font-medium">
                    Twitter
                  </Label>
                  <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                    {integrations.twitter ? "Connected as @myshop" : "Not connected"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {integrations.twitter ? (
                  <Button variant="outline" size="sm">
                    Disconnect
                  </Button>
                ) : (
                  <Button size="sm" style={{ backgroundColor: "var(--primary)" }}>
                    Connect
                  </Button>
                )}
                <Switch id="twitter" checked={integrations.twitter} onCheckedChange={() => handleToggle("twitter")} />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-blue-100 p-2 rounded-md">
                  <svg className="h-5 w-5 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                </div>
                <div>
                  <Label htmlFor="facebook" className="font-medium">
                    Facebook
                  </Label>
                  <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                    {integrations.facebook ? "Connected" : "Not connected"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {integrations.facebook ? (
                  <Button variant="outline" size="sm">
                    Disconnect
                  </Button>
                ) : (
                  <Button size="sm" style={{ backgroundColor: "var(--primary)" }}>
                    Connect
                  </Button>
                )}
                <Switch
                  id="facebook"
                  checked={integrations.facebook}
                  onCheckedChange={() => handleToggle("facebook")}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-gray-100 p-2 rounded-md">
                  <svg className="h-5 w-5 text-black" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z" />
                  </svg>
                </div>
                <div>
                  <Label htmlFor="tiktok" className="font-medium">
                    TikTok
                  </Label>
                  <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                    {integrations.tiktok ? "Connected" : "Not connected"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {integrations.tiktok ? (
                  <Button variant="outline" size="sm">
                    Disconnect
                  </Button>
                ) : (
                  <Button size="sm" style={{ backgroundColor: "var(--primary)" }}>
                    Connect
                  </Button>
                )}
                <Switch id="tiktok" checked={integrations.tiktok} onCheckedChange={() => handleToggle("tiktok")} />
              </div>
            </div>
          </div>
        </div>

        {/* API Access */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            API Access
          </h3>
          <Separator />

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              Keep your API keys secure. Do not share them in public repositories or client-side code.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey" className="font-medium">
                API Key
              </Label>
              <div className="flex">
                <Input
                  id="apiKey"
                  value="sk_test_example_api_key_12345"
                  readOnly
                  className="rounded-r-none font-mono text-sm"
                />
                <Button
                  type="button"
                  variant="outline"
                  className="rounded-l-none border-l-0"
                  onClick={handleCopyApiKey}
                >
                  <Copy className="h-4 w-4" />
                  <span className="sr-only">Copy</span>
                </Button>
              </div>
              <p className="text-xs" style={{ color: "var(--text-secondary)" }}>
                Last generated on April 15, 2023
              </p>
            </div>

            <Button
              type="button"
              variant="outline"
              onClick={handleRegenerateApiKey}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
              Regenerate API Key
            </Button>
          </div>

          <div className="pt-4">
            <Button
              type="button"
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => window.open("#", "_blank")}
            >
              <ExternalLink className="h-4 w-4" />
              View API Documentation
            </Button>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" type="button">
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading} style={{ backgroundColor: "var(--primary)" }}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
