'use client';

import { TabsContent } from '@/components/ui/tabs';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowDownAZ,
  ArrowUpAZ,
  Calendar,
  ChevronDown,
  Clock,
  Copy,
  Download,
  FileText,
  Filter,
  Grid,
  Layers,
  Plus,
  Search,
  SlidersHorizontal,
  Trash2,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import GameCard from '@/components/dashboard/GameCard';
import GameDetailModal from '@/components/dashboard/games/GameDetailModal';
import GameListItem from '@/components/dashboard/games/GameListItem';
import GameFilterSidebar from '@/components/dashboard/games/GameFilterSidebar';
import NoGamesPlaceholder from '@/components/dashboard/games/NoGamesPlaceholder';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import type { Game } from '@/types/game';

export default function GamesPage() {
  const router = useRouter();
  const [view, setView] = useState<'grid' | 'list' | 'compact'>('grid');
  const [sortBy, setSortBy] = useState<string>('newest');
  const [filterOpen, setFilterOpen] = useState(false);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGames, setSelectedGames] = useState<string[]>([]);
  const [games, setGames] = useState<Game[]>([]);
  const [filteredGames, setFilteredGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: [] as string[],
    type: [] as string[],
    dateRange: 'all',
  });

  // Fetch games from API
  const fetchGames = async () => {
    try {
      setIsLoading(true); // Show loading state
      const queryParams = new URLSearchParams();

      if (filters.status.length > 0) {
        queryParams.append('status', filters.status.join(','));
      }

      if (filters.type.length > 0) {
        queryParams.append('type', filters.type.join(','));
      }

      if (filters.dateRange !== 'all') {
        queryParams.append('dateRange', filters.dateRange);
      }

      queryParams.append('sortBy', sortBy);

      const response = await fetch(`/api/dashboard/games?${queryParams.toString()}`);
      if (!response.ok) throw new Error('Failed to fetch games');

      const data = await response.json();
      setGames(data);
    } catch (error) {
      console.error('Error fetching games:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch games when filters or sortBy change
  useEffect(() => {
    fetchGames();
  }, [filters, sortBy]);

  // Handle search and filtering
  useEffect(() => {
    let result = [...games];

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (game) =>
          game.name.toLowerCase().includes(query) ||
          game.type.toLowerCase().includes(query) ||
          game.status.toLowerCase().includes(query)
      );
    }

    setFilteredGames(result);
  }, [searchQuery, games]);

  // Handle game selection for batch actions
  const toggleGameSelection = (gameId: string) => {
    setSelectedGames((prev) => (prev.includes(gameId) ? prev.filter((id) => id !== gameId) : [...prev, gameId]));
  };

  const selectAllGames = () => {
    if (selectedGames.length === filteredGames.length) {
      setSelectedGames([]);
    } else {
      setSelectedGames(filteredGames.map((game) => game.id));
    }
  };

  // Handle batch actions
  const handleBatchAction = (action: string) => {
    if (action === 'delete') {
      alert(`Deleting ${selectedGames.length} games`);
      setSelectedGames([]);
      fetchGames(); // Refresh games after batch delete
    } else if (action === 'export') {
      alert(`Exporting data for ${selectedGames.length} games`);
    } else if (action === 'duplicate') {
      alert(`Duplicating ${selectedGames.length} games`);
      setSelectedGames([]);
    }
  };

  // Handle filter changes
  const updateFilters = (newFilters: any) => {
    setFilters({ ...filters, ...newFilters });
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      status: [],
      type: [],
      dateRange: 'all',
    });
    setSearchQuery('');
  };

  // Fetch game details when a game is selected
  const handleViewGameDetails = async (game: Game) => {
    try {
      const response = await fetch(`/api/dashboard/games/${game.id}`);
      if (!response.ok) throw new Error('Failed to fetch game details');

      const gameDetails = await response.json();
      setSelectedGame(gameDetails);
    } catch (error) {
      console.error('Error fetching game details:', error);
      setSelectedGame(game);
    }
  };

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      <div className="flex h-screen overflow-hidden">
        <DashboardSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <DashboardHeader />
          <div className="flex-1 overflow-hidden flex">
            <GameFilterSidebar
              isOpen={filterOpen}
              onClose={() => setFilterOpen(false)}
              filters={filters}
              updateFilters={updateFilters}
              resetFilters={resetFilters}
            />
            <div className="flex-1 overflow-y-auto p-4 md:p-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div>
                  <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                    بازی‌های من
                  </h1>
                  <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                    همه بازی‌های اینستاگرام خود را مدیریت و نظارت کنید
                  </p>
                </div>
                <Button
                  onClick={() => router.push('/create-game')}
                  className="flex items-center gap-2"
                  style={{
                    backgroundColor: 'var(--secondary)',
                    color: 'var(--button-text)',
                  }}
                >
                  <Plus className="h-4 w-4" />
                  ایجاد بازی جدید
                </Button>
              </div>

              <div className="mb-6 flex flex-col sm:flex-row gap-3">
                <div className="relative flex-1">
                  <Search
                    className="h-4 w-4 absolute left-3 top-1/2 -translate-y-1/2"
                    style={{ color: 'var(--text-secondary)' }}
                  />
                  <input
                    type="text"
                    placeholder="جستجوی بازی‌ها..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 pr-4 py-2 w-full rounded-md border"
                    style={{
                      borderColor: 'var(--border)',
                      backgroundColor: 'var(--background)',
                      color: 'var(--text-primary)',
                    }}
                  />
                </div>
                <Button
                  variant="outline"
                  className={`flex items-center gap-2 ${filterOpen ? 'bg-primary/10' : ''}`}
                  onClick={() => setFilterOpen(!filterOpen)}
                >
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">Filters</span>
                  {(filters.status.length > 0 || filters.type.length > 0 || filters.dateRange !== 'all') && (
                    <Badge className="ml-1 h-5 w-5 p-0 flex items-center justify-center">
                      {filters.status.length + filters.type.length + (filters.dateRange !== 'all' ? 1 : 0)}
                    </Badge>
                  )}
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center gap-2">
                      <SlidersHorizontal className="h-4 w-4" />
                      <span className="hidden sm:inline">Sort</span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>Sort Games By</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                      <DropdownMenuItem onClick={() => setSortBy('newest')}>
                        <Calendar className="mr-2 h-4 w-4" />
                        <span>Newest First</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy('oldest')}>
                        <Clock className="mr-2 h-4 w-4" />
                        <span>Oldest First</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy('name-asc')}>
                        <ArrowDownAZ className="mr-2 h-4 w-4" />
                        <span>Name (A-Z)</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy('name-desc')}>
                        <ArrowUpAZ className="mr-2 h-4 w-4" />
                        <span>Name (Z-A)</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => setSortBy('plays-high')}>
                        <span>Most Plays</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy('plays-low')}>
                        <span>Least Plays</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy('conversion-high')}>
                        <span>Highest Conversion</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy('conversion-low')}>
                        <span>Lowest Conversion</span>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
                <div className="flex border rounded-md" style={{ borderColor: 'var(--border)' }}>
                  <button
                    className={`p-2 ${view === 'grid' ? 'bg-gray-100' : ''}`}
                    onClick={() => setView('grid')}
                    aria-label="Grid view"
                  >
                    <Grid className="h-4 w-4" style={{ color: 'var(--text-primary)' }} />
                  </button>
                  <button
                    className={`p-2 ${view === 'list' ? 'bg-gray-100' : ''}`}
                    onClick={() => setView('list')}
                    aria-label="List view"
                  >
                    <FileText className="h-4 w-4" style={{ color: 'var(--text-primary)' }} />
                  </button>
                  <button
                    className={`p-2 ${view === 'compact' ? 'bg-gray-100' : ''}`}
                    onClick={() => setView('compact')}
                    aria-label="Compact view"
                  >
                    <Layers className="h-4 w-4" style={{ color: 'var(--text-primary)' }} />
                  </button>
                </div>
              </div>

              {(filters.status.length > 0 || filters.type.length > 0 || filters.dateRange !== 'all') && (
                <div className="mb-4 flex flex-wrap gap-2 items-center">
                  <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                    Active filters:
                  </span>
                  {filters.status.map((status) => (
                    <Badge key={status} variant="outline" className="flex items-center gap-1">
                      Status: {status}
                      <button
                        onClick={() =>
                          updateFilters({
                            status: filters.status.filter((s) => s !== status),
                          })
                        }
                        className="ml-1 h-4 w-4 rounded-full hover:bg-gray-200 flex items-center justify-center"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                  {filters.type.map((type) => (
                    <Badge key={type} variant="outline" className="flex items-center gap-1">
                      Type: {type}
                      <button
                        onClick={() =>
                          updateFilters({
                            type: filters.type.filter((t) => t !== type),
                          })
                        }
                        className="ml-1 h-4 w-4 rounded-full hover:bg-gray-200 flex items-center justify-center"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                  {filters.dateRange !== 'all' && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      Date: {filters.dateRange === '30days' ? 'Last 30 days' : 'Last 90 days'}
                      <button
                        onClick={() => updateFilters({ dateRange: 'all' })}
                        className="ml-1 h-4 w-4 rounded-full hover:bg-gray-200 flex items-center justify-center"
                      >
                        ×
                      </button>
                    </Badge>
                  )}
                  <Button variant="ghost" size="sm" onClick={resetFilters} className="text-xs h-7">
                    Clear all
                  </Button>
                </div>
              )}

              {selectedGames.length > 0 && (
                <div className="mb-4 p-3 bg-primary/5 rounded-lg flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={selectedGames.length === filteredGames.length}
                      onCheckedChange={selectAllGames}
                      id="select-all"
                    />
                    <label
                      htmlFor="select-all"
                      className="text-sm font-medium"
                      style={{ color: 'var(--text-primary)' }}
                    >
                      {selectedGames.length} games selected
                    </label>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="h-8" onClick={() => handleBatchAction('duplicate')}>
                      <Copy className="h-4 w-4 mr-1" />
                      Duplicate
                    </Button>
                    <Button variant="outline" size="sm" className="h-8" onClick={() => handleBatchAction('export')}>
                      <Download className="h-4 w-4 mr-1" />
                      Export
                    </Button>
                    <Button variant="destructive" size="sm" className="h-8" onClick={() => handleBatchAction('delete')}>
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              )}

              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                </div>
              ) : filteredGames.length === 0 ? (
                <NoGamesPlaceholder onCreateNew={() => router.push('/create-game')} />
              ) : (
                <>
                  {view === 'grid' && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredGames.map((game) => (
                        <GameCard
                          key={game.id}
                          game={game}
                          isSelected={selectedGames.includes(game.id)}
                          onSelect={() => toggleGameSelection(game.id)}
                          onViewDetails={() => handleViewGameDetails(game)}
                        />
                      ))}
                    </div>
                  )}
                  {view === 'list' && (
                    <Card>
                      <CardContent className="p-0">
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead>
                              <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                                <th className="p-3 text-left w-10">
                                  <Checkbox
                                    checked={selectedGames.length > 0 && selectedGames.length === filteredGames.length}
                                    onCheckedChange={selectAllGames}
                                    aria-label="Select all games"
                                  />
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Game Name
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Type
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Plays
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Conversions
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Rate
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Status
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Created
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Actions
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {filteredGames.map((game) => (
                                <GameListItem
                                  key={game.id}
                                  game={game}
                                  isSelected={selectedGames.includes(game.id)}
                                  onSelect={() => toggleGameSelection(game.id)}
                                  onViewDetails={() => handleViewGameDetails(game)}
                                />
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                  {view === 'compact' && (
                    <Tabs defaultValue="all" className="w-full">
                      <TabsList className="mb-4">
                        <TabsTrigger value="all">All Games</TabsTrigger>
                        <TabsTrigger value="active">Active</TabsTrigger>
                        <TabsTrigger value="inactive">Inactive</TabsTrigger>
                        <TabsTrigger value="wheel">Wheel Games</TabsTrigger>
                        <TabsTrigger value="lever">Lever Games</TabsTrigger>
                        <TabsTrigger value="envelope">Envelope Games</TabsTrigger>
                      </TabsList>
                      <TabsContent value="all" className="mt-0">
                        <Card>
                          <table className="w-full">
                            <thead>
                              <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                                <th className="p-3 text-left w-10">
                                  <Checkbox
                                    checked={selectedGames.length > 0 && selectedGames.length === filteredGames.length}
                                    onCheckedChange={selectAllGames}
                                    aria-label="Select all games"
                                  />
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Game
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Plays
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Conv. Rate
                                </th>
                                <th
                                  className="text-center p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Status
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Actions
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {filteredGames.map((game) => (
                                <tr
                                  key={game.id}
                                  className="border-b hover:bg-gray-50"
                                  style={{ borderColor: 'var(--border)' }}
                                >
                                  <td className="p-3">
                                    <Checkbox
                                      checked={selectedGames.includes(game.id)}
                                      onCheckedChange={() => toggleGameSelection(game.id)}
                                      aria-label={`Select ${game.name}`}
                                    />
                                  </td>
                                  <td className="p-3">
                                    <div className="flex items-center gap-3">
                                      <div
                                        className="w-8 h-8 rounded-full flex items-center justify-center"
                                        style={{
                                          backgroundColor:
                                            game.type === 'wheel'
                                              ? 'rgba(139, 92, 246, 0.2)'
                                              : game.type === 'lever'
                                              ? 'rgba(249, 115, 22, 0.2)'
                                              : 'rgba(132, 204, 22, 0.2)',
                                        }}
                                      >
                                        {game.type === 'wheel' ? (
                                          <div className="h-4 w-4 rounded-full border-2 border-purple-500 border-t-transparent animate-spin-slow" />
                                        ) : game.type === 'lever' ? (
                                          <div className="h-4 w-1 bg-orange-500" />
                                        ) : (
                                          <div className="h-3 w-3 bg-green-500 rounded-sm" />
                                        )}
                                      </div>
                                      <div>
                                        <p
                                          className="font-medium text-sm"
                                          style={{
                                            color: 'var(--text-primary)',
                                          }}
                                        >
                                          {game.name}
                                        </p>
                                        <p
                                          className="text-xs capitalize"
                                          style={{
                                            color: 'var(--text-secondary)',
                                          }}
                                        >
                                          {game.type}
                                        </p>
                                      </div>
                                    </div>
                                  </td>
                                  <td className="p-3 text-right">
                                    <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                      {game.plays.toLocaleString()}
                                    </p>
                                  </td>
                                  <td className="p-3 text-right">
                                    <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                      {game.plays > 0 ? Math.round((game.conversions / game.plays) * 100) : 0}%
                                    </p>
                                  </td>
                                  <td className="p-3 text-center">
                                    <span
                                      className={`px-2 py-1 rounded-full text-xs ${
                                        game.status === 'active'
                                          ? 'bg-green-100 text-green-800'
                                          : 'bg-gray-100 text-gray-800'
                                      }`}
                                    >
                                      {game.status}
                                    </span>
                                  </td>
                                  <td className="p-3 text-right">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 text-xs"
                                      onClick={() => handleViewGameDetails(game)}
                                    >
                                      View Details
                                    </Button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </Card>
                      </TabsContent>
                      {/* Other TabsContent sections remain unchanged */}
                      <TabsContent value="active" className="mt-0">
                        <Card>
                          <table className="w-full">
                            <thead>
                              <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                                <th className="p-3 text-left w-10">
                                  <Checkbox
                                    checked={
                                      selectedGames.length > 0 &&
                                      selectedGames.length ===
                                        filteredGames.filter((game) => game.status === 'active').length
                                    }
                                    onCheckedChange={() => {
                                      const activeGames = filteredGames.filter((game) => game.status === 'active');
                                      if (selectedGames.length === activeGames.length) {
                                        setSelectedGames([]);
                                      } else {
                                        setSelectedGames(activeGames.map((game) => game.id));
                                      }
                                    }}
                                    aria-label="Select all active games"
                                  />
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Game
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Plays
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Conv. Rate
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Actions
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {filteredGames
                                .filter((game) => game.status === 'active')
                                .map((game) => (
                                  <tr
                                    key={game.id}
                                    className="border-b hover:bg-gray-50"
                                    style={{ borderColor: 'var(--border)' }}
                                  >
                                    <td className="p-3">
                                      <Checkbox
                                        checked={selectedGames.includes(game.id)}
                                        onCheckedChange={() => toggleGameSelection(game.id)}
                                        aria-label={`Select ${game.name}`}
                                      />
                                    </td>
                                    <td className="p-3">
                                      <div className="flex items-center gap-3">
                                        <div
                                          className="w-8 h-8 rounded-full flex items-center justify-center"
                                          style={{
                                            backgroundColor:
                                              game.type === 'wheel'
                                                ? 'rgba(139, 92, 246, 0.2)'
                                                : game.type === 'lever'
                                                ? 'rgba(249, 115, 22, 0.2)'
                                                : 'rgba(132, 204, 22, 0.2)',
                                          }}
                                        >
                                          {game.type === 'wheel' ? (
                                            <div className="h-4 w-4 rounded-full border-2 border-purple-500 border-t-transparent animate-spin-slow" />
                                          ) : game.type === 'lever' ? (
                                            <div className="h-4 w-1 bg-orange-500" />
                                          ) : (
                                            <div className="h-3 w-3 bg-green-500 rounded-sm" />
                                          )}
                                        </div>
                                        <div>
                                          <p
                                            className="font-medium text-sm"
                                            style={{
                                              color: 'var(--text-primary)',
                                            }}
                                          >
                                            {game.name}
                                          </p>
                                          <p
                                            className="text-xs capitalize"
                                            style={{
                                              color: 'var(--text-secondary)',
                                            }}
                                          >
                                            {game.type}
                                          </p>
                                        </div>
                                      </div>
                                    </td>
                                    <td className="p-3 text-right">
                                      <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                        {game.plays.toLocaleString()}
                                      </p>
                                    </td>
                                    <td className="p-3 text-right">
                                      <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                        {game.plays > 0 ? Math.round((game.conversions / game.plays) * 100) : 0}%
                                      </p>
                                    </td>
                                    <td className="p-3 text-right">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-8 text-xs"
                                        onClick={() => handleViewGameDetails(game)}
                                      >
                                        View Details
                                      </Button>
                                    </td>
                                  </tr>
                                ))}
                            </tbody>
                          </table>
                        </Card>
                      </TabsContent>
                      <TabsContent value="inactive" className="mt-0">
                        <Card>
                          <table className="w-full">
                            <thead>
                              <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                                <th className="p-3 text-left w-10">
                                  <Checkbox aria-label="Select all inactive games" />
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Game
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Plays
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Conv. Rate
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Actions
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {filteredGames
                                .filter((game) => game.status === 'inactive')
                                .map((game) => (
                                  <tr
                                    key={game.id}
                                    className="border-b hover:bg-gray-50"
                                    style={{ borderColor: 'var(--border)' }}
                                  >
                                    <td className="p-3">
                                      <Checkbox
                                        checked={selectedGames.includes(game.id)}
                                        onCheckedChange={() => toggleGameSelection(game.id)}
                                        aria-label={`Select ${game.name}`}
                                      />
                                    </td>
                                    <td className="p-3">
                                      <div className="flex items-center gap-3">
                                        <div
                                          className="w-8 h-8 rounded-full flex items-center justify-center"
                                          style={{
                                            backgroundColor:
                                              game.type === 'wheel'
                                                ? 'rgba(139, 92, 246, 0.2)'
                                                : game.type === 'lever'
                                                ? 'rgba(249, 115, 22, 0.2)'
                                                : 'rgba(132, 204, 22, 0.2)',
                                          }}
                                        >
                                          {game.type === 'wheel' ? (
                                            <div className="h-4 w-4 rounded-full border-2 border-purple-500 border-t-transparent animate-spin-slow" />
                                          ) : game.type === 'lever' ? (
                                            <div className="h-4 w-1 bg-orange-500" />
                                          ) : (
                                            <div className="h-3 w-3 bg-green-500 rounded-sm" />
                                          )}
                                        </div>
                                        <div>
                                          <p
                                            className="font-medium text-sm"
                                            style={{
                                              color: 'var(--text-primary)',
                                            }}
                                          >
                                            {game.name}
                                          </p>
                                          <p
                                            className="text-xs capitalize"
                                            style={{
                                              color: 'var(--text-secondary)',
                                            }}
                                          >
                                            {game.type}
                                          </p>
                                        </div>
                                      </div>
                                    </td>
                                    <td className="p-3 text-right">
                                      <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                        {game.plays.toLocaleString()}
                                      </p>
                                    </td>
                                    <td className="p-3 text-right">
                                      <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                        {game.plays > 0 ? Math.round((game.conversions / game.plays) * 100) : 0}%
                                      </p>
                                    </td>
                                    <td className="p-3 text-right">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-8 text-xs"
                                        onClick={() => handleViewGameDetails(game)}
                                      >
                                        View Details
                                      </Button>
                                    </td>
                                  </tr>
                                ))}
                            </tbody>
                          </table>
                        </Card>
                      </TabsContent>
                      <TabsContent value="wheel" className="mt-0">
                        <Card>
                          <table className="w-full">
                            <thead>
                              <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                                <th className="p-3 text-left w-10">
                                  <Checkbox aria-label="Select all wheel games" />
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Game
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Plays
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Conv. Rate
                                </th>
                                <th
                                  className="text-center p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Status
                                </th>
                                <th
                                  className="text-right p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Actions
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {filteredGames
                                .filter((game) => game.type === 'wheel')
                                .map((game) => (
                                  <tr
                                    key={game.id}
                                    className="border-b hover:bg-gray-50"
                                    style={{ borderColor: 'var(--border)' }}
                                  >
                                    <td className="p-3">
                                      <Checkbox
                                        checked={selectedGames.includes(game.id)}
                                        onCheckedChange={() => toggleGameSelection(game.id)}
                                        aria-label={`Select ${game.name}`}
                                      />
                                    </td>
                                    <td className="p-3">
                                      <div className="flex items-center gap-3">
                                        <div
                                          className="w-8 h-8 rounded-full flex items-center justify-center"
                                          style={{
                                            backgroundColor: 'rgba(139, 92, 246, 0.2)',
                                          }}
                                        >
                                          <div className="h-4 w-4 rounded-full border-2 border-purple-500 border-t-transparent animate-spin-slow" />
                                        </div>
                                        <p
                                          className="font-medium text-sm"
                                          style={{
                                            color: 'var(--text-primary)',
                                          }}
                                        >
                                          {game.name}
                                        </p>
                                      </div>
                                    </td>
                                    <td className="p-3 text-right">
                                      <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                        {game.plays.toLocaleString()}
                                      </p>
                                    </td>
                                    <td className="p-3 text-right">
                                      <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                                        {game.plays > 0 ? Math.round((game.conversions / game.plays) * 100) : 0}%
                                      </p>
                                    </td>
                                    <td className="p-3 text-center">
                                      <span
                                        className={`px-2 py-1 rounded-full text-xs ${
                                          game.status === 'active'
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-gray-100 text-gray-800'
                                        }`}
                                      >
                                        {game.status}
                                      </span>
                                    </td>
                                    <td className="p-3 text-right">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-8 text-xs"
                                        onClick={() => handleViewGameDetails(game)}
                                      >
                                        View Details
                                      </Button>
                                    </td>
                                  </tr>
                                ))}
                            </tbody>
                          </table>
                        </Card>
                      </TabsContent>
                    </Tabs>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Game Detail Modal */}
      {selectedGame && (
        <GameDetailModal
          game={selectedGame}
          onClose={() => setSelectedGame(null)}
          onGameDeleted={fetchGames} // Pass callback to refresh games
          onUpdate={(updatedGame) => {
            // Update the game in the local state
            setGames((prevGames) => prevGames.map((game) => (game.id === updatedGame.id ? updatedGame : game)));
            setSelectedGame(updatedGame);
          }}
        />
      )}
    </div>
  );
}
