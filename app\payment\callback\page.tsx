'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckCircle, XCircle, Loader2, CreditCard, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { formatPrice } from '@/lib/pricing-config';

interface PaymentResult {
  success: boolean;
  message: string;
  refId?: number;
  credits?: number;
  gameId?: string;
  gameLink?: string;
  error?: string;
}

export default function PaymentCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(true);
  const [result, setResult] = useState<PaymentResult | null>(null);

  // Get URL parameters
  const authority = searchParams.get('Authority');
  const status = searchParams.get('Status');

  useEffect(() => {
    const verifyPayment = async () => {
      if (!authority) {
        setResult({
          success: false,
          message: 'کد تراکنش معتبر نیست',
          error: 'Missing authority parameter'
        });
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch('/api/payment/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            authority,
            status: status || 'OK',
          }),
        });

        const data: PaymentResult = await response.json();
        setResult(data);

        // Show toast notification
        if (data.success) {
          toast({
            title: 'پرداخت موفق',
            description: data.message,
          });

          // Dispatch credit update event if credits were added
          if (data.credits) {
            window.dispatchEvent(
              new CustomEvent('credits-updated', {
                detail: { credits: data.credits },
              })
            );
          }
        } else {
          toast({
            title: 'پرداخت ناموفق',
            description: data.message,
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Payment verification error:', error);
        setResult({
          success: false,
          message: 'خطا در تأیید پرداخت. لطفاً دوباره تلاش کنید.',
          error: 'Network or server error'
        });

        toast({
          title: 'خطا',
          description: 'خطا در تأیید پرداخت. لطفاً دوباره تلاش کنید.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    verifyPayment();
  }, [authority, status, toast]);

  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  const handleGoToCredits = () => {
    router.push('/dashboard/credits');
  };

  const handleGoToGame = () => {
    if (result?.gameLink) {
      window.open(result.gameLink, '_blank');
    }
  };

  const handleGoToGameSettings = () => {
    if (result?.gameId) {
      router.push(`/dashboard/games/${result.gameId}/settings`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
            </div>
            <CardTitle>در حال تأیید پرداخت...</CardTitle>
            <CardDescription>
              لطفاً صبر کنید تا پرداخت شما تأیید شود
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className={`mx-auto mb-4 w-16 h-16 rounded-full flex items-center justify-center ${
            result?.success 
              ? 'bg-green-100' 
              : 'bg-red-100'
          }`}>
            {result?.success ? (
              <CheckCircle className="w-8 h-8 text-green-600" />
            ) : (
              <XCircle className="w-8 h-8 text-red-600" />
            )}
          </div>
          <CardTitle className={result?.success ? 'text-green-700' : 'text-red-700'}>
            {result?.success ? 'پرداخت موفق!' : 'پرداخت ناموفق'}
          </CardTitle>
          <CardDescription>
            {result?.message}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Success Details */}
          {result?.success && (
            <div className="space-y-3">
              {result.refId && (
                <Alert>
                  <CreditCard className="h-4 w-4" />
                  <AlertDescription>
                    شماره تراکنش: <strong>{result.refId}</strong>
                  </AlertDescription>
                </Alert>
              )}

              {result.credits && (
                <Alert>
                  <AlertDescription>
                    <strong>{result.credits}</strong> اعتبار به حساب شما اضافه شد
                  </AlertDescription>
                </Alert>
              )}

              {result.gameId && (
                <Alert>
                  <AlertDescription>
                    بازی شما با موفقیت ایجاد شد
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            {result?.success ? (
              <>
                {result.gameLink && (
                  <Button 
                    onClick={handleGoToGame}
                    className="w-full"
                    variant="default"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    مشاهده بازی
                  </Button>
                )}

                {result.gameId && (
                  <Button 
                    onClick={handleGoToGameSettings}
                    className="w-full"
                    variant="outline"
                  >
                    تنظیمات بازی
                  </Button>
                )}

                {result.credits && (
                  <Button 
                    onClick={handleGoToCredits}
                    className="w-full"
                    variant="outline"
                  >
                    مشاهده اعتبارات
                  </Button>
                )}

                <Button 
                  onClick={handleGoToDashboard}
                  className="w-full"
                  variant="secondary"
                >
                  بازگشت به داشبورد
                </Button>
              </>
            ) : (
              <>
                <Button 
                  onClick={handleGoToCredits}
                  className="w-full"
                  variant="default"
                >
                  تلاش مجدد
                </Button>
                <Button 
                  onClick={handleGoToDashboard}
                  className="w-full"
                  variant="outline"
                >
                  بازگشت به داشبورد
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
