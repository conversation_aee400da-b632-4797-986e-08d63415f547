import { NextResponse } from 'next/server';

export async function GET() {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const redirectUri = `${baseUrl}/api/auth/callback/google`;
  
  const debugInfo = {
    environment: {
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID ? 
        `${process.env.GOOGLE_CLIENT_ID.substring(0, 20)}...` : 
        'NOT SET',
      GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET ? 
        'SET (hidden)' : 
        'NOT SET',
    },
    expectedRedirectUri: redirectUri,
    instructions: [
      '1. Copy the expectedRedirectUri above',
      '2. Go to Google Cloud Console',
      '3. Navigate to APIs & Services → Credentials',
      '4. Edit your OAuth 2.0 Client ID',
      '5. Add the redirect URI to "Authorized redirect URIs"',
      '6. Save the configuration',
      '7. Wait 5-10 minutes for changes to propagate'
    ],
    troubleshooting: {
      '403_forbidden': 'Redirect URI mismatch - check Google Cloud Console configuration',
      'invalid_client': 'Check GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET',
      'access_denied': 'User denied access or OAuth consent screen issues'
    }
  };

  return NextResponse.json(debugInfo, { 
    headers: {
      'Content-Type': 'application/json',
    }
  });
}
