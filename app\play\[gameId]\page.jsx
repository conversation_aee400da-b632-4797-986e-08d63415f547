'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import PhoneVerificationForm from '@/components/game/PhoneVerificationForm';
import CodeVerificationForm from '@/components/game/CodeVerificationForm';
import GameDisplay from '@/components/game/GameDisplay';
import CongratulationsMessage from '@/components/game/CongratulationsMessage';
import { Loader2 } from 'lucide-react';
import WheelGameBackground from '@/components/game/backgrounds/WheelGameBackground';
import LeverGameBackground from '@/components/game/backgrounds/LeverGameBackground';
import EnvelopeGameBackground from '@/components/game/backgrounds/EnvelopeGameBackground';
import { useSessionTracking } from '@/hooks/useSessionTracking';

// Function to fetch game data from the API
const fetchGameData = async (gameId) => {
  try {
    console.log('Client: Starting to fetch game data for:', gameId);

    // The format is: gameType-instagramHandle-gameId
    const parts = gameId.split('-');

    // If the URL has the expected format with at least 3 parts (type-handle-id)
    if (parts.length >= 3) {
      // Use the full gameId for the API call - the API will extract the actual ID
      console.log('Client: Using full game ID format:', gameId);
    } else {
      console.log('Client: Using direct game ID:', gameId);
    }

    // Call the API to get the game data - use the full gameId
    console.log('Client: Fetching from API:', `/api/games/${gameId}`);
    const response = await fetch(`/api/games/${gameId}`);

    if (!response.ok) {
      console.error('Client: API response not OK:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Client: Error response:', errorText);
      throw new Error(`Failed to fetch game data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Client: Game data received:', data);

    // Record a play via the play endpoint
    try {
      console.log('Client: Recording play');
      const playResponse = await fetch(`/api/games/${gameId}/play`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!playResponse.ok) {
        console.error('Client: Failed to record play:', playResponse.status, playResponse.statusText);
        const errorText = await playResponse.text();
        console.error('Client: Play error response:', errorText);
      } else {
        console.log('Client: Play recorded successfully');
      }
    } catch (playError) {
      console.error('Client: Failed to record play:', playError);
    }

    if (!data.game) {
      console.error('Client: No game data in response');
      throw new Error('Game data not found in response');
    }

    return data.game;
  } catch (error) {
    console.error('Client: Error fetching game data:', error);
    throw error;
  }
};

export default function GamePage() {
  const router = useRouter();
  const params = useParams();
  const gameId = params?.gameId;
  const [stage, setStage] = useState('loading');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [gameData, setGameData] = useState(null);
  const [prize, setPrize] = useState('');
  const [error, setError] = useState(null);
  const [playRecorded, setPlayRecorded] = useState(false);
  const [gameStartTime, setGameStartTime] = useState(null);

  // Session tracking
  const { sessionId, isActive, endSession } = useSessionTracking({
    gameId: gameId || '',
    onSessionStart: (sessionId) => {
      console.log('🎮 Game session started:', sessionId, 'for game:', gameId);
    },
    onSessionEnd: (sessionId, duration) => {
      console.log('🏁 Game session ended:', sessionId, 'Duration:', duration, 'seconds', 'for game:', gameId);
    },
    onError: (error) => {
      console.error('❌ Session tracking error:', error);
    },
  });

  // Debug session tracking
  useEffect(() => {
    console.log('📊 Session tracking status:', {
      sessionId,
      isActive,
      gameId,
    });
  }, [sessionId, isActive, gameId]);

  // Handle page unload for abandoned sessions
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (gameStartTime && gameData?._id && stage !== 'congratulations') {
        const playDuration = Math.floor((Date.now() - gameStartTime) / 1000);
        console.log('🚪 Page unload - recording abandoned session, duration:', playDuration);

        // Record abandoned session
        const gameIdStr = gameData._id.toString ? gameData._id.toString() : gameData._id;
        const abandonedSessionId = `abandoned_${Date.now()}_${Math.random().toString(36).substring(2)}`;

        // Use sendBeacon for reliable tracking on page unload
        const playTimeData = JSON.stringify({
          playDuration: playDuration,
          sessionId: abandonedSessionId,
          exitReason: 'abandoned',
        });

        if (navigator.sendBeacon) {
          // Use the dedicated play-time API for abandoned sessions
          navigator.sendBeacon(`/api/games/${gameIdStr}/play-time`, playTimeData);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [gameStartTime, gameData, stage]);

  useEffect(() => {
    console.log('Client: GamePage mounted, gameId:', gameId);

    const loadGameData = async () => {
      try {
        if (!gameId) {
          console.error('Client: Game ID is missing');
          throw new Error('Game ID is missing');
        }

        const data = await fetchGameData(gameId);
        console.log('Client: Setting game data:', data);
        setGameData(data);
        setPlayRecorded(true);
        setGameStartTime(Date.now()); // Record when game actually starts
        setStage('phone');
        console.log('🎯 Game start time recorded:', new Date());
      } catch (error) {
        console.error('Client: Failed to load game data:', error);
        setError(`Failed to load game: ${error.message}`);
      }
    };

    loadGameData();
  }, [gameId]);

  // Rest of the component remains the same...
  // ... (keeping the rest of the component unchanged)

  const handlePhoneSubmit = async (phone) => {
    setPhoneNumber(phone);
    // In a real app, this would call an API to send a verification code
    console.log(`Client: Sending verification code to ${phone}`);

    // Record this interaction in the database
    if (gameData?._id) {
      try {
        const gameIdStr = gameData._id.toString ? gameData._id.toString() : gameData._id;
        console.log('Client: Recording phone interaction for game:', gameIdStr);

        await fetch(`/api/games/${gameIdStr}/interaction`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'phone_verification',
            phoneNumber: phone,
          }),
        });
      } catch (error) {
        console.error('Client: Failed to record interaction:', error);
      }
    }

    setStage('code');
  };

  const handleCodeVerify = async () => {
    // In a real app, this would verify the code with an API
    console.log('Client: Code verified successfully');

    // Record this interaction in the database
    if (gameData?._id) {
      try {
        const gameIdStr = gameData._id.toString ? gameData._id.toString() : gameData._id;
        console.log('Client: Recording code interaction for game:', gameIdStr);

        await fetch(`/api/games/${gameIdStr}/interaction`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'code_verification',
            phoneNumber: phoneNumber,
          }),
        });
      } catch (error) {
        console.error('Client: Failed to record interaction:', error);
      }
    }

    setStage('game');
  };

  const handleGameComplete = async (wonPrize) => {
    setPrize(wonPrize);

    // Calculate actual play time
    const playDuration = gameStartTime ? Math.floor((Date.now() - gameStartTime) / 1000) : 0;
    console.log('🎮 Game completed! Play duration:', playDuration, 'seconds');

    // Record this conversion in the database
    if (gameData?._id) {
      try {
        const gameIdStr = gameData._id.toString ? gameData._id.toString() : gameData._id;
        console.log('Client: Recording conversion for game:', gameIdStr, 'with play time:', playDuration);

        await fetch(`/api/games/${gameIdStr}/conversion`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prize: wonPrize,
            phoneNumber: phoneNumber,
            playDuration: playDuration, // Include actual play time
          }),
        });
      } catch (error) {
        console.error('Client: Failed to record conversion:', error);
      }
    }

    // Record play time using the dedicated API endpoint
    if (gameStartTime && gameData?._id && playDuration > 0) {
      try {
        const gameIdStr = gameData._id.toString ? gameData._id.toString() : gameData._id;

        console.log('💾 Recording play time via dedicated API:', gameIdStr, 'duration:', playDuration);

        const response = await fetch(`/api/games/${gameIdStr}/play-time`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            playDuration: playDuration,
            phoneNumber: phoneNumber,
            sessionId: sessionId || `completed_${Date.now()}_${Math.random().toString(36).substring(2)}`,
            exitReason: 'completed',
          }),
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Play time recorded successfully:', data);
        } else {
          console.error('❌ Failed to record play time:', response.statusText);
        }
      } catch (error) {
        console.error('❌ Error recording play time:', error);
      }
    }

    // Also end the automatic session if it's active
    if (isActive && sessionId) {
      console.log('🏁 Ending automatic session tracking');
      endSession('completed', phoneNumber);
    }

    // Here is where we would ideally send the SMS. Since I don't have access to the SMS sending functionality, I will leave a comment indicating where it should be placed.
    // In a real application, you would use a service like Twilio or AWS SNS to send the SMS.
    // The message should include the prize, game name, and Instagram page name.
    // Example:
    // const message = `Congratulations! You won ${wonPrize} playing ${gameData?.name}! Visit our Instagram page @${gameData?.instagramHandle} for more chances to win!`;
    // await sendSMS(phoneNumber, message);

    setStage('congratulations');
  };

  // Render the appropriate game background based on game type
  const renderGameBackground = () => {
    if (!gameData) return null;

    const primaryColor =
      gameData.colorScheme === 'purple' ? '#8b5cf6' : gameData.colorScheme === 'blue' ? '#3b82f6' : '#f97316';
    const secondaryColor =
      gameData.colorScheme === 'purple' ? '#c4b5fd' : gameData.colorScheme === 'blue' ? '#93c5fd' : '#fdba74';

    switch (gameData.type) {
      case 'wheel':
        return <WheelGameBackground primaryColor={primaryColor} secondaryColor={secondaryColor} />;
      case 'lever':
        return <LeverGameBackground primaryColor={primaryColor} secondaryColor={secondaryColor} />;
      case 'envelope':
        return <EnvelopeGameBackground primaryColor={primaryColor} secondaryColor={secondaryColor} />;
      default:
        return null;
    }
  };

  console.log('Client: Current stage:', stage);
  console.log('Client: Game data:', gameData);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-purple-500 to-pink-500">
        <div className="bg-white/80 backdrop-blur-sm p-8 rounded-lg shadow-xl flex flex-col items-center">
          <h2 className="text-xl font-bold text-red-500 mb-4">Error</h2>
          <p className="text-center mb-4">{error}</p>
          <Button onClick={() => router.push('/')}>Return Home</Button>
        </div>
      </div>
    );
  }

  if (stage === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-purple-500 to-pink-500">
        <div className="bg-white/80 backdrop-blur-sm p-8 rounded-lg shadow-xl flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
          <h2 className="text-xl font-bold">Loading Game...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Game Background */}
      <div className="absolute inset-0 z-0">{renderGameBackground()}</div>

      {/* Content */}
      <div className="relative z-10 w-[92%] max-w-md mx-auto px-4">
        <AnimatePresence mode="wait">
          {stage === 'phone' && (
            <motion.div
              key="phone"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <PhoneVerificationForm onSubmit={handlePhoneSubmit} gameName={gameData?.name} />
            </motion.div>
          )}

          {stage === 'code' && (
            <motion.div
              key="code"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <CodeVerificationForm
                onVerify={handleCodeVerify}
                phoneNumber={phoneNumber}
                onBack={() => setStage('phone')}
              />
            </motion.div>
          )}

          {stage === 'game' && (
            <motion.div
              key="game"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{
                duration: 0.5,
                type: 'spring',
                stiffness: 100,
                damping: 15,
              }}
              className="w-full"
            >
              <GameDisplay
                gameData={{
                  ...gameData,
                  id: params.gameId, // Pass the game ID to the component
                }}
                onComplete={handleGameComplete}
              />
            </motion.div>
          )}

          {stage === 'congratulations' && (
            <motion.div
              key="congrats"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
            >
              <CongratulationsMessage
                prize={prize}
                phoneNumber={phoneNumber}
                gameName={gameData?.name}
                gameId={params.gameId}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
