"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"

import type React from "react"

import { useState } from "react"
import { AlertCircle, Check, Loader2, Shield, Smartphone } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"

export default function SecuritySettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [sessionTimeout, setSessionTimeout] = useState(30)

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData((prev) => ({ ...prev, [name]: value }))
  }

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation password must match.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Password updated",
        description: "Your password has been updated successfully.",
      })
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      })
    }, 1000)
  }

  const handleEnableTwoFactor = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setTwoFactorEnabled(true)
      toast({
        title: "Two-factor authentication enabled",
        description: "Your account is now more secure with 2FA.",
      })
    }, 1000)
  }

  const handleDisableTwoFactor = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setTwoFactorEnabled(false)
      toast({
        title: "Two-factor authentication disabled",
        description: "Two-factor authentication has been disabled for your account.",
      })
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-1" style={{ color: "var(--text-primary)" }}>
          Security Settings
        </h2>
        <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
          Manage your account security and authentication methods
        </p>
      </div>

      {/* Password Change */}
      <div className="space-y-4">
        <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
          Change Password
        </h3>
        <Separator />

        <form onSubmit={handlePasswordSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentPassword" className="font-medium">
              Current Password
            </Label>
            <Input
              id="currentPassword"
              name="currentPassword"
              type="password"
              value={passwordData.currentPassword}
              onChange={handlePasswordChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="newPassword" className="font-medium">
              New Password
            </Label>
            <Input
              id="newPassword"
              name="newPassword"
              type="password"
              value={passwordData.newPassword}
              onChange={handlePasswordChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="font-medium">
              Confirm New Password
            </Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              value={passwordData.confirmPassword}
              onChange={handlePasswordChange}
              required
            />
          </div>

          <div className="pt-2">
            <Button type="submit" disabled={isLoading} style={{ backgroundColor: "var(--primary)" }}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Password"
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Two-Factor Authentication */}
      <div className="space-y-4">
        <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
          Two-Factor Authentication (2FA)
        </h3>
        <Separator />

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-green-100 p-2 rounded-md">
              <Smartphone className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="font-medium" style={{ color: "var(--text-primary)" }}>
                Two-Factor Authentication
              </p>
              <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                {twoFactorEnabled
                  ? "Your account is protected with 2FA"
                  : "Add an extra layer of security to your account"}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {twoFactorEnabled ? (
              <Button variant="outline" size="sm" onClick={handleDisableTwoFactor} disabled={isLoading}>
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Disable"}
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={handleEnableTwoFactor}
                disabled={isLoading}
                style={{ backgroundColor: "var(--primary)" }}
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Enable"}
              </Button>
            )}
            <Switch checked={twoFactorEnabled} onCheckedChange={setTwoFactorEnabled} disabled={isLoading} />
          </div>
        </div>

        {twoFactorEnabled && (
          <div className="mt-4 p-4 border rounded-md" style={{ borderColor: "var(--border)" }}>
            <div className="flex items-center gap-2 mb-2">
              <Check className="h-5 w-5 text-green-500" />
              <span className="font-medium">Two-Factor Authentication is enabled</span>
            </div>
            <p className="text-sm mb-4" style={{ color: "var(--text-secondary)" }}>
              Your account is protected with an authentication app. You'll need to enter a code from your app whenever
              you sign in.
            </p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                Change App
              </Button>
              <Button variant="outline" size="sm">
                View Recovery Codes
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Session Management */}
      <div className="space-y-4">
        <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
          Session Management
        </h3>
        <Separator />

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="sessionTimeout" className="font-medium">
              Session Timeout (minutes)
            </Label>
            <Input
              id="sessionTimeout"
              type="number"
              min="5"
              max="120"
              value={sessionTimeout}
              onChange={(e) => setSessionTimeout(Number.parseInt(e.target.value))}
            />
            <p className="text-xs" style={{ color: "var(--text-secondary)" }}>
              Your session will automatically expire after this period of inactivity
            </p>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Active Sessions</AlertTitle>
            <AlertDescription>
              You currently have 2 active sessions. You can review and terminate any suspicious sessions.
            </AlertDescription>
          </Alert>

          <Button variant="outline" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Manage Active Sessions
          </Button>
        </div>
      </div>

      {/* Login History */}
      <div className="space-y-4">
        <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
          Login History
        </h3>
        <Separator />

        <div className="space-y-4">
          <div className="border rounded-md divide-y" style={{ borderColor: "var(--border)" }}>
            <div className="p-3">
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium" style={{ color: "var(--text-primary)" }}>
                    Current Session
                  </p>
                  <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                    Chrome on Windows • New York, USA
                  </p>
                </div>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Active Now
                </Badge>
              </div>
            </div>
            <div className="p-3">
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium" style={{ color: "var(--text-primary)" }}>
                    Previous Login
                  </p>
                  <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                    Safari on iPhone • New York, USA
                  </p>
                </div>
                <span className="text-xs" style={{ color: "var(--text-secondary)" }}>
                  Yesterday, 3:42 PM
                </span>
              </div>
            </div>
            <div className="p-3">
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium" style={{ color: "var(--text-primary)" }}>
                    Previous Login
                  </p>
                  <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                    Chrome on MacOS • New York, USA
                  </p>
                </div>
                <span className="text-xs" style={{ color: "var(--text-secondary)" }}>
                  April 12, 2023, 10:15 AM
                </span>
              </div>
            </div>
          </div>

          <Button variant="outline" size="sm">
            View Full Login History
          </Button>
        </div>
      </div>
    </div>
  )
}
