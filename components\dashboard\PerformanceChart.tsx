'use client';

import { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler);

interface PerformanceChartProps {
  period?: string;
}

interface ChartData {
  labels: string[];
  playsData: number[];
  conversionsData: number[];
  isSampleData?: boolean;
}

export default function PerformanceChart({ period = 'all' }: PerformanceChartProps) {
  const [chartData, setChartData] = useState<ChartData>({
    labels: [],
    playsData: [],
    conversionsData: [],
    isSampleData: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPerformanceData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/dashboard/performance?period=${period}`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch performance data');
        }
        const data = await response.json();
        // console.log\(.+\);

        // Validate the data structure
        if (
          !Array.isArray(data.labels) ||
          !Array.isArray(data.playsData) ||
          !Array.isArray(data.conversionsData) ||
          data.labels.length !== data.playsData.length ||
          data.labels.length !== data.conversionsData.length
        ) {
          throw new Error('Invalid data format: arrays are missing or mismatched');
        }

        // console.log\(.+\);
        setChartData(data);
      } catch (error) {
        console.error('Error fetching performance data:', error);
        setError(error instanceof Error ? error.message : 'Unknown error occurred');
        setChartData({
          labels: [],
          playsData: [],
          conversionsData: [],
          isSampleData: false,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPerformanceData();
  }, [period]);

  const data = {
    labels: chartData.labels,
    datasets: [
      {
        label: 'بازی‌ها',
        data: chartData.playsData,
        borderColor: '#8b5cf6',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        fill: true,
        tension: 0.4,
      },
      {
        label: 'تبدیل‌ها',
        data: chartData.conversionsData,
        borderColor: '#84cc16',
        backgroundColor: 'rgba(132, 204, 22, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <p className="text-lg font-medium mb-2 text-red-500">خطا در بارگذاری نمودار</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (chartData.labels.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <p className="text-lg font-medium mb-2">داده‌ای در دسترس نیست</p>
          <p className="text-sm text-gray-500">برای مشاهده داده‌های عملکرد، بازی‌هایی ایجاد کنید</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-[93%]">
      {chartData.isSampleData && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 rounded-md">
          <p className="text-sm">
            <span className="font-medium">داده‌های نمونه نمایش داده می‌شود.</span> این نمودار داده‌های مثال بر اساس
            بازی‌های شما را نشان می‌دهد. با تعامل کاربران با بازی‌هایتان، داده‌های واقعی اینجا ظاهر خواهد شد.
          </p>
        </div>
      )}
      <Line data={data} options={options} />
    </div>
  );
}
