"use client"

import { motion } from "framer-motion"

interface WheelGameBackgroundProps {
  primaryColor: string
  secondaryColor: string
}

export default function WheelGameBackground({ primaryColor, secondaryColor }: WheelGameBackgroundProps) {
  return (
    <div className="absolute inset-0 overflow-hidden opacity-30">
      <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent" />

      {/* Background gradient */}
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(135deg, ${primaryColor}, ${secondaryColor})`,
        }}
      />

      {/* Spinning wheels */}
      {[...Array(5)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full"
          style={{
            width: 100 + Math.random() * 200,
            height: 100 + Math.random() * 200,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            borderWidth: "8px",
            borderStyle: "solid",
            borderTop: `8px solid ${Math.random() > 0.5 ? primaryColor : secondaryColor}`,
            borderRight: `8px solid ${Math.random() > 0.5 ? primaryColor : secondaryColor}`,
            borderBottom: `8px solid ${Math.random() > 0.5 ? primaryColor : secondaryColor}`,
            borderLeft: `8px solid ${Math.random() > 0.5 ? primaryColor : secondaryColor}`,
            opacity: 0.3,
          }}
          animate={{ rotate: 360 }}
          transition={{
            duration: 10 + Math.random() * 20,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        />
      ))}

      {/* Wheel segments */}
      <motion.div
        className="absolute rounded-full"
        style={{
          width: 300,
          height: 300,
          left: "50%",
          top: "50%",
          marginLeft: -150,
          marginTop: -150,
          background: `conic-gradient(
            ${primaryColor} 0%, 
            ${primaryColor} 12.5%, 
            ${secondaryColor} 12.5%, 
            ${secondaryColor} 25%,
            ${primaryColor} 25%, 
            ${primaryColor} 37.5%,
            ${secondaryColor} 37.5%, 
            ${secondaryColor} 50%,
            ${primaryColor} 50%, 
            ${primaryColor} 62.5%,
            ${secondaryColor} 62.5%, 
            ${secondaryColor} 75%,
            ${primaryColor} 75%, 
            ${primaryColor} 87.5%,
            ${secondaryColor} 87.5%, 
            ${secondaryColor} 100%
          )`,
          opacity: 0.2,
        }}
        animate={{ rotate: 360 }}
        transition={{
          duration: 20,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
      />
    </div>
  )
}
