import { Skeleton } from "@/components/ui/skeleton"
import DashboardHeader from "@/components/dashboard/DashboardHeader"
import DashboardSidebar from "@/components/dashboard/DashboardSidebar"

export default function SettingsLoading() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <DashboardSidebar />

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Header */}
          <DashboardHeader />

          {/* Settings Content */}
          <main className="p-4 md:p-6 space-y-6">
            <div>
              <Skeleton className="h-8 w-48 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>

            <div className="flex flex-col md:flex-row gap-6">
              {/* Settings Navigation - Desktop */}
              <div className="hidden md:block w-64 flex-shrink-0">
                <Skeleton className="h-[400px] w-full rounded-lg" />
              </div>

              {/* Settings Content */}
              <div className="flex-1">
                <Skeleton className="h-[600px] w-full rounded-lg" />
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
