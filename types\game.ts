export interface Game {
  id: string;
  name: string;
  type: string;
  plays: number;
  conversions: number;
  createdAt: string;
  lastActive: string;
  status: string;
  gameInteractions?: GameInteractions;
  sessionMetrics?: SessionMetrics;
}

export interface SessionMetrics {
  totalSessions: number;
  averageSessionTime: number; // in seconds
  completionRate: number; // percentage
  lastCalculated: string;
}

export interface Prize {
  type: string;
  description: string;
  probability: number;
  quantity: number;
  remainingQuantity: number;
}

export interface GameInteractions {
  plays: PlayInteraction[];
  conversions: ConversionInteraction[];
  codeVerifications: CodeVerificationInteraction[];
  phoneVerifications: PhoneVerificationInteraction[];
  sessions: SessionInteraction[];
}

export interface SessionInteraction {
  sessionId: string;
  startTime: string;
  endTime?: string;
  duration?: number; // in seconds
  phoneNumber?: string;
  completed: boolean;
  exitReason?: 'completed' | 'abandoned' | 'error';
}

export interface PlayInteraction {
  timestamp: string;
  phoneNumber?: string;
}

export interface ConversionInteraction {
  timestamp: string;
  phoneNumber: string;
  prize: string;
}

export interface CodeVerificationInteraction {
  timestamp: string;
  phoneNumber: string;
  code?: string;
  success?: boolean;
}

export interface PhoneVerificationInteraction {
  timestamp: string;
  phoneNumber: string;
  success?: boolean;
}
