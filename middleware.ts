import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getToken } from "next-auth/jwt"

export async function middleware(request: NextRequest) {
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })
  const isAuthenticated = !!token

  // Define paths that require authentication
  const protectedPaths = ["/dashboard", "/create-game"]

  // Define authentication paths (login/signup)
  const authPaths = ["/signin", "/signup"]

  const path = request.nextUrl.pathname

  // Check if the path is protected and user is not authenticated
  const isProtectedPath = protectedPaths.some(
    (protectedPath) => path === protectedPath || path.startsWith(`${protectedPath}/`),
  )

  // Check if the path is an auth path and user is already authenticated
  const isAuthPath = authPaths.some((authPath) => path === authPath)

  // Redirect to login if trying to access protected path while not authenticated
  if (isProtectedPath && !isAuthenticated) {
    const redirectUrl = new URL("/signin", request.url)
    redirectUrl.searchParams.set("callbackUrl", encodeURI(request.url))
    return NextResponse.redirect(redirectUrl)
  }

  // Redirect to dashboard if trying to access auth paths while already authenticated
  if (isAuthPath && isAuthenticated) {
    return NextResponse.redirect(new URL("/dashboard", request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: ["/dashboard/:path*", "/create-game/:path*", "/signin", "/signup"],
}
