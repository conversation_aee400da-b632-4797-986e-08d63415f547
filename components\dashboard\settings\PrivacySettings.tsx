"use client"

import type React from "react"

import { useState } from "react"
import { Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useToast } from "@/components/ui/use-toast"

export default function PrivacySettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [privacySettings, setPrivacySettings] = useState({
    dataCollection: true,
    analytics: true,
    thirdPartySharing: false,
    marketingEmails: true,
    cookiePreference: "essential",
  })

  const handleToggle = (key: keyof typeof privacySettings) => {
    setPrivacySettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  const handleCookiePreferenceChange = (value: string) => {
    setPrivacySettings((prev) => ({
      ...prev,
      cookiePreference: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Privacy settings updated",
        description: "Your privacy preferences have been saved successfully.",
      })
    }, 1000)
  }

  const handleDataExport = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Data export requested",
        description: "We'll email you when your data export is ready for download.",
      })
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-1" style={{ color: "var(--text-primary)" }}>
          Privacy Settings
        </h2>
        <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
          Control how your data is collected and used
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Data Collection */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Data Collection
          </h3>
          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="dataCollection" className="font-medium">
                  Usage Data Collection
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Allow us to collect data about how you use our platform to improve our services
                </p>
              </div>
              <Switch
                id="dataCollection"
                checked={privacySettings.dataCollection}
                onCheckedChange={() => handleToggle("dataCollection")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="analytics" className="font-medium">
                  Analytics
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Allow us to use analytics tools to understand how our platform is used
                </p>
              </div>
              <Switch
                id="analytics"
                checked={privacySettings.analytics}
                onCheckedChange={() => handleToggle("analytics")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="thirdPartySharing" className="font-medium">
                  Third-Party Data Sharing
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Allow us to share your data with trusted third parties
                </p>
              </div>
              <Switch
                id="thirdPartySharing"
                checked={privacySettings.thirdPartySharing}
                onCheckedChange={() => handleToggle("thirdPartySharing")}
              />
            </div>
          </div>
        </div>

        {/* Marketing Preferences */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Marketing Preferences
          </h3>
          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="marketingEmails" className="font-medium">
                Marketing Emails
              </Label>
              <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                Receive emails about new features, promotions, and special offers
              </p>
            </div>
            <Switch
              id="marketingEmails"
              checked={privacySettings.marketingEmails}
              onCheckedChange={() => handleToggle("marketingEmails")}
            />
          </div>
        </div>

        {/* Cookie Preferences */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Cookie Preferences
          </h3>
          <Separator />

          <RadioGroup
            value={privacySettings.cookiePreference}
            onValueChange={handleCookiePreferenceChange}
            className="space-y-4"
          >
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="essential" id="essential" className="mt-1" />
              <div className="grid gap-1.5">
                <Label htmlFor="essential" className="font-medium">
                  Essential Cookies Only
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Only cookies that are necessary for the website to function properly
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-2">
              <RadioGroupItem value="functional" id="functional" className="mt-1" />
              <div className="grid gap-1.5">
                <Label htmlFor="functional" className="font-medium">
                  Functional Cookies
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Cookies that remember your preferences and settings
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-2">
              <RadioGroupItem value="all" id="all" className="mt-1" />
              <div className="grid gap-1.5">
                <Label htmlFor="all" className="font-medium">
                  All Cookies
                </Label>
                <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
                  Allow all cookies, including those used for analytics and personalized advertising
                </p>
              </div>
            </div>
          </RadioGroup>
        </div>

        {/* Data Management */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg" style={{ color: "var(--text-primary)" }}>
            Data Management
          </h3>
          <Separator />

          <div className="space-y-4">
            <div>
              <p className="text-sm mb-2" style={{ color: "var(--text-secondary)" }}>
                You can request a copy of your data or delete your account and all associated data.
              </p>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={handleDataExport} disabled={isLoading}>
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Export My Data
                </Button>
                <Button type="button" variant="destructive" disabled={isLoading}>
                  Delete My Data
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" type="button">
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading} style={{ backgroundColor: "var(--primary)" }}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
