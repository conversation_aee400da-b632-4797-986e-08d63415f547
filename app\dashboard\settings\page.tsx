'use client';

import { useState } from 'react';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import ProfileSettings from '@/components/dashboard/settings/ProfileSettings';
import AccountSettings from '@/components/dashboard/settings/AccountSettings';
import NotificationSettings from '@/components/dashboard/settings/NotificationSettings';
import AppearanceSettings from '@/components/dashboard/settings/AppearanceSettings';
import IntegrationSettings from '@/components/dashboard/settings/IntegrationSettings';
import SecuritySettings from '@/components/dashboard/settings/SecuritySettings';
import PrivacySettings from '@/components/dashboard/settings/PrivacySettings';
import SettingsSidebar from '@/components/dashboard/settings/SettingsSidebar';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('profile');

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      <div className="flex h-screen overflow-hidden relative">
        {/* Mobile Sidebar Overlay */}
        <div
          className="mobile-sidebar-overlay md:hidden"
          onClick={() => {
            document.body.classList.remove('mobile-sidebar-open');
          }}
        />

        {/* Sidebar */}
        <DashboardSidebar />

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Header */}
          <DashboardHeader />

          {/* Settings Content */}
          <main className="p-4 md:p-6 space-y-6">
            <div>
              <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                تنظیمات
              </h1>
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                تنظیمات حساب و ترجیحات خود را مدیریت کنید
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-6">
              {/* Settings Navigation - Mobile */}
              <div className="md:hidden">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid grid-cols-2 sm:grid-cols-4 mb-4">
                    <TabsTrigger value="profile">پروفایل</TabsTrigger>
                    <TabsTrigger value="account">حساب</TabsTrigger>
                    <TabsTrigger value="notifications">اعلان‌ها</TabsTrigger>
                    <TabsTrigger value="appearance">ظاهر</TabsTrigger>
                    <TabsTrigger value="integrations">یکپارچه‌سازی</TabsTrigger>
                    <TabsTrigger value="security">امنیت</TabsTrigger>
                    <TabsTrigger value="privacy">حریم خصوصی</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              {/* Settings Navigation - Desktop */}
              <div className="hidden md:block w-64 flex-shrink-0">
                <SettingsSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
              </div>

              {/* Settings Content */}
              <div className="flex-1">
                <Card className="p-6">
                  {activeTab === 'profile' && <ProfileSettings />}
                  {activeTab === 'account' && <AccountSettings />}
                  {activeTab === 'notifications' && <NotificationSettings />}
                  {activeTab === 'appearance' && <AppearanceSettings />}
                  {activeTab === 'integrations' && <IntegrationSettings />}
                  {activeTab === 'security' && <SecuritySettings />}
                  {activeTab === 'privacy' && <PrivacySettings />}
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
