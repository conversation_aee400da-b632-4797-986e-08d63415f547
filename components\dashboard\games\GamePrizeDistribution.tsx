"use client"

import { useState, useEffect } from "react"
import { Pie } from "react-chartjs-2"
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js"

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend)

interface Prize {
  type?: string
  description: string
  probability?: number
  quantity?: number
  remainingQuantity?: number
  value?: number
}

interface GamePrizeDistributionProps {
  prizes: Prize[]
  gameId?: string
}

export default function GamePrizeDistribution({ prizes, gameId }: GamePrizeDistributionProps) {
  const [chartData, setChartData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(gameId ? true : false)

  // Generate colors based on prize types
  const generateColors = (count: number) => {
    const baseColors = [
      "rgba(139, 92, 246, 0.7)", // purple
      "rgba(132, 204, 22, 0.7)", // green
      "rgba(249, 115, 22, 0.7)", // orange
      "rgba(236, 72, 153, 0.7)", // pink
      "rgba(59, 130, 246, 0.7)", // blue
      "rgba(234, 179, 8, 0.7)", // yellow
    ]

    // Return colors for the number of prizes
    return Array(count)
      .fill(0)
      .map((_, i) => baseColors[i % baseColors.length])
  }

  useEffect(() => {
    const fetchPrizeDistribution = async () => {
      setIsLoading(true)

      // Process prizes from props as fallback
      const processPrizesFromProps = () => {
        if (prizes && prizes.length > 0) {
          const labels = prizes.map((prize) => prize.description)
          const values = prizes.map((prize) => (prize.probability || 0) * 100)
          const backgroundColor = generateColors(labels.length)

          setChartData({
            labels,
            datasets: [
              {
                data: values,
                backgroundColor,
                borderColor: backgroundColor.map((color) => color.replace("0.7", "1")),
                borderWidth: 1,
              },
            ],
          })
        } else {
          // Default data if no prizes
          const defaultLabels = ["10% Discount", "Free Shipping", "Free Sample", "No Prize"]
          const defaultValues = [30, 20, 10, 40]
          const backgroundColor = generateColors(defaultLabels.length)

          setChartData({
            labels: defaultLabels,
            datasets: [
              {
                data: defaultValues,
                backgroundColor,
                borderColor: backgroundColor.map((color) => color.replace("0.7", "1")),
                borderWidth: 1,
              },
            ],
          })
        }
      }

      // If gameId is provided, try to fetch real distribution data
      if (gameId) {
        try {
          const response = await fetch(`/api/dashboard/games/${gameId}/analytics`)

          if (response.ok) {
            const data = await response.json()

            if (data.prizeDistribution && data.prizeDistribution.length > 0) {
              const labels = data.prizeDistribution.map((item: any) => item.name)
              const values = data.prizeDistribution.map((item: any) => item.value)
              const backgroundColor = generateColors(labels.length)

              setChartData({
                labels,
                datasets: [
                  {
                    data: values,
                    backgroundColor,
                    borderColor: backgroundColor.map((color) => color.replace("0.7", "1")),
                    borderWidth: 1,
                  },
                ],
              })
            } else {
              // If no distribution data, fall back to prizes prop
              processPrizesFromProps()
            }
          } else {
            // If API call fails, fall back to prizes prop
            processPrizesFromProps()
          }
        } catch (error) {
          console.error("Error fetching prize distribution:", error)
          // Fall back to prizes prop
          processPrizesFromProps()
        }
      } else {
        // If no gameId, just process the prizes prop
        processPrizesFromProps()
      }

      setIsLoading(false)
    }

    fetchPrizeDistribution()
  }, [gameId, prizes])

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "right" as const,
        labels: {
          boxWidth: 15,
          padding: 15,
        },
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const label = context.label || ""
            const value = context.raw || 0
            return `${label}: ${value.toFixed(1)}%`
          },
        },
      },
    },
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!chartData) {
    return (
      <div className="flex justify-center items-center h-full">
        <p className="text-gray-500">No prize data available</p>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      <Pie data={chartData} options={options} />
    </div>
  )
}
