import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getUserTransactions } from '@/lib/models/user';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user ID from the session
    const userId = session.user.id;

    // Get user transactions
    const transactions = await getUserTransactions(userId);

    // Sort transactions by date (newest first)
    const sortedTransactions = transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Return the transactions
    return NextResponse.json({ transactions: sortedTransactions });
  } catch (error) {
    console.error('API Error fetching user transactions:', error);
    return NextResponse.json({ error: 'Failed to fetch user transactions' }, { status: 500 });
  }
}
