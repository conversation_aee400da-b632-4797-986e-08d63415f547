'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  BarChart3,
  Calendar,
  CreditCard,
  Gift,
  HelpCircle,
  LayoutDashboard,
  MessageSquare,
  Settings,
  Sparkles,
  Users,
  X,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { useSidebarMobile } from '@/hooks/use-sidebar-mobile';
import NotificationBadge from './NotificationBadge';

interface NavItemProps {
  icon: React.ElementType;
  label: string;
  href: string;
  active?: boolean;
  badge?: React.ReactNode;
}

function NavItem({ icon: Icon, label, href, active, badge }: NavItemProps) {
  return (
    <Link
      href={href}
      className={`flex items-center gap-3 px-3 py-2 rounded-md transition-colors ${
        active ? 'bg-primary/10' : 'hover:bg-gray-100'
      }`}
      style={{ color: active ? 'var(--primary)' : 'var(--text-primary)' }}
    >
      <Icon className="h-5 w-5" />
      <span className="text-sm font-medium">{label}</span>
      {badge && <div className="ml-auto">{badge}</div>}
    </Link>
  );
}

export default function DashboardSidebar() {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);
  const { isOpen, closeSidebar } = useSidebarMobile();

  const navItems = [
    { icon: LayoutDashboard, label: 'داشبورد', href: '/dashboard' },
    { icon: Gift, label: 'بازی‌های من', href: '/dashboard/games' },
    { icon: CreditCard, label: 'اعتبارات', href: '/dashboard/credits' },
    { icon: Users, label: 'مخاطبان', href: '/dashboard/audience' },
    { icon: BarChart3, label: 'تحلیل‌ها', href: '/dashboard/analytics' },
    { icon: Calendar, label: 'برنامه‌ریزی', href: '/dashboard/schedule' },
    {
      icon: MessageSquare,
      label: 'پیام‌ها',
      href: '/dashboard/messages',
      badge: <NotificationBadge />,
    },
  ];

  const secondaryNavItems = [
    { icon: Settings, label: 'تنظیمات', href: '/dashboard/settings' },
    { icon: HelpCircle, label: 'راهنما و پشتیبانی', href: '/dashboard/support' },
  ];

  // Prevent scrolling when mobile sidebar is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  return (
    <>
      {/* Mobile sidebar overlay */}
      {isOpen && <div className="fixed inset-0 bg-black/50 z-40 md:hidden" onClick={closeSidebar} aria-hidden="true" />}

      {/* Sidebar */}
      <aside
        className={`flex flex-col ${collapsed ? 'w-16' : 'w-64'} transition-all duration-300 
         ${isOpen ? 'fixed inset-y-0 right-0 z-50' : 'hidden'} md:flex md:relative md:z-10`}
        style={{
          borderLeftColor: 'var(--border)',
          backgroundColor: 'var(--background)',
          borderLeftStyle: 'solid',
          borderLeftWidth: '1px',
        }}
      >
        {/* Logo */}
        <div
          className={`h-16 flex items-center ${collapsed ? 'justify-center' : 'px-4'}`}
          style={{
            borderBottomColor: 'var(--border)',
            borderBottomStyle: 'solid',
            borderBottomWidth: '1px',
          }}
        >
          {collapsed ? (
            <Sparkles className="h-6 w-6" style={{ color: 'var(--primary)' }} />
          ) : (
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2 font-bold text-xl">
                <Sparkles className="h-6 w-6" style={{ color: 'var(--primary)' }} />
                <span>InstaGameify</span>
              </div>
              {/* Close button for mobile */}
              <Button variant="ghost" size="icon" className="md:hidden" onClick={closeSidebar}>
                <X className="h-5 w-5" />
              </Button>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-1 py-4 flex flex-col justify-between overflow-y-auto">
          <div className={collapsed ? 'px-2' : 'px-3'}>
            {/* Primary Nav */}
            <div className="space-y-1">
              {navItems.map((item) => (
                <NavItem
                  key={item.href}
                  icon={item.icon}
                  label={collapsed ? '' : item.label}
                  href={item.href}
                  active={pathname === item.href}
                  badge={item.badge}
                />
              ))}
            </div>

            {/* Secondary Nav */}
            <div className="mt-8 pt-4 border-t space-y-1" style={{ borderTopColor: 'var(--border)' }}>
              {secondaryNavItems.map((item) => (
                <NavItem
                  key={item.href}
                  icon={item.icon}
                  label={collapsed ? '' : item.label}
                  href={item.href}
                  active={pathname === item.href}
                />
              ))}
            </div>
          </div>

          {/* Collapse Button - only show on desktop */}
          <div className={`mt-auto ${collapsed ? 'px-2' : 'px-3'} hidden md:block`}>
            <Button
              variant="outline"
              onClick={() => setCollapsed(!collapsed)}
              className={`w-full justify-center ${collapsed ? 'px-2' : ''}`}
            >
              {collapsed ? '←' : '→'}
            </Button>
          </div>
        </div>
      </aside>
    </>
  );
}
