import { type NextRequest, NextResponse } from "next/server";
import { decrementPrizeQuantity, getGameById } from "@/lib/models/game";

export async function POST(
  request: NextRequest,
  { params }: { params: { gameId: string } }
) {
  try {
    const { gameId } = params;
    const { prizeDescription } = await request.json();

    console.log(
      `API: Awarding prize "${prizeDescription}" for game ID: ${gameId}`
    );

    if (!prizeDescription) {
      console.error("API Error: Prize description is missing");
      return NextResponse.json(
        { error: "Prize description is required" },
        { status: 400 }
      );
    }

    // Get the game first to check if the prize exists
    const game = await getGameById(gameId);

    if (!game) {
      console.error(`API Error: Game not found with ID: ${gameId}`);
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    // Check if the prize exists in the game
    const prizeExists = game.prizes.some(
      (p) =>
        (p.description === prizeDescription || p.name === prizeDescription) &&
        p.remainingQuantity > 0 &&
        p.probability > 0
    );

    if (!prizeExists) {
      console.error(
        `API Error: Prize "${prizeDescription}" not found or not available in game ${gameId}`
      );
      console.log("Available prizes:", JSON.stringify(game.prizes));
      return NextResponse.json(
        {
          error: "Prize not found or not available",
          availablePrizes: game.prizes,
        },
        { status: 400 }
      );
    }

    // Decrement the prize quantity and redistribute probabilities if needed
    const success = await decrementPrizeQuantity(gameId, prizeDescription);

    if (!success) {
      console.error(
        `API Error: Failed to decrement prize "${prizeDescription}" for game ${gameId}`
      );
      return NextResponse.json(
        {
          error: "Failed to award prize",
          availablePrizes: game.prizes,
        },
        { status: 500 }
      );
    }

    // Get the updated game with redistributed probabilities
    const updatedGame = await getGameById(gameId);

    if (!updatedGame) {
      console.error(
        `API Error: Failed to retrieve updated game after awarding prize`
      );
      return NextResponse.json(
        {
          error: "Failed to retrieve updated game",
          availablePrizes: game.prizes,
        },
        { status: 500 }
      );
    }

    console.log(`API: Prize "${prizeDescription}" awarded successfully`);

    return NextResponse.json({
      success: true,
      message: `Prize "${prizeDescription}" awarded successfully`,
      availablePrizes: updatedGame.prizes,
    });
  } catch (error) {
    console.error("API Error awarding prize:", error);
    return NextResponse.json(
      {
        error: "Failed to award prize",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { gameId: string } }
) {
  try {
    const { gameId } = params;
    console.log(`API: Getting available prizes for game ID: ${gameId}`);

    // Get the full game to include all prizes with their probabilities
    const game = await getGameById(gameId);

    if (!game) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      availablePrizes: game.prizes,
    });
  } catch (error) {
    console.error("API Error getting available prizes:", error);
    return NextResponse.json(
      { error: "Failed to get available prizes" },
      { status: 500 }
    );
  }
}
