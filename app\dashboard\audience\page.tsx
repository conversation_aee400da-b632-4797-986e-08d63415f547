'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Calendar,
  Download,
  Filter,
  Users,
  Target,
  Clock,
  Search,
  Mail,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  User,
  X,
  Loader2,
  AlertCircle,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import AudienceGrowthChart from '@/components/dashboard/audience/AudienceGrowthChart';
import AudienceEngagementChart from '@/components/dashboard/audience/AudienceEngagementChart';
import AudienceFilterSidebar from '@/components/dashboard/audience/AudienceFilterSidebar';
import StatsCard from '@/components/dashboard/StatsCard';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';

// Define types for our audience data
interface AudienceStats {
  totalPlayers: number;
  avgPlayTime: string;
  conversionRate: number;
  repeatPlayers: number;
}

interface Participant {
  id: string;
  name: string;
  contact: string;
  gameId: string;
  date: string;
  prize: string;
  converted: boolean;
}

interface Game {
  id: string;
  name: string;
  type: string;
  status: string;
  plays: number;
  participants: number;
}

interface AudienceData {
  audienceStats: AudienceStats;
  participants: Participant[];
  activeGames: Game[];
  inactiveGames: Game[];
}

export default function AudiencePage() {
  const router = useRouter();
  const [filterOpen, setFilterOpen] = useState(false);
  const [timeRange, setTimeRange] = useState('30days');
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedGameId, setSelectedGameId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [audienceData, setAudienceData] = useState<AudienceData | null>(null);

  // Fetch audience data from API
  useEffect(() => {
    const fetchAudienceData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/dashboard/audience?period=${timeRange}`);
        if (!response.ok) {
          throw new Error(`Error fetching audience data: ${response.status}`);
        }

        const data = await response.json();
        console.log('API response:', JSON.stringify(data, null, 2));
        setAudienceData(data);
      } catch (err) {
        console.error('Failed to fetch audience data:', err);
        setError('بارگذاری داده‌های مخاطبان ناموفق بود. لطفاً بعداً دوباره تلاش کنید.');
      } finally {
        setLoading(false);
      }
    };

    fetchAudienceData();
  }, [timeRange]);

  // Filter participants based on selected game
  const filteredParticipants =
    selectedGameId && audienceData
      ? audienceData.participants.filter((p) => p.gameId === selectedGameId || selectedGameId === 'all')
      : audienceData?.participants || [];

  // Get selected game
  const selectedGame =
    selectedGameId && audienceData
      ? [...audienceData.activeGames, ...audienceData.inactiveGames].find((game) => game.id === selectedGameId)
      : null;

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <div className="flex h-screen overflow-hidden">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden">
            <DashboardHeader />
            <div className="flex-1 overflow-y-auto p-4 md:p-6 flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-600" />
                <p className="text-lg font-medium">در حال بارگذاری داده‌های مخاطبان...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex flex-col">
        <div className="flex h-screen overflow-hidden">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden">
            <DashboardHeader />
            <div className="flex-1 overflow-y-auto p-4 md:p-6">
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
              <Button onClick={() => window.location.reload()}>تلاش مجدد</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <DashboardSidebar />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <DashboardHeader />

          {/* Audience Content */}
          <div className="flex-1 overflow-hidden flex">
            {/* Filter Sidebar - Only visible when filter is open */}
            <AudienceFilterSidebar isOpen={filterOpen} onClose={() => setFilterOpen(false)} />

            {/* Main Content Area */}
            <div className="flex-1 overflow-y-auto p-4 md:p-6">
              {/* Page Header */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div>
                  <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                    مخاطبان بازی
                  </h1>
                  <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                    بازیکنان خود را بشناسید و بازی‌هایتان را برای مشارکت بهتر بهینه کنید
                  </p>
                </div>
                <div className="flex gap-2 w-full md:w-auto">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => setFilterOpen(!filterOpen)}
                  >
                    <Filter className="h-4 w-4" />
                    <span className="hidden sm:inline">فیلترها</span>
                  </Button>
                  <Button variant="outline" className="flex items-center gap-2 ml-auto md:ml-0">
                    <Download className="h-4 w-4" />
                    <span className="hidden sm:inline">خروجی</span>
                  </Button>
                </div>
              </div>

              {/* Time Range Selector */}
              <div className="mb-6 flex justify-end">
                <div className="flex border rounded-md" style={{ borderColor: 'var(--border)' }}>
                  <Button
                    variant={timeRange === '7days' ? 'default' : 'ghost'}
                    size="sm"
                    className="text-xs"
                    onClick={() => setTimeRange('7days')}
                    style={
                      timeRange === '7days'
                        ? {
                            backgroundColor: 'var(--primary)',
                            color: 'var(--button-text)',
                          }
                        : {}
                    }
                  >
                    ۷ روز گذشته
                  </Button>
                  <Button
                    variant={timeRange === '30days' ? 'default' : 'ghost'}
                    size="sm"
                    className="text-xs"
                    onClick={() => setTimeRange('30days')}
                    style={
                      timeRange === '30days'
                        ? {
                            backgroundColor: 'var(--primary)',
                            color: 'var(--button-text)',
                          }
                        : {}
                    }
                  >
                    ۳۰ روز گذشته
                  </Button>
                  <Button
                    variant={timeRange === '90days' ? 'default' : 'ghost'}
                    size="sm"
                    className="text-xs"
                    onClick={() => setTimeRange('90days')}
                    style={
                      timeRange === '90days'
                        ? {
                            backgroundColor: 'var(--primary)',
                            color: 'var(--button-text)',
                          }
                        : {}
                    }
                  >
                    ۹۰ روز گذشته
                  </Button>
                  <Button
                    variant={timeRange === 'custom' ? 'default' : 'ghost'}
                    size="sm"
                    className="text-xs"
                    onClick={() => setTimeRange('custom')}
                    style={
                      timeRange === 'custom'
                        ? {
                            backgroundColor: 'var(--primary)',
                            color: 'var(--button-text)',
                          }
                        : {}
                    }
                  >
                    <Calendar className="h-3 w-3 ml-1" />
                    سفارشی
                  </Button>
                </div>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <StatsCard
                  title="کل بازیکنان"
                  value={audienceData?.audienceStats.totalPlayers.toLocaleString() || '0'}
                  icon={Users}
                  trend={+8}
                  color="#8b5cf6"
                />
                <StatsCard
                  title="میانگین زمان بازی"
                  value={audienceData?.audienceStats.avgPlayTime || '0m 0s'}
                  icon={Clock}
                  trend={+5}
                  color="#f97316"
                />
                <StatsCard
                  title="نرخ تبدیل"
                  value={`${audienceData?.audienceStats.conversionRate || 0}%`}
                  icon={Target}
                  trend={+1.2}
                  color="#ec4899"
                />
                <StatsCard
                  title="بازیکنان مکرر"
                  value={`${audienceData?.audienceStats.repeatPlayers || 0}%`}
                  icon={Users}
                  trend={+3}
                  color="#0ea5e9"
                />
              </div>

              {/* Tabs Navigation */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
                <TabsList className="grid grid-cols-3 w-full max-w-md">
                  <TabsTrigger value="overview">نمای کلی</TabsTrigger>
                  <TabsTrigger value="engagement">مشارکت بازی</TabsTrigger>
                  <TabsTrigger value="participants">شرکت‌کنندگان</TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Tab Content */}
              <div className="space-y-6">
                {/* Overview Tab */}
                {activeTab === 'overview' && (
                  <>
                    {/* Player Growth Chart */}
                    <Card
                      className="border rounded-lg shadow-sm overflow-hidden"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle style={{ color: 'var(--text-primary)' }}>رشد بازیکنان</CardTitle>
                            <CardDescription>بازیکنان جدید و بازگشتی در طول زمان</CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[460px]">
                          <AudienceGrowthChart
                            timeRange={timeRange}
                            setTimeRange={setTimeRange}
                            chartData={audienceData?.chartData || null}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Game Performance Overview */}
                    <Card
                      className="border rounded-lg shadow-sm overflow-hidden"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle style={{ color: 'var(--text-primary)' }}>عملکرد بازی</CardTitle>
                        <CardDescription>مشارکت بر اساس نوع بازی</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {[
                            {
                              name: 'بازی‌های چرخ',
                              plays: 2345,
                              completion: 92,
                              conversion: 3.2,
                            },
                            {
                              name: 'بازی‌های اهرم',
                              plays: 1876,
                              completion: 88,
                              conversion: 2.8,
                            },
                            {
                              name: 'بازی‌های پاکت',
                              plays: 1543,
                              completion: 82,
                              conversion: 2.1,
                            },
                          ].map((game, i) => (
                            <div key={i} className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                  {game.name}
                                </span>
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  {game.plays.toLocaleString()} بازی
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-full bg-gray-100 rounded-full h-2">
                                  <div
                                    className="h-2 rounded-full"
                                    style={{
                                      width: `${game.completion}%`,
                                      backgroundColor: i === 0 ? '#8b5cf6' : i === 1 ? '#f97316' : '#84cc16',
                                    }}
                                  ></div>
                                </div>
                                <span className="text-xs whitespace-nowrap" style={{ color: 'var(--text-primary)' }}>
                                  {game.completion}% تکمیل
                                </span>
                              </div>
                              <div className="flex justify-between text-xs" style={{ color: 'var(--text-secondary)' }}>
                                <span>نرخ تبدیل: {game.conversion}%</span>
                                <span>میانگین زمان: {i === 0 ? '1m 32s' : i === 1 ? '1m 12s' : '0m 58s'}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Audience Insights */}
                    <Card
                      className="border rounded-lg shadow-sm overflow-hidden"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle style={{ color: 'var(--text-primary)' }}>بینش‌های بهینه‌سازی بازی</CardTitle>
                        <CardDescription>بینش‌های عملی برای بهبود بازی‌هایتان</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h3 className="font-medium text-blue-700 mb-2">بهترین زمان راه‌اندازی بازی</h3>
                            <p className="text-sm text-blue-600">
                              مخاطبان شما بین ساعت ۶ تا ۹ شب در روزهای هفته فعال‌ترین هستند. تبلیغات بازی‌هایتان را در
                              این ساعات برای حداکثر مشارکت برنامه‌ریزی کنید.
                            </p>
                          </div>
                          <div className="bg-green-50 p-4 rounded-lg">
                            <h3 className="font-medium text-green-700 mb-2">فرمت برتر عملکرد</h3>
                            <p className="text-sm text-green-600">
                              بازی‌های چرخ ۳.۲ برابر مشارکت بیشتری نسبت به سایر فرمت‌ها دارند. استفاده از این فرمت را
                              برای تبلیغات مهم بیشتر در نظر بگیرید.
                            </p>
                          </div>
                          <div className="bg-purple-50 p-4 rounded-lg">
                            <h3 className="font-medium text-purple-700 mb-2">بهینه‌سازی جایزه</h3>
                            <p className="text-sm text-purple-600">
                              بازی‌هایی که "ارسال رایگان" را به عنوان جایزه ارائه می‌دهند ۲۸٪ نرخ تبدیل بالاتری نسبت به
                              تخفیف‌های درصدی دارند. این نوع جایزه را در کمپین بعدی‌تان آزمایش کنید.
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )}

                {/* Engagement Tab */}
                {activeTab === 'engagement' && (
                  <>
                    {/* Engagement Overview Chart */}
                    <Card
                      className="border rounded-lg shadow-sm overflow-hidden"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle style={{ color: 'var(--text-primary)' }}>روندهای مشارکت بازی</CardTitle>
                        <CardDescription>نحوه تعامل بازیکنان با بازی‌هایتان در طول زمان</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="h-80">
                          <AudienceEngagementChart />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Engagement Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Card
                        className="border rounded-lg shadow-sm overflow-hidden"
                        style={{ borderColor: 'var(--border)' }}
                      >
                        <CardHeader className="pb-2">
                          <CardTitle style={{ color: 'var(--text-primary)' }}>تکمیل بازی</CardTitle>
                          <CardDescription>نحوه پیشرفت کاربران در بازی‌ها</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  نرخ شروع تا پایان
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  86%
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div className="bg-purple-600 h-2 rounded-full" style={{ width: '86%' }}></div>
                              </div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  میانگین زمان صرف شده
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  {audienceData?.audienceStats.avgPlayTime || '0m 0s'}
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div className="bg-green-500 h-2 rounded-full" style={{ width: '72%' }}></div>
                              </div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  Replay Rate
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  {audienceData?.audienceStats.repeatPlayers || 0}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div
                                  className="bg-blue-500 h-2 rounded-full"
                                  style={{
                                    width: `${audienceData?.audienceStats.repeatPlayers || 0}%`,
                                  }}
                                ></div>
                              </div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  Share Rate
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  18%
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div className="bg-orange-500 h-2 rounded-full" style={{ width: '18%' }}></div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card
                        className="border rounded-lg shadow-sm overflow-hidden"
                        style={{ borderColor: 'var(--border)' }}
                      >
                        <CardHeader className="pb-2">
                          <CardTitle style={{ color: 'var(--text-primary)' }}>Prize Performance</CardTitle>
                          <CardDescription>Which prizes drive the most engagement</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {[
                              {
                                name: 'Free Shipping',
                                conversion: '4.8%',
                                popularity: 85,
                              },
                              {
                                name: '10% Discount',
                                conversion: '3.2%',
                                popularity: 72,
                              },
                              {
                                name: 'Free Sample',
                                conversion: '5.1%',
                                popularity: 68,
                              },
                              {
                                name: 'Buy One Get One',
                                conversion: '6.3%',
                                popularity: 54,
                              },
                            ].map((prize, i) => (
                              <div key={i} className="space-y-1">
                                <div className="flex justify-between">
                                  <span className="text-sm" style={{ color: 'var(--text-primary)' }}>
                                    {prize.name}
                                  </span>
                                  <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                    {prize.conversion}
                                  </span>
                                </div>
                                <div className="w-full bg-gray-100 rounded-full h-2">
                                  <div
                                    className="h-2 rounded-full"
                                    style={{
                                      width: `${prize.popularity}%`,
                                      backgroundColor: ['#8b5cf6', '#f97316', '#84cc16', '#ec4899'][i],
                                    }}
                                  ></div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      <Card
                        className="border rounded-lg shadow-sm overflow-hidden"
                        style={{ borderColor: 'var(--border)' }}
                      >
                        <CardHeader className="pb-2">
                          <CardTitle style={{ color: 'var(--text-primary)' }}>Conversion Metrics</CardTitle>
                          <CardDescription>How game plays convert to actions</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  Game to Purchase
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  {audienceData?.audienceStats.conversionRate || 0}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div
                                  className="bg-green-600 h-2 rounded-full"
                                  style={{
                                    width: `${(audienceData?.audienceStats.conversionRate || 0) * 10}%`,
                                  }}
                                ></div>
                              </div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  Email Capture
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  8.7%
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div className="bg-purple-600 h-2 rounded-full" style={{ width: '87%' }}></div>
                              </div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  Coupon Usage
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  12.4%
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div className="bg-orange-500 h-2 rounded-full" style={{ width: '124%' }}></div>
                              </div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                  Return Rate
                                </span>
                                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                  {audienceData?.audienceStats.repeatPlayers || 0}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-2">
                                <div
                                  className="bg-blue-500 h-2 rounded-full"
                                  style={{
                                    width: `${audienceData?.audienceStats.repeatPlayers || 0}%`,
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Engagement by Game Type */}
                    <Card
                      className="border rounded-lg shadow-sm overflow-hidden"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle style={{ color: 'var(--text-primary)' }}>Engagement by Game Type</CardTitle>
                        <CardDescription>Which game types perform best with your audience</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead>
                              <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Game Type
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Plays
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Avg. Time
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Completion Rate
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Conversion Rate
                                </th>
                                <th
                                  className="text-left p-3 text-sm font-medium"
                                  style={{ color: 'var(--text-secondary)' }}
                                >
                                  Engagement Score
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {[
                                {
                                  type: 'Wheel',
                                  plays: '2,345',
                                  time: '1m 32s',
                                  completion: '92%',
                                  conversion: '3.2%',
                                  score: '8.7/10',
                                },
                                {
                                  type: 'Lever',
                                  plays: '1,876',
                                  time: '1m 12s',
                                  completion: '88%',
                                  conversion: '2.8%',
                                  score: '7.9/10',
                                },
                                {
                                  type: 'Envelope',
                                  plays: '1,543',
                                  time: '0m 58s',
                                  completion: '82%',
                                  conversion: '2.1%',
                                  score: '7.2/10',
                                },
                              ].map((game, i) => (
                                <tr
                                  key={i}
                                  className="border-b hover:bg-gray-50"
                                  style={{ borderColor: 'var(--border)' }}
                                >
                                  <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                    {game.type}
                                  </td>
                                  <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                    {game.plays}
                                  </td>
                                  <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                    {game.time}
                                  </td>
                                  <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                    {game.completion}
                                  </td>
                                  <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                    {game.conversion}
                                  </td>
                                  <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                    {game.score}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )}

                {/* Participants Tab */}
                {activeTab === 'participants' && (
                  <>
                    {/* Selected Game Info */}
                    {selectedGameId && (
                      <div className="mb-4 flex items-center justify-between bg-purple-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div
                            className="w-10 h-10 rounded-full flex items-center justify-center"
                            style={{
                              backgroundColor: 'rgba(139, 92, 246, 0.2)',
                            }}
                          >
                            {selectedGame?.type === 'wheel' ? (
                              <div className="h-5 w-5 rounded-full border-2 border-purple-500 border-t-transparent animate-spin-slow" />
                            ) : selectedGame?.type === 'lever' ? (
                              <div className="h-5 w-1.5 bg-orange-500" />
                            ) : (
                              <div className="h-4 w-4 bg-green-500 rounded-sm" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                              {selectedGame?.name}
                            </h3>
                            <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                              Showing participants for this game
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedGameId(null)}
                          className="flex items-center gap-1"
                        >
                          <X className="h-3 w-3" />
                          Clear Selection
                        </Button>
                      </div>
                    )}

                    {/* Game Selection */}
                    {!selectedGameId && (
                      <div className="mb-6">
                        <h2 className="text-lg font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                          Select a Game to View Participants
                        </h2>

                        {/* Active Games */}
                        <div className="mb-6">
                          <h3
                            className="text-sm font-medium mb-3 flex items-center gap-2"
                            style={{ color: 'var(--text-secondary)' }}
                          >
                            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
                            Active Games
                          </h3>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {audienceData?.activeGames.map((game) => (
                              <Card
                                key={game.id}
                                className="border rounded-lg shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                                style={{ borderColor: 'var(--border)' }}
                                onClick={() => setSelectedGameId(game.id)}
                              >
                                <CardContent className="p-4">
                                  <div className="flex items-center gap-3 mb-3">
                                    <div
                                      className="w-10 h-10 rounded-full flex items-center justify-center"
                                      style={{
                                        backgroundColor:
                                          game.type === 'wheel'
                                            ? 'rgba(139, 92, 246, 0.2)'
                                            : game.type === 'lever'
                                            ? 'rgba(249, 115, 22, 0.2)'
                                            : 'rgba(132, 204, 22, 0.2)',
                                      }}
                                    >
                                      {game.type === 'wheel' ? (
                                        <div className="h-5 w-5 rounded-full border-2 border-purple-500 border-t-transparent animate-spin-slow" />
                                      ) : game.type === 'lever' ? (
                                        <div className="h-5 w-1.5 bg-orange-500" />
                                      ) : (
                                        <div className="h-4 w-4 bg-green-500 rounded-sm" />
                                      )}
                                    </div>
                                    <div>
                                      <h3 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                        {game.name}
                                      </h3>
                                      <p
                                        className="text-xs capitalize"
                                        style={{
                                          color: 'var(--text-secondary)',
                                        }}
                                      >
                                        {game.type} Game
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex justify-between text-sm">
                                    <span style={{ color: 'var(--text-secondary)' }}>Total Plays:</span>
                                    <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                      {game.plays.toLocaleString()}
                                    </span>
                                  </div>
                                  <div className="flex justify-between text-sm">
                                    <span style={{ color: 'var(--text-secondary)' }}>Participants:</span>
                                    <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                      {game.participants}
                                    </span>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}

                            {audienceData?.activeGames.length === 0 && (
                              <div className="col-span-full text-center p-6 bg-gray-50 rounded-lg">
                                <p className="text-gray-500">No active games found</p>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Inactive Games */}
                        <div>
                          <h3
                            className="text-sm font-medium mb-3 flex items-center gap-2"
                            style={{ color: 'var(--text-secondary)' }}
                          >
                            <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>
                            Inactive Games
                          </h3>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {audienceData?.inactiveGames.map((game) => (
                              <Card
                                key={game.id}
                                className="border rounded-lg shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-shadow opacity-75"
                                style={{ borderColor: 'var(--border)' }}
                                onClick={() => setSelectedGameId(game.id)}
                              >
                                <CardContent className="p-4">
                                  <div className="flex items-center gap-3 mb-3">
                                    <div
                                      className="w-10 h-10 rounded-full flex items-center justify-center"
                                      style={{
                                        backgroundColor:
                                          game.type === 'wheel'
                                            ? 'rgba(139, 92, 246, 0.2)'
                                            : game.type === 'lever'
                                            ? 'rgba(249, 115, 22, 0.2)'
                                            : 'rgba(132, 204, 22, 0.2)',
                                      }}
                                    >
                                      {game.type === 'wheel' ? (
                                        <div className="h-5 w-5 rounded-full border-2 border-purple-500 border-t-transparent" />
                                      ) : game.type === 'lever' ? (
                                        <div className="h-5 w-1.5 bg-orange-500" />
                                      ) : (
                                        <div className="h-4 w-4 bg-green-500 rounded-sm" />
                                      )}
                                    </div>
                                    <div>
                                      <h3 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                        {game.name}
                                      </h3>
                                      <p
                                        className="text-xs capitalize"
                                        style={{
                                          color: 'var(--text-secondary)',
                                        }}
                                      >
                                        {game.type} Game
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex justify-between text-sm">
                                    <span style={{ color: 'var(--text-secondary)' }}>Total Plays:</span>
                                    <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                      {game.plays.toLocaleString()}
                                    </span>
                                  </div>
                                  <div className="flex justify-between text-sm">
                                    <span style={{ color: 'var(--text-secondary)' }}>Participants:</span>
                                    <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                      {game.participants}
                                    </span>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}

                            {audienceData?.inactiveGames.length === 0 && (
                              <div className="col-span-full text-center p-6 bg-gray-50 rounded-lg">
                                <p className="text-gray-500">No inactive games found</p>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* View All Participants Button */}
                        <div className="mt-6 flex justify-center">
                          <Button
                            className="flex items-center gap-2"
                            style={{
                              backgroundColor: 'var(--primary)',
                              color: 'var(--button-text)',
                            }}
                            onClick={() => setSelectedGameId('all')}
                          >
                            <Users className="h-4 w-4" />
                            View All Participants
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Search and Export */}
                    <div className="flex flex-col sm:flex-row gap-4 mb-6">
                      <div className="relative flex-1">
                        <Search
                          className="h-4 w-4 absolute left-3 top-1/2 -translate-y-1/2"
                          style={{ color: 'var(--text-secondary)' }}
                        />
                        <input
                          type="text"
                          placeholder="Search participants..."
                          className="pl-9 pr-4 py-2 w-full rounded-md border"
                          style={{
                            borderColor: 'var(--border)',
                            backgroundColor: 'var(--background)',
                            color: 'var(--text-primary)',
                          }}
                        />
                      </div>
                      <Button variant="outline" className="flex items-center gap-2 sm:w-auto">
                        <Download className="h-4 w-4" />
                        <span>Export</span>
                      </Button>
                    </div>

                    {/* Participants List */}
                    <Card
                      className="border rounded-lg shadow-sm overflow-hidden"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle style={{ color: 'var(--text-primary)' }}>
                              {selectedGameId === 'all'
                                ? 'All Game Participants'
                                : selectedGameId
                                ? `${selectedGame?.name} Participants`
                                : 'Game Participants'}
                            </CardTitle>
                            <CardDescription>{filteredParticipants.length} participants found</CardDescription>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" className="text-xs">
                              <Filter className="h-3 w-3 mr-1" />
                              Filter
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {filteredParticipants.length > 0 ? (
                          <div className="overflow-x-auto">
                            <table className="w-full">
                              <thead>
                                <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                                  <th
                                    className="text-left p-3 text-sm font-medium"
                                    style={{ color: 'var(--text-secondary)' }}
                                  >
                                    Participant
                                  </th>
                                  {(selectedGameId === 'all' || !selectedGameId) && (
                                    <th
                                      className="text-left p-3 text-sm font-medium"
                                      style={{ color: 'var(--text-secondary)' }}
                                    >
                                      Game
                                    </th>
                                  )}
                                  <th
                                    className="text-left p-3 text-sm font-medium"
                                    style={{ color: 'var(--text-secondary)' }}
                                  >
                                    Date Played
                                  </th>
                                  <th
                                    className="text-left p-3 text-sm font-medium"
                                    style={{ color: 'var(--text-secondary)' }}
                                  >
                                    Prize
                                  </th>
                                  <th
                                    className="text-left p-3 text-sm font-medium"
                                    style={{ color: 'var(--text-secondary)' }}
                                  >
                                    Converted
                                  </th>
                                  <th
                                    className="text-left p-3 text-sm font-medium"
                                    style={{ color: 'var(--text-secondary)' }}
                                  >
                                    Actions
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {filteredParticipants.map((participant) => {
                                  const participantGame = [
                                    ...(audienceData?.activeGames || []),
                                    ...(audienceData?.inactiveGames || []),
                                  ].find((g) => g.id === participant.gameId);
                                  return (
                                    <tr
                                      key={participant.id}
                                      className="border-b hover:bg-gray-50"
                                      style={{ borderColor: 'var(--border)' }}
                                    >
                                      <td className="p-3">
                                        <div className="flex flex-col">
                                          <span
                                            className="font-medium"
                                            style={{
                                              color: 'var(--text-primary)',
                                            }}
                                          >
                                            {participant.name}
                                          </span>
                                          <span
                                            className="text-xs"
                                            style={{
                                              color: 'var(--text-secondary)',
                                            }}
                                          >
                                            {participant.contact}
                                          </span>
                                        </div>
                                      </td>
                                      {(selectedGameId === 'all' || !selectedGameId) && (
                                        <td
                                          className="p-3"
                                          style={{
                                            color: 'var(--text-primary)',
                                          }}
                                        >
                                          {participantGame?.name || 'Unknown Game'}
                                        </td>
                                      )}
                                      <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                        {participant.date}
                                      </td>
                                      <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                                        {participant.prize}
                                      </td>
                                      <td className="p-3">
                                        <span
                                          className={`px-2 py-1 rounded-full text-xs ${
                                            participant.converted
                                              ? 'bg-green-100 text-green-800'
                                              : 'bg-gray-100 text-gray-800'
                                          }`}
                                        >
                                          {participant.converted ? 'Yes' : 'No'}
                                        </span>
                                      </td>
                                      <td className="p-3">
                                        <div className="flex items-center gap-2">
                                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                            <Mail
                                              className="h-4 w-4"
                                              style={{
                                                color: 'var(--primary)',
                                              }}
                                            />
                                          </Button>
                                          <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                              </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                              <DropdownMenuItem>View Details</DropdownMenuItem>
                                              <DropdownMenuItem>Send Message</DropdownMenuItem>
                                              <DropdownMenuItem>Add to Segment</DropdownMenuItem>
                                            </DropdownMenuContent>
                                          </DropdownMenu>
                                        </div>
                                      </td>
                                    </tr>
                                  );
                                })}
                              </tbody>
                            </table>
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                              No participants found
                            </h3>
                            <p className="text-sm text-gray-500 max-w-md mx-auto">
                              There are no participants for this game yet. Share your game link to start collecting
                              player data.
                            </p>
                          </div>
                        )}

                        {/* Pagination */}
                        {filteredParticipants.length > 0 && (
                          <div className="flex items-center justify-between mt-4">
                            <div className="text-sm text-gray-500">
                              Showing 1-{filteredParticipants.length} of {filteredParticipants.length} participants
                            </div>
                            <div className="flex items-center gap-2">
                              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                                <ChevronLeft className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm" className="h-8 w-8 p-0 bg-primary text-white">
                                1
                              </Button>
                              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Participant Insights */}
                    {filteredParticipants.length > 0 && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <Card
                          className="border rounded-lg shadow-sm overflow-hidden"
                          style={{ borderColor: 'var(--border)' }}
                        >
                          <CardHeader className="pb-2">
                            <CardTitle style={{ color: 'var(--text-primary)' }}>Top Participants</CardTitle>
                            <CardDescription>Players who engage with your games the most</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              {filteredParticipants.slice(0, 5).map((player, i) => (
                                <div key={i} className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div
                                      className="w-8 h-8 rounded-full flex items-center justify-center"
                                      style={{
                                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                      }}
                                    >
                                      <User className="h-4 w-4" style={{ color: 'var(--primary)' }} />
                                    </div>
                                    <span style={{ color: 'var(--text-primary)' }}>{player.name}</span>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <span
                                      className="text-sm"
                                      style={{
                                        color: 'var(--text-secondary)',
                                      }}
                                    >
                                      {Math.floor(Math.random() * 10) + 1} plays
                                    </span>
                                    <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                      {player.converted ? 1 : 0} conversions
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>

                        <Card
                          className="border rounded-lg shadow-sm overflow-hidden"
                          style={{ borderColor: 'var(--border)' }}
                        >
                          <CardHeader>
                            <CardTitle style={{ color: 'var(--text-primary)' }}>Top Participants</CardTitle>
                            <CardDescription>Players who engage with your games the most</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              {filteredParticipants.slice(0, 5).map((player, i) => (
                                <div key={i} className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div
                                      className="w-8 h-8 rounded-full flex items-center justify-center"
                                      style={{
                                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                      }}
                                    >
                                      <User className="h-4 w-4" style={{ color: 'var(--primary)' }} />
                                    </div>
                                    <span style={{ color: 'var(--text-primary)' }}>{player.name}</span>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <span
                                      className="text-sm"
                                      style={{
                                        color: 'var(--text-secondary)',
                                      }}
                                    >
                                      {Math.floor(Math.random() * 10) + 1} plays
                                    </span>
                                    <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                                      {player.converted ? 1 : 0} conversions
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>

                        <Card
                          className="border rounded-lg shadow-sm overflow-hidden"
                          style={{ borderColor: 'var(--border)' }}
                        >
                          <CardHeader className="pb-2">
                            <CardTitle style={{ color: 'var(--text-primary)' }}>Participant Insights</CardTitle>
                            <CardDescription>Understand your player behavior</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              <div>
                                <h4 className="text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                                  Repeat Play Rate
                                </h4>
                                <div className="flex items-center gap-4">
                                  <div className="w-full bg-gray-100 rounded-full h-2.5">
                                    <div
                                      className="bg-purple-600 h-2.5 rounded-full"
                                      style={{
                                        width: `${audienceData?.audienceStats.repeatPlayers || 0}%`,
                                      }}
                                    ></div>
                                  </div>
                                  <span
                                    className="text-sm font-medium whitespace-nowrap"
                                    style={{ color: 'var(--text-primary)' }}
                                  >
                                    {audienceData?.audienceStats.repeatPlayers || 0}%
                                  </span>
                                </div>
                                <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                                  {audienceData?.audienceStats.repeatPlayers || 0}% of players return to play your games
                                  again
                                </p>
                              </div>

                              <div>
                                <h4 className="text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                                  Contact Information Provided
                                </h4>
                                <div className="flex items-center gap-4">
                                  <div className="w-full bg-gray-100 rounded-full h-2.5">
                                    <div className="bg-green-500 h-2.5 rounded-full" style={{ width: '78%' }}></div>
                                  </div>
                                  <span
                                    className="text-sm font-medium whitespace-nowrap"
                                    style={{ color: 'var(--text-primary)' }}
                                  >
                                    78%
                                  </span>
                                </div>
                                <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                                  78% of players provide contact information
                                </p>
                              </div>

                              <div>
                                <h4 className="text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                                  Prize Redemption Rate
                                </h4>
                                <div className="flex items-center gap-4">
                                  <div className="w-full bg-gray-100 rounded-full h-2.5">
                                    <div
                                      className="bg-orange-500 h-2.5 rounded-full"
                                      style={{
                                        width: `${audienceData?.audienceStats.conversionRate || 0}%`,
                                      }}
                                    ></div>
                                  </div>
                                  <span
                                    className="text-sm font-medium whitespace-nowrap"
                                    style={{ color: 'var(--text-primary)' }}
                                  >
                                    {audienceData?.audienceStats.conversionRate || 0}%
                                  </span>
                                </div>
                                <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                                  {audienceData?.audienceStats.conversionRate || 0}% of winners redeem their prizes
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
