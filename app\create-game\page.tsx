'use client';
import { useRouter } from 'next/navigation';
import GameWizard from '@/components/wizard/GameWizard';

export default function CreateGamePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      {/* Hero-style background with animated gradient */}
      <div
        className="fixed inset-0 bg-gradient-to-r opacity-90 animate-gradient"
        style={{
          backgroundImage: 'linear-gradient(to right, var(--primary), #F9A8D4)',
          animation: 'gradientShift 10s infinite linear',
        }}
      />

      <div className="relative z-10 flex flex-col min-h-screen">
        <main className="flex-1 container py-8 px-4">
          <GameWizard />
        </main>
      </div>
    </div>
  );
}
