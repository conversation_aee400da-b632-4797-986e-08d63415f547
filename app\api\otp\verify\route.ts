import { NextResponse, type NextRequest } from "next/server"
import { getOTP, deleteOTP, normalizePhoneNumber, listAllOTPs } from "@/lib/otp-store"

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, otpCode } = await request.json()

    // console.log$$[^)]*$$;

    if (!phoneNumber || !otpCode) {
      return NextResponse.json({ success: false, error: "Phone number and OTP code are required" }, { status: 400 })
    }

    const normalizedPhone = normalizePhoneNumber(phoneNumber)
    // console.log$$[^)]*$$;

    // Debug: List all stored OTPs
    const allOTPs = listAllOTPs()
    // console.log$$[^)]*$$;

    // Get the stored OTP for this phone number
    const storedOTPData = getOTP(normalizedPhone)

    if (!storedOTPData) {
      return NextResponse.json(
        {
          success: false,
          error: "No verification code found for this phone number",
        },
        { status: 400 },
      )
    }

    // Check if the OTP has expired
    const now = new Date()
    if (now > storedOTPData.expiresAt) {
      deleteOTP(normalizedPhone) // Clean up expired OTP
      return NextResponse.json(
        {
          success: false,
          error: "Verification code has expired. Please request a new one.",
        },
        { status: 400 },
      )
    }

    // Check if the OTP matches
    console.log(`Comparing OTPs: stored=${storedOTPData.otp}, received=${otpCode}`)
    if (storedOTPData.otp !== otpCode) {
      return NextResponse.json({ success: false, error: "Invalid verification code" }, { status: 400 })
    }

    // OTP is valid, delete it to prevent reuse
    deleteOTP(normalizedPhone)

    return NextResponse.json({
      success: true,
      message: "Phone number verified successfully",
    })
  } catch (error) {
    console.error("Error verifying OTP:", error)
    return NextResponse.json({ success: false, error: "Failed to verify OTP" }, { status: 500 })
  }
}
