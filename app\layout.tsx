import type { Metadata } from 'next';
import './globals.css';
import ClientLayout from './client-layout';

export const metadata: Metadata = {
  title: {
    default: 'بوستاگرام - پلتفرم گیمیفیکیشن اینستاگرام',
    template: '%s | بوستاگرام',
  },
  description:
    'پلتفرم بوستاگرام برای ایجاد بازی‌های جذاب و تعاملی برای فالوورهای اینستاگرام شما. افزایش تعامل، جذب مخاطب و رشد کسب‌وکار با بازی‌های هوشمند.',
  keywords: [
    'بوستاگرام',
    'گیمیفیکیشن اینستاگرام',
    'بازی اینستاگرام',
    'افزایش تعامل اینستاگرام',
    'جذب فالوور',
    'بازاریابی اینستاگرام',
    'تعامل مخاطب',
    'رشد کسب‌وکار',
  ],
  authors: [{ name: 'بوستاگرام' }],
  creator: 'بوستاگرام',
  publisher: 'بوستاگرام',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://boostagram.ir'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'بوستاگرام - پلتفرم گیمیفیکیشن اینستاگرام',
    description:
      'پلتفرم بوستاگرام برای ایجاد بازی‌های جذاب و تعاملی برای فالوورهای اینستاگرام شما. افزایش تعامل، جذب مخاطب و رشد کسب‌وکار با بازی‌های هوشمند.',
    url: 'https://boostagram.ir',
    siteName: 'بوستاگرام',
    locale: 'fa_IR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'بوستاگرام - پلتفرم گیمیفیکیشن اینستاگرام',
    description: 'پلتفرم بوستاگرام برای ایجاد بازی‌های جذاب و تعاملی برای فالوورهای اینستاگرام شما.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="fa" dir="rtl">
      <head>
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />

        {/* Theme Color */}
        <meta name="theme-color" content="#6366f1" />
        <meta name="msapplication-TileColor" content="#6366f1" />

        {/* Fonts */}
        <link
          href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100;200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />

        {/* Additional Meta Tags */}
        <meta name="application-name" content="بوستاگرام" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="بوستاگرام" />
        <meta name="mobile-web-app-capable" content="yes" />

        {/* Preconnect for Performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body style={{ fontFamily: 'Vazirmatn, Tahoma, sans-serif' }}>
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
