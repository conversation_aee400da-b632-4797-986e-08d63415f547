import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET() {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      console.log("No authenticated user found for activity feed")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id
    console.log("Fetching activity for user ID:", userId)

    // Connect to MongoDB
    const client = await clientPromise
    const db = client.db()

    // Try different user ID formats to find games
    let games = []

    // First try with ObjectId
    try {
      const objectIdQuery = { userId: new ObjectId(userId) }
      console.log("Trying activity query with ObjectId:", objectIdQuery)
      games = await db.collection("games").find(objectIdQuery).toArray()
      console.log(`Found ${games.length} games with ObjectId query for activity`)
    } catch (error) {
      console.log("Error with ObjectId query for activity:", error.message)
    }

    // If no games found, try with string ID
    if (games.length === 0) {
      try {
        const stringIdQuery = { userId: userId }
        console.log("Trying activity query with string ID:", stringIdQuery)
        games = await db.collection("games").find(stringIdQuery).toArray()
        console.log(`Found ${games.length} games with string ID query for activity`)
      } catch (error) {
        console.log("Error with string ID query for activity:", error.message)
      }
    }

    // If still no games, try with string representation of ObjectId
    if (games.length === 0) {
      try {
        const stringObjectIdQuery = { userId: userId.toString() }
        console.log("Trying activity query with string representation of ObjectId:", stringObjectIdQuery)
        games = await db.collection("games").find(stringObjectIdQuery).toArray()
        console.log(`Found ${games.length} games with string representation of ObjectId query for activity`)
      } catch (error) {
        console.log("Error with string representation of ObjectId query for activity:", error.message)
      }
    }

    // If we still have no games, return empty activity
    if (games.length === 0) {
      console.log("No games found for activity, returning empty array")
      return NextResponse.json([])
    }

    // Collect all activities from all games
    const allActivities = []

    // Process each game to extract activities
    games.forEach((game) => {
      const gameName = game.name || "Unnamed Game"
      const gameId = game._id.toString()

      // Add plays as activities
      if (game.gameInteractions && game.gameInteractions.plays) {
        game.gameInteractions.plays.forEach((play) => {
          allActivities.push({
            id: new ObjectId().toString(), // Generate a unique ID
            type: "play",
            gameId,
            gameName,
            timestamp: play.timestamp,
            phoneNumber: play.phoneNumber || "Anonymous",
            details: "Played the game",
          })
        })
      }

      // Add conversions as activities
      if (game.gameInteractions && game.gameInteractions.conversions) {
        game.gameInteractions.conversions.forEach((conversion) => {
          allActivities.push({
            id: new ObjectId().toString(),
            type: "conversion",
            gameId,
            gameName,
            timestamp: conversion.timestamp,
            phoneNumber: conversion.phoneNumber,
            details: `Won prize: ${conversion.prize}`,
          })
        })
      }

      // Add phone verifications as activities
      if (game.gameInteractions && game.gameInteractions.phoneVerifications) {
        game.gameInteractions.phoneVerifications.forEach((verification) => {
          allActivities.push({
            id: new ObjectId().toString(),
            type: "phone_verification",
            gameId,
            gameName,
            timestamp: verification.timestamp,
            phoneNumber: verification.phoneNumber,
            details: `Phone verification ${verification.success ? "successful" : "failed"}`,
          })
        })
      }

      // Add code verifications as activities
      if (game.gameInteractions && game.gameInteractions.codeVerifications) {
        game.gameInteractions.codeVerifications.forEach((verification) => {
          allActivities.push({
            id: new ObjectId().toString(),
            type: "code_verification",
            gameId,
            gameName,
            timestamp: verification.timestamp,
            phoneNumber: verification.phoneNumber,
            details: `Code verification ${verification.success ? "successful" : "failed"}`,
          })
        })
      }
    })

    // Sort activities by timestamp (newest first)
    allActivities.sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    })

    // Limit to the most recent 20 activities
    const recentActivities = allActivities.slice(0, 20)

    console.log(`Returning ${recentActivities.length} recent activities`)
    return NextResponse.json(recentActivities)
  } catch (error) {
    console.error("Error fetching activity feed:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch activity feed",
        details: error.message,
      },
      { status: 500 },
    )
  }
}
