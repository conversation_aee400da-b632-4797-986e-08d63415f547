'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Check, AlertTriangle, ArrowLeft, Info } from 'lucide-react';
import { formatPrice, PRICE_PER_CREDIT_TOMANS } from '@/lib/pricing-config';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import type { WizardFormData } from '../GameWizard';

interface PlanSelectionStepProps {
  formData: WizardFormData;
  updateFormData: (data: Partial<WizardFormData>) => void;
  onBack: () => void;
  onNext: () => void;
}

// Define available plans
const plans = [
  {
    id: 'free',
    name: 'پلن رایگان',
    credits: 5,
    price: 'رایگان',
    description: 'عالی برای تست یا کمپین‌های کوچک',
    features: ['۵ اعتبار شامل', 'تحلیل‌های پایه', 'پشتیبانی استاندارد'],
    recommended: false,
  },
  {
    id: 'starter',
    name: 'شروع',
    credits: 50,
    price: formatPrice(50 * PRICE_PER_CREDIT_TOMANS),
    description: 'عالی برای کسب‌وکارهای کوچک',
    features: ['۵۰ اعتبار شامل', 'تحلیل‌های پیشرفته', 'پشتیبانی اولویت‌دار'],
    recommended: true,
  },
  {
    id: 'business',
    name: 'کسب‌وکار',
    credits: 200,
    price: formatPrice(200 * PRICE_PER_CREDIT_TOMANS),
    description: 'برای کسب‌وکارهای در حال رشد',
    features: ['۲۰۰ اعتبار شامل', 'مجموعه کامل تحلیل‌ها', 'پشتیبانی اولویت‌دار', 'برندینگ سفارشی'],
    recommended: false,
  },
  {
    id: 'enterprise',
    name: 'سازمانی',
    credits: 500,
    price: formatPrice(500 * PRICE_PER_CREDIT_TOMANS),
    description: 'برای کمپین‌های بزرگ',
    features: ['۵۰۰ اعتبار شامل', 'تحلیل‌های سازمانی', 'پشتیبانی ۲۴/۷', 'برندینگ سفارشی', 'دسترسی API'],
    recommended: false,
  },
];

export default function PlanSelectionStep({ formData, updateFormData, onBack, onNext }: PlanSelectionStepProps) {
  const [selectedPlanId, setSelectedPlanId] = useState<string>(formData.selectedPlanId || 'free');
  const [totalPrizes, setTotalPrizes] = useState<number>(0);
  const [showInfoModal, setShowInfoModal] = useState<boolean>(false);

  // Calculate total prize quantity
  useEffect(() => {
    const total = formData.prizes.reduce((sum, prize) => sum + prize.quantity, 0);
    setTotalPrizes(total);

    // Auto-select a recommended plan based on prize count
    const recommendedPlan = plans.find((plan) => plan.credits >= total && !plan.id.includes('enterprise'));
    if (recommendedPlan && !formData.selectedPlanId) {
      setSelectedPlanId(recommendedPlan.id);
      updateFormData({ selectedPlanId: recommendedPlan.id });
    }
  }, [formData.prizes]);

  // Handle plan selection
  const handlePlanSelect = (planId: string) => {
    setSelectedPlanId(planId);
    updateFormData({ selectedPlanId: planId });
  };

  // Get the selected plan
  const selectedPlan = plans.find((plan) => plan.id === selectedPlanId) || plans[0];

  // Handle proceeding to next step (invoice)
  const handleProceedToInvoice = () => {
    // Simply proceed to the invoice step
    onNext();
  };

  return (
    <div className="py-4">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
        <div
          className="inline-flex items-center justify-center h-16 w-16 rounded-full mb-4"
          style={{ backgroundColor: 'rgba(139, 92, 246, 0.1)' }}
        >
          <CreditCard className="h-8 w-8" style={{ color: 'var(--primary)' }} />
        </div>
        <h2 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
          پلن خود را انتخاب کنید
        </h2>
        <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
          پلنی انتخاب کنید که با نیازهای کمپین شما مطابقت دارد
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-6"
      >
        {/* Prize Summary */}
        <div className="bg-blue-50 p-4 rounded-lg flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-blue-700">Credit System Information</h3>
            <p className="text-sm text-blue-600 mt-1">
              Each credit covers the cost of sending two SMS messages to a participant: one for verification and one for
              the winning message.
            </p>
            <div className="mt-3 flex items-center gap-2">
              <span className="text-sm font-medium text-blue-700">شما انتخاب کرده‌اید:</span>
              <span className="bg-blue-200 text-blue-800 px-2 py-0.5 rounded-md text-sm font-medium">
                {totalPrizes} جایزه = {totalPrizes} اعتبار مورد نیاز
              </span>
              <Button
                variant="outline"
                size="sm"
                className="text-xs h-7 bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                onClick={onBack}
              >
                <ArrowLeft className="h-3 w-3 ml-1" />
                تغییر جوایز
              </Button>
            </div>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {plans.map((plan) => {
            const isSelected = selectedPlanId === plan.id;
            const hasEnoughCredits = plan.credits >= totalPrizes;

            return (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 + plans.indexOf(plan) * 0.1 }}
                whileHover={{ scale: 1.03 }}
                className={`cursor-pointer rounded-lg overflow-hidden transition-all border-2 ${
                  isSelected ? 'border-primary shadow-lg' : 'border-gray-200'
                } ${plan.recommended ? 'ring-2 ring-secondary ring-offset-2' : ''}`}
                style={{
                  backgroundColor: 'var(--card-bg)',
                }}
                onClick={() => handlePlanSelect(plan.id)}
              >
                {plan.recommended && (
                  <div className="bg-secondary text-white text-xs font-medium py-1 px-2 text-center">پیشنهادی</div>
                )}

                <div className="p-4">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
                        {plan.name}
                      </h3>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        {plan.description}
                      </p>
                    </div>
                    {isSelected && (
                      <div
                        className="w-6 h-6 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: 'var(--primary)' }}
                      >
                        <Check className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <span className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                      {plan.price}
                    </span>
                  </div>

                  <div className="space-y-2 mb-4">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <Check
                          className="h-4 w-4 mt-0.5"
                          style={{ color: isSelected ? 'var(--primary)' : 'var(--text-secondary)' }}
                        />
                        <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>

                  {!hasEnoughCredits && (
                    <Alert variant="warning" className="mb-3 py-2">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription className="text-xs">
                        Not enough credits for {totalPrizes} prizes
                      </AlertDescription>
                    </Alert>
                  )}

                  <Button
                    variant={isSelected ? 'default' : 'outline'}
                    className="w-full"
                    style={
                      isSelected
                        ? { backgroundColor: 'var(--primary)', color: 'white' }
                        : { borderColor: 'var(--border)' }
                    }
                    onClick={() => handlePlanSelect(plan.id)}
                  >
                    {isSelected ? 'Selected' : 'Select Plan'}
                  </Button>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Selected Plan Summary */}
        <div className="bg-gray-50 p-4 rounded-lg mt-6">
          <h3 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
            Selected Plan: {selectedPlan.name}
          </h3>
          <div className="flex flex-wrap gap-4">
            <div>
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                Credits:
              </span>
              <span className="ml-2 font-medium" style={{ color: 'var(--text-primary)' }}>
                {selectedPlan.credits}
              </span>
            </div>
            <div>
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                Prizes Selected:
              </span>
              <span className="ml-2 font-medium" style={{ color: 'var(--text-primary)' }}>
                {totalPrizes}
              </span>
            </div>
            <div>
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                Credits Remaining After Campaign:
              </span>
              <span
                className={`ml-2 font-medium ${selectedPlan.credits - totalPrizes < 0 ? 'text-red-500' : ''}`}
                style={{ color: selectedPlan.credits - totalPrizes >= 0 ? 'var(--text-primary)' : undefined }}
              >
                {selectedPlan.credits - totalPrizes}
              </span>
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-center items-center mt-8">
          <Button onClick={handleProceedToInvoice} disabled={!selectedPlanId} className="flex items-center gap-2">
            پرداخت
            <ArrowLeft className="w-4 h-4 rotate-180" />
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
