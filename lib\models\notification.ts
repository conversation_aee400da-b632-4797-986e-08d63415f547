import { ObjectId } from "mongodb"
import clientPromise from "../mongodb"

export interface Notification {
  _id?: ObjectId
  userId: string | ObjectId
  type: string
  title: string
  message: string
  link?: string
  isRead: boolean
  isDeleted: boolean
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface NotificationPreferences {
  _id?: ObjectId
  userId: string | ObjectId
  gameUpdates: boolean
  accountAlerts: boolean
  marketingMessages: boolean
  systemAnnouncements: boolean
  createdAt: Date
  updatedAt: Date
}

// Create a new notification
export async function createNotification(
  notificationData: Omit<Notification, "_id" | "isRead" | "isDeleted" | "createdAt" | "updatedAt">,
) {
  const client = await clientPromise
  const collection = client.db().collection("notifications")

  // Ensure userId is stored consistently
  let userId = notificationData.userId
  if (typeof userId === "string" && ObjectId.isValid(userId)) {
    userId = new ObjectId(userId)
  }

  const newNotification = {
    ...notificationData,
    userId,
    isRead: false,
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const result = await collection.insertOne(newNotification)
  return { ...newNotification, _id: result.insertedId }
}

// Get notifications for a user
export async function getUserNotifications(userId: string | ObjectId, includeDeleted = false) {
  const client = await clientPromise
  const collection = client.db().collection("notifications")

  let id = userId
  if (typeof userId === "string" && ObjectId.isValid(userId)) {
    id = new ObjectId(userId)
  }

  const query: any = { userId: id }

  if (!includeDeleted) {
    query.isDeleted = false
  }

  return collection.find(query).sort({ createdAt: -1 }).toArray() as Promise<Notification[]>
}

// Get a single notification by ID
export async function getNotificationById(notificationId: string) {
  const client = await clientPromise
  const collection = client.db().collection("notifications")

  if (!ObjectId.isValid(notificationId)) {
    return null
  }

  return collection.findOne({ _id: new ObjectId(notificationId) }) as Promise<Notification | null>
}

// Mark a notification as read
export async function markNotificationAsRead(notificationId: string) {
  const client = await clientPromise
  const collection = client.db().collection("notifications")

  if (!ObjectId.isValid(notificationId)) {
    return false
  }

  const result = await collection.updateOne(
    { _id: new ObjectId(notificationId) },
    { $set: { isRead: true, updatedAt: new Date() } },
  )

  return result.modifiedCount > 0
}

// Mark all notifications as read for a user
export async function markAllNotificationsAsRead(userId: string | ObjectId) {
  const client = await clientPromise
  const collection = client.db().collection("notifications")

  let id = userId
  if (typeof userId === "string" && ObjectId.isValid(userId)) {
    id = new ObjectId(userId)
  }

  const result = await collection.updateMany(
    { userId: id, isRead: false, isDeleted: false },
    { $set: { isRead: true, updatedAt: new Date() } },
  )

  return result.modifiedCount
}

// Delete a notification (soft delete)
export async function deleteNotification(notificationId: string) {
  const client = await clientPromise
  const collection = client.db().collection("notifications")

  if (!ObjectId.isValid(notificationId)) {
    return false
  }

  const result = await collection.updateOne(
    { _id: new ObjectId(notificationId) },
    { $set: { isDeleted: true, updatedAt: new Date() } },
  )

  return result.modifiedCount > 0
}

// Get notification preferences for a user
export async function getUserNotificationPreferences(userId: string | ObjectId) {
  const client = await clientPromise
  const collection = client.db().collection("notificationPreferences")

  let id = userId
  if (typeof userId === "string" && ObjectId.isValid(userId)) {
    id = new ObjectId(userId)
  }

  const preferences = await collection.findOne({ userId: id })

  if (!preferences) {
    // Create default preferences if none exist
    return createUserNotificationPreferences(userId)
  }

  return preferences as NotificationPreferences
}

// Create default notification preferences for a user
export async function createUserNotificationPreferences(userId: string | ObjectId) {
  const client = await clientPromise
  const collection = client.db().collection("notificationPreferences")

  let id = userId
  if (typeof userId === "string" && ObjectId.isValid(userId)) {
    id = new ObjectId(userId)
  }

  const defaultPreferences = {
    userId: id,
    gameUpdates: true,
    accountAlerts: true,
    marketingMessages: true,
    systemAnnouncements: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const result = await collection.insertOne(defaultPreferences)
  return { ...defaultPreferences, _id: result.insertedId }
}

// Update notification preferences for a user
export async function updateUserNotificationPreferences(
  userId: string | ObjectId,
  preferences: Partial<Omit<NotificationPreferences, "_id" | "userId" | "createdAt" | "updatedAt">>,
) {
  const client = await clientPromise
  const collection = client.db().collection("notificationPreferences")

  let id = userId
  if (typeof userId === "string" && ObjectId.isValid(userId)) {
    id = new ObjectId(userId)
  }

  const result = await collection.updateOne(
    { userId: id },
    {
      $set: {
        ...preferences,
        updatedAt: new Date(),
      },
    },
    { upsert: true },
  )

  return result.modifiedCount > 0 || result.upsertedCount > 0
}

// Count unread notifications for a user
export async function countUnreadNotifications(userId: string | ObjectId) {
  const client = await clientPromise
  const collection = client.db().collection("notifications")

  let id = userId
  if (typeof userId === "string" && ObjectId.isValid(userId)) {
    id = new ObjectId(userId)
  }

  return collection.countDocuments({ userId: id, isRead: false, isDeleted: false })
}

// Send welcome notification to a new user
export async function sendWelcomeNotification(userId: string | ObjectId, userName: string) {
  return createNotification({
    userId,
    type: "welcome",
    title: "Welcome to InstaGameify!",
    message: `Hi ${userName}, welcome to InstaGameify! We're excited to have you on board. Start creating your first game to engage with your Instagram followers.`,
    link: "/dashboard",
    metadata: {
      priority: "high",
    },
  })
}

// Send notification about new features
export async function sendFeatureNotification(
  userId: string | ObjectId,
  featureTitle: string,
  featureDescription: string,
  featureLink: string,
) {
  return createNotification({
    userId,
    type: "feature",
    title: `New Feature: ${featureTitle}`,
    message: featureDescription,
    link: featureLink,
    metadata: {
      priority: "medium",
      feature: featureTitle,
    },
  })
}

// Send game activity notification
export async function sendGameActivityNotification(
  userId: string | ObjectId,
  gameName: string,
  activityType: string,
  details: string,
) {
  return createNotification({
    userId,
    type: "game_activity",
    title: `Activity on ${gameName}`,
    message: details,
    link: `/dashboard/games`,
    metadata: {
      priority: "low",
      gameName,
      activityType,
    },
  })
}
