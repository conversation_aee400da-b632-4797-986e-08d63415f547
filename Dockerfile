FROM node:20.18.0-alpine AS base

# 1. Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./

RUN \
  if [ -f yarn.lock ]; then yarn --verbose --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci --verbose; \
  elif [ -f pnpm-lock.yaml ]; then yarn --verbose global add pnpm && pnpm i; \
  else echo "Lockfile not found." && exit 1; \
  fi

# 2. Rebuild the source code only when needed
FROM base AS builder
RUN apk add --no-cache libc6-compat

WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/package.json /app/yarn.lock* /app/package-lock.json* /app/pnpm-lock.yaml* ./
COPY . .

# Inject environment variables into a .env file
ARG ENVIRONMENT_STATE
ARG NEXT_PUBLIC_BASE_URL

RUN echo "ENVIRONMENT_STATE=${ENVIRONMENT_STATE}" > .env && \
    echo "NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL}" >> .env

# Add the build step here
RUN npm run build

# 3. Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /app/assets ./assets
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder /app/deployments/nginx/ ./nginx_config/

# Copy the docker-entrypoint.sh script
COPY /deployments/bash/docker-entrypoint.sh ./docker-entrypoint.sh
RUN chmod +x ./docker-entrypoint.sh

USER nextjs

EXPOSE 5005

ENV PORT 5005
ENV HOSTNAME 0.0.0.0

ENTRYPOINT ["./docker-entrypoint.sh"]