'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BarChart3, Edit, Eye, FileText, Grid, Plus, Search, Sparkles, Trash2, TrendingUp, Clock } from 'lucide-react';
import { formatSessionTimeShortPersian, formatSessionTimePersian } from '@/lib/utils/sessionUtils';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import GameCard from '@/components/dashboard/GameCard';
import StatsCard from '@/components/dashboard/StatsCard';
import ActivityFeed from '@/components/dashboard/ActivityFeed';
import PerformanceChart from '@/components/dashboard/PerformanceChart';
import GameDetailModal from '@/components/dashboard/games/GameDetailModal';
import type { Game } from '@/types/game';

export default function DashboardPage() {
  const router = useRouter();
  const [view, setView] = useState<'grid' | 'list'>('grid');
  const [games, setGames] = useState([]);
  const [stats, setStats] = useState({
    totalPlays: 0,
    totalConversions: 0,
    conversionRate: 0,
    activeGames: 0,
    avgSessionTime: 0,
    trends: {
      playsChange: 0,
      conversionsChange: 0,
      rateChange: 0,
      activeGamesChange: 0,
      sessionTimeChange: 0,
    },
  });
  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [chartPeriod, setChartPeriod] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [showGameModal, setShowGameModal] = useState(false);

  // Fetch dashboard stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        console.log('Fetching dashboard stats...');
        const response = await fetch('/api/dashboard/stats');

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Stats API error:', errorData);
          throw new Error(`Failed to fetch stats: ${errorData.error || response.statusText}`);
        }

        const data = await response.json();
        console.log('Stats data received:', data);
        setStats(data);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        setError('خطا در بارگذاری آمار. لطفاً صفحه را تازه‌سازی کنید.');
      }
    };

    fetchStats();
  }, []);

  // Fetch games
  useEffect(() => {
    const fetchGames = async () => {
      try {
        console.log('Fetching games...');
        const response = await fetch('/api/dashboard/games');

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Games API error:', errorData);
          throw new Error(`Failed to fetch games: ${errorData.error || response.statusText}`);
        }

        const data = await response.json();
        console.log('Games data received:', data);
        setGames(data);
      } catch (error) {
        console.error('Error fetching games:', error);
        setError('خطا در بارگذاری بازی‌ها. لطفاً صفحه را تازه‌سازی کنید.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchGames();
  }, []);

  // Fetch activity feed
  useEffect(() => {
    const fetchActivities = async () => {
      try {
        console.log('Fetching activities...');
        const response = await fetch('/api/dashboard/activity');

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Activity API error:', errorData);
          throw new Error(`Failed to fetch activities: ${errorData.error || response.statusText}`);
        }

        const data = await response.json();
        console.log('Activities data received:', data);
        setActivities(data);
      } catch (error) {
        console.error('Error fetching activities:', error);
        // Don't set error state here to avoid overwhelming the user with error messages
      }
    };

    fetchActivities();
  }, []);

  // Handle game detail modal
  const handleViewGameDetails = (game: any) => {
    setSelectedGame(game);
    setShowGameModal(true);
  };

  const handleCloseGameModal = () => {
    setShowGameModal(false);
    setSelectedGame(null);
  };

  // Calculate dynamic engagement insights
  const calculateEngagementInsights = () => {
    if (!games || games.length === 0) {
      return {
        avgTimeSpent: '0 دقیقه',
        avgTimePercentage: 0,
        repeatPlayersPercentage: 0,
        mobilePlayersPercentage: 0,
        topPerformingGame: null,
      };
    }

    // Calculate session metrics from games
    const totalPlays = games.reduce((sum: number, game: any) => sum + game.plays, 0);
    const totalConversions = games.reduce((sum: number, game: any) => sum + game.conversions, 0);
    const avgConversionRate = totalPlays > 0 ? (totalConversions / totalPlays) * 100 : 0;

    // Calculate unified average session time across all games
    // This will be used for both the stats card and engagement insights
    const calculateUnifiedAverageSessionTime = () => {
      let totalSessionTime = 0;
      let totalCompletedSessions = 0;

      games.forEach((game: any) => {
        // Method 1: Use sessionMetrics if available (most reliable)
        if (game.sessionMetrics?.averageSessionTime && game.sessionMetrics.averageSessionTime > 0) {
          const gameAvgTime = game.sessionMetrics.averageSessionTime;
          const gameSessions = game.sessionMetrics.totalSessions || 1;
          totalSessionTime += gameAvgTime * gameSessions;
          totalCompletedSessions += gameSessions;
        }
        // Method 2: Calculate from individual sessions
        else if (game.gameInteractions?.sessions) {
          const completedSessions = game.gameInteractions.sessions.filter((s: any) => s.completed && s.duration);
          if (completedSessions.length > 0) {
            const gameTotal = completedSessions.reduce((sum: number, s: any) => sum + (s.duration || 0), 0);
            totalSessionTime += gameTotal;
            totalCompletedSessions += completedSessions.length;
          }
        }
      });

      return totalCompletedSessions > 0 ? Math.round(totalSessionTime / totalCompletedSessions) : 0;
    };

    const unifiedAvgSessionTime = calculateUnifiedAverageSessionTime();

    // Use the unified calculation for both displays
    const avgTimeSpent = unifiedAvgSessionTime > 0 ? formatSessionTimePersian(unifiedAvgSessionTime) : '0 ثانیه';
    const avgTimePercentage = Math.min(100, (unifiedAvgSessionTime / 180) * 100);

    // Calculate repeat players (simulated based on active games)
    const activeGamesCount = games.filter((game: any) => game.status === 'active').length;
    const repeatPlayersPercentage = Math.min(50, Math.max(10, (activeGamesCount / games.length) * 40));

    // Calculate mobile players (simulated based on total engagement)
    const mobilePlayersPercentage = Math.min(95, Math.max(60, 70 + avgConversionRate / 2));

    // Find top performing game
    const topPerformingGame = games.reduce((best: any, current: any) => {
      const currentRate = current.plays > 0 ? (current.conversions / current.plays) * 100 : 0;
      const bestRate = best && best.plays > 0 ? (best.conversions / best.plays) * 100 : 0;
      return currentRate > bestRate ? current : best;
    }, null);

    return {
      avgTimeSpent,
      avgTimePercentage,
      avgSessionTime: unifiedAvgSessionTime,
      repeatPlayersPercentage,
      mobilePlayersPercentage,
      topPerformingGame,
    };
  };

  const engagementInsights = calculateEngagementInsights();

  // Update stats when games data changes
  useEffect(() => {
    if (games.length > 0) {
      const totalPlays = games.reduce((sum: number, game: any) => sum + game.plays, 0);
      const totalConversions = games.reduce((sum: number, game: any) => sum + game.conversions, 0);
      const avgConversionRate = totalPlays > 0 ? (totalConversions / totalPlays) * 100 : 0;
      const activeGamesCount = games.filter((game: any) => game.status === 'active').length;

      setStats((prevStats) => ({
        ...prevStats,
        totalPlays,
        totalConversions,
        conversionRate: Math.round(avgConversionRate * 100) / 100,
        activeGames: activeGamesCount,
        avgSessionTime: engagementInsights.avgSessionTime || 0,
      }));
    }
  }, [games, engagementInsights.avgSessionTime]);

  // Filter games based on search term
  const filteredGames = games.filter((game: any) => game.name.toLowerCase().includes(searchTerm.toLowerCase()));

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <DashboardSidebar />

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Header */}
          <DashboardHeader />

          {/* Dashboard Content */}
          <main className="p-4 md:p-6 space-y-6">
            {/* Welcome Section */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                  خوش آمدید، صاحب فروشگاه!
                </h1>
                <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                  اینجا می‌توانید وضعیت بازی‌های امروز خود را مشاهده کنید.
                </p>
              </div>
              <Button
                onClick={() => router.push('/create-game')}
                className="flex items-center gap-2"
                style={{
                  backgroundColor: 'var(--secondary)',
                  color: 'var(--button-text)',
                }}
              >
                <Plus className="h-4 w-4" />
                ایجاد بازی جدید
              </Button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span className="block sm:inline">{error}</span>
                <button className="absolute top-0 bottom-0 right-0 px-4 py-3" onClick={() => setError('')}>
                  <span className="sr-only">بستن</span>
                  <svg
                    className="fill-current h-6 w-6 text-red-500"
                    role="button"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                  </svg>
                </button>
              </div>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
              <StatsCard
                title="کل بازی‌ها"
                value={stats.totalPlays.toLocaleString()}
                icon={Eye}
                trend={stats.trends.playsChange}
                color="#8b5cf6"
                tooltip="تعداد کل دفعاتی که کاربران بازی‌های شما را بازی کرده‌اند. این شامل تمام تعاملات با بازی‌ها می‌شود."
              />
              <StatsCard
                title="تبدیل‌ها"
                value={stats.totalConversions.toLocaleString()}
                icon={TrendingUp}
                trend={stats.trends.conversionsChange}
                color="#ec4899"
                tooltip="تعداد کاربرانی که پس از بازی کردن، اطلاعات تماس خود را وارد کرده‌اند و به مشتری بالقوه تبدیل شده‌اند."
              />
              <StatsCard
                title="نرخ تبدیل"
                value={`${stats.conversionRate}%`}
                icon={BarChart3}
                trend={stats.trends.rateChange}
                color="#f97316"
                tooltip="درصد کاربرانی که پس از بازی کردن، به مشتری بالقوه تبدیل شده‌اند. این نسبت تبدیل‌ها به کل بازی‌ها است."
              />
              <StatsCard
                title="بازی‌های فعال"
                value={stats.activeGames.toString()}
                icon={Sparkles}
                trend={stats.trends.activeGamesChange}
                color="#84cc16"
                tooltip="تعداد بازی‌هایی که در حال حاضر فعال هستند و کاربران می‌توانند آن‌ها را بازی کنند."
              />
              <StatsCard
                title="میانگین زمان جلسه"
                value={formatSessionTimeShortPersian(engagementInsights.avgSessionTime || 0)}
                icon={Clock}
                trend={stats.trends.sessionTimeChange}
                color="#06b6d4"
                tooltip="میانگین زمانی که کاربران در هر جلسه بازی صرف می‌کنند."
              />
            </div>

            {/* Performance Chart */}
            <div className="relative">
              <Card
                className=" shadow-lg hover:shadow-xl transition-all duration-300 pb-4"
                style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
              >
                <CardHeader className="pb-4 relative z-10">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        نمای کلی عملکرد
                      </CardTitle>
                      <CardDescription className="text-gray-600 mt-1">بازی‌ها و تبدیل‌ها در طول زمان</CardDescription>
                    </div>
                    <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-lg p-1 border border-gray-200">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs transition-all duration-200 border-0 hover:bg-purple-100"
                        style={{
                          backgroundColor: chartPeriod === '7days' ? 'var(--primary)' : 'transparent',
                          color: chartPeriod === '7days' ? 'white' : 'var(--text-primary)',
                          boxShadow: chartPeriod === '7days' ? '0 2px 8px rgba(139, 92, 246, 0.3)' : 'none',
                        }}
                        onClick={() => setChartPeriod('7days')}
                      >
                        ۷ روز گذشته
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs transition-all duration-200 border-0 hover:bg-purple-100"
                        style={{
                          backgroundColor: chartPeriod === '30days' ? 'var(--primary)' : 'transparent',
                          color: chartPeriod === '30days' ? 'white' : 'var(--text-primary)',
                          boxShadow: chartPeriod === '30days' ? '0 2px 8px rgba(139, 92, 246, 0.3)' : 'none',
                        }}
                        onClick={() => setChartPeriod('30days')}
                      >
                        ۳۰ روز گذشته
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs transition-all duration-200 border-0 hover:bg-purple-100"
                        style={{
                          backgroundColor: chartPeriod === 'all' ? 'var(--primary)' : 'transparent',
                          color: chartPeriod === 'all' ? 'white' : 'var(--text-primary)',
                          boxShadow: chartPeriod === 'all' ? '0 2px 8px rgba(139, 92, 246, 0.3)' : 'none',
                        }}
                        onClick={() => setChartPeriod('all')}
                      >
                        همه زمان‌ها
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="relative z-10">
                  <div className="h-[400px] bg-white/60 backdrop-blur-sm rounded-lg p-3 pb-8 border border-gray-100">
                    <PerformanceChart period={chartPeriod} />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Your Games Section */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                  بازی‌های شما
                </h2>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="h-4 w-4 absolute right-2.5 top-2.5" style={{ color: 'var(--text-secondary)' }} />
                    <input
                      type="text"
                      placeholder="جستجوی بازی‌ها..."
                      className="pr-9 pl-4 py-2 text-sm rounded-md border"
                      style={{
                        borderColor: 'var(--border)',
                        backgroundColor: 'var(--background)',
                        color: 'var(--text-primary)',
                      }}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="flex border rounded-md" style={{ borderColor: 'var(--border)' }}>
                    <button
                      className={`p-2 ${view === 'grid' ? 'bg-gray-100' : ''}`}
                      onClick={() => setView('grid')}
                      aria-label="نمای شبکه‌ای"
                    >
                      <Grid className="h-4 w-4" style={{ color: 'var(--text-primary)' }} />
                    </button>
                    <button
                      className={`p-2 ${view === 'list' ? 'bg-gray-100' : ''}`}
                      onClick={() => setView('list')}
                      aria-label="نمای لیستی"
                    >
                      <FileText className="h-4 w-4" style={{ color: 'var(--text-primary)' }} />
                    </button>
                  </div>
                </div>
              </div>

              {/* Games Grid/List */}
              {isLoading ? (
                <div className="flex justify-center items-center h-40">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : filteredGames.length === 0 ? (
                <div className="text-center p-8 border rounded-lg">
                  <h3 className="text-lg font-medium mb-2">هیچ بازی‌ای یافت نشد</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    {searchTerm
                      ? 'هیچ بازی‌ای با معیارهای جستجوی شما مطابقت ندارد.'
                      : 'اولین بازی خود را ایجاد کنید تا شروع کنید'}
                  </p>
                  <Button onClick={() => router.push('/create-game')}>
                    <Plus className="h-4 w-4 mr-2" />
                    ایجاد بازی
                  </Button>
                </div>
              ) : view === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredGames.map((game: any) => (
                    <GameCard key={game.id} game={game} onViewDetails={() => handleViewGameDetails(game)} />
                  ))}
                </div>
              ) : (
                <Card>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b" style={{ borderColor: 'var(--border)' }}>
                          <th className="text-right p-3 text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                            نام بازی
                          </th>
                          <th className="text-right p-3 text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                            نوع
                          </th>
                          <th className="text-right p-3 text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                            بازی‌ها
                          </th>
                          <th className="text-right p-3 text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                            تبدیل‌ها
                          </th>
                          <th className="text-right p-3 text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                            وضعیت
                          </th>
                          <th className="text-right p-3 text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                            عملیات
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredGames.map((game: any) => (
                          <tr key={game.id} className="border-b" style={{ borderColor: 'var(--border)' }}>
                            <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                              {game.name}
                            </td>
                            <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                              {game.type === 'wheel' ? 'چرخ شانس' : game.type === 'lever' ? 'اهرم شانس' : 'جعبه هدیه'}
                            </td>
                            <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                              {game.plays.toLocaleString()}
                            </td>
                            <td className="p-3" style={{ color: 'var(--text-primary)' }}>
                              {game.conversions.toLocaleString()}
                            </td>
                            <td className="p-3">
                              <span
                                className={`px-2 py-1 rounded-full text-xs ${
                                  game.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                                }`}
                              >
                                {game.status === 'active' ? 'فعال' : 'غیرفعال'}
                              </span>
                            </td>
                            <td className="p-3">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  style={{ color: 'var(--primary)' }}
                                  title="ویرایش بازی"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  style={{ color: 'var(--text-secondary)' }}
                                  onClick={() => handleViewGameDetails(game)}
                                  title="مشاهده جزئیات"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-500" title="حذف بازی">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </Card>
              )}
            </div>

            {/* Bottom Section: Activity Feed and Quick Stats */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Activity Feed */}
              <Card
                className="lg:col-span-2"
                style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
              >
                <CardHeader>
                  <CardTitle style={{ color: 'var(--text-primary)' }}>فعالیت‌های اخیر</CardTitle>
                  <CardDescription>آخرین تعاملات با بازی‌های شما</CardDescription>
                </CardHeader>
                <CardContent>
                  <ActivityFeed activities={activities} />
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}>
                <CardHeader>
                  <CardTitle style={{ color: 'var(--text-primary)' }}>بینش‌های مشارکت</CardTitle>
                  <CardDescription>معیارهای کلیدی برای بازی‌های شما</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Average Time Spent */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                        میانگین زمان صرف شده
                      </span>
                      <span className="text-sm font-bold" style={{ color: 'var(--text-primary)' }}>
                        {engagementInsights.avgTimeSpent}
                      </span>
                    </div>
                    <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className="h-full rounded-full"
                        style={{
                          width: `${engagementInsights.avgTimePercentage}%`,
                          backgroundColor: 'var(--primary)',
                        }}
                      ></div>
                    </div>
                  </div>

                  {/* Repeat Players */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                        بازیکنان مکرر
                      </span>
                      <span className="text-sm font-bold" style={{ color: 'var(--text-primary)' }}>
                        {Math.round(engagementInsights.repeatPlayersPercentage)}٪
                      </span>
                    </div>
                    <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className="h-full rounded-full"
                        style={{ width: `${engagementInsights.repeatPlayersPercentage}%`, backgroundColor: '#ec4899' }}
                      ></div>
                    </div>
                  </div>

                  {/* Mobile vs Desktop */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                        بازیکنان موبایل
                      </span>
                      <span className="text-sm font-bold" style={{ color: 'var(--text-primary)' }}>
                        {Math.round(engagementInsights.mobilePlayersPercentage)}٪
                      </span>
                    </div>
                    <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className="h-full rounded-full"
                        style={{ width: `${engagementInsights.mobilePlayersPercentage}%`, backgroundColor: '#f97316' }}
                      ></div>
                    </div>
                  </div>

                  {/* Top Performing Game */}
                  <div className="pt-4 border-t" style={{ borderColor: 'var(--border)' }}>
                    <h4 className="text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                      بازی با بهترین عملکرد
                    </h4>
                    {engagementInsights.topPerformingGame ? (
                      <div className="flex items-center gap-3">
                        <div
                          className="w-10 h-10 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: 'rgba(139, 92, 246, 0.1)' }}
                        >
                          <Sparkles className="h-5 w-5" style={{ color: 'var(--primary)' }} />
                        </div>
                        <div>
                          <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                            {engagementInsights.topPerformingGame.name}
                          </p>
                          <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                            {engagementInsights.topPerformingGame.plays > 0
                              ? `${Math.round(
                                  (engagementInsights.topPerformingGame.conversions /
                                    engagementInsights.topPerformingGame.plays) *
                                    100
                                )}% نرخ تبدیل`
                              : 'هنوز بازی نشده'}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">هیچ بازی‌ای در دسترس نیست</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>

      {/* Game Detail Modal */}
      {showGameModal && selectedGame && (
        <GameDetailModal
          game={selectedGame}
          onClose={handleCloseGameModal}
          onUpdate={(updatedGame) => {
            // Update the game in local state if needed
            setSelectedGame(updatedGame);
          }}
        />
      )}
    </div>
  );
}
