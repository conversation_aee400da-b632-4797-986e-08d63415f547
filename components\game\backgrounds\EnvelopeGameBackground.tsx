"use client"

import { motion } from "framer-motion"
import { Gift } from "lucide-react"

interface EnvelopeGameBackgroundProps {
  primaryColor: string
  secondaryColor: string
}

export default function EnvelopeGameBackground({ primaryColor, secondaryColor }: EnvelopeGameBackgroundProps) {
  return (
    <div className="absolute inset-0 overflow-hidden opacity-30">
      <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent" />

      {/* Background gradient */}
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(135deg, ${primaryColor}, ${secondaryColor})`,
        }}
      />

      {/* Floating envelopes */}
      {[...Array(12)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -20, 0],
            rotate: [0, Math.random() > 0.5 ? 10 : -10, 0],
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            repeat: Number.POSITIVE_INFINITY,
            delay: Math.random() * 5,
          }}
        >
          <Gift
            size={30 + Math.floor(Math.random() * 40)}
            style={{ color: Math.random() > 0.5 ? primaryColor : secondaryColor }}
          />
        </motion.div>
      ))}

      {/* Large decorative envelopes */}
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={`large-${i}`}
          className="absolute rounded-lg"
          style={{
            width: 100 + Math.random() * 100,
            height: 140 + Math.random() * 100,
            left: `${20 + i * 30}%`,
            top: `${20 + i * 20}%`,
            backgroundColor: Math.random() > 0.5 ? primaryColor : secondaryColor,
            opacity: 0.2,
          }}
          animate={{
            rotate: [0, Math.random() > 0.5 ? 10 : -10, 0],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 5 + Math.random() * 3,
            repeat: Number.POSITIVE_INFINITY,
            delay: Math.random() * 2,
          }}
        />
      ))}
    </div>
  )
}
