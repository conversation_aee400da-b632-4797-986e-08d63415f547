import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'You must be logged in' }, { status: 401 });
    }

    const userId = session.user.id;
    console.log('DEBUG: Checking games for userId:', userId, 'Type:', typeof userId);

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db('instagram_gamification');

    // Get all games for this user
    const allGames = await db.collection('games').find({}).toArray();
    console.log('DEBUG: Total games in database:', allGames.length);

    // Find games for this user with different userId formats
    const userGamesString = await db.collection('games').find({ userId: userId }).toArray();
    const userGamesObjectId = ObjectId.isValid(userId) 
      ? await db.collection('games').find({ userId: new ObjectId(userId) }).toArray()
      : [];

    console.log('DEBUG: Games found with string userId:', userGamesString.length);
    console.log('DEBUG: Games found with ObjectId userId:', userGamesObjectId.length);

    // Sample of all games to see the structure
    const sampleGames = allGames.slice(0, 10).map(game => ({
      _id: game._id.toString(),
      name: game.name,
      userId: game.userId,
      userIdType: typeof game.userId,
      status: game.status
    }));

    return NextResponse.json({
      success: true,
      debug: {
        sessionUserId: userId,
        sessionUserIdType: typeof userId,
        totalGamesInDb: allGames.length,
        userGamesWithString: userGamesString.length,
        userGamesWithObjectId: userGamesObjectId.length,
        sampleGames: sampleGames,
        userGamesString: userGamesString.map(g => ({
          _id: g._id.toString(),
          name: g.name,
          status: g.status
        })),
        userGamesObjectId: userGamesObjectId.map(g => ({
          _id: g._id.toString(),
          name: g.name,
          status: g.status
        }))
      }
    });

  } catch (error) {
    console.error('DEBUG API Error:', error);
    return NextResponse.json({ error: 'Debug failed', details: error.message }, { status: 500 });
  }
}
