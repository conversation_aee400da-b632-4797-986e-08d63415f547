// Create a global OTP store that persists between API calls
// Key: phoneNumber, Value: { otp: string, expiresAt: Date }
export type OTPData = {
  otp: string
  expiresAt: Date
}

// Use a global variable to ensure the store persists between serverless function invocations
declare global {
  var otpStore: Map<string, OTPData>
}

// Initialize the global OTP store if it doesn't exist
if (!global.otpStore) {
  global.otpStore = new Map<string, OTPData>()
}

// Helper function to normalize phone numbers for consistent storage/lookup
export function normalizePhoneNumber(phoneNumber: string): string {
  // Remove any non-digit characters and ensure consistent format
  return phoneNumber.replace(/\D/g, "")
}

// Store an OTP for a phone number
export function storeOTP(phoneNumber: string, otp: string, expirationMinutes = 5): void {
  const normalizedPhone = normalizePhoneNumber(phoneNumber)

  const expiresAt = new Date()
  expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes)

  global.otpStore.set(normalizedPhone, { otp, expiresAt })
  console.log(`Stored OTP for ${normalizedPhone}: ${otp} (expires in ${expirationMinutes} minutes)`)
}

// Get the stored OTP for a phone number
export function getOTP(phoneNumber: string): OTPData | undefined {
  const normalizedPhone = normalizePhoneNumber(phoneNumber)
  const otpData = global.otpStore.get(normalizedPhone)

  console.log(`Retrieved OTP for ${normalizedPhone}: ${otpData ? otpData.otp : "not found"}`)
  return otpData
}

// Delete the stored OTP for a phone number
export function deleteOTP(phoneNumber: string): boolean {
  const normalizedPhone = normalizePhoneNumber(phoneNumber)
  const result = global.otpStore.delete(normalizedPhone)
  console.log(`Deleted OTP for ${normalizedPhone}: ${result ? "success" : "not found"}`)
  return result
}

// Debug function to list all stored OTPs (for development only)
export function listAllOTPs(): {
  phoneNumber: string
  otp: string
  expiresAt: Date
}[] {
  const otps: { phoneNumber: string; otp: string; expiresAt: Date }[] = []

  global.otpStore.forEach((data, phoneNumber) => {
    otps.push({
      phoneNumber,
      otp: data.otp,
      expiresAt: data.expiresAt,
    })
  })

  return otps
}
