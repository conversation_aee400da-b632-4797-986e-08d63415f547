'use client';

import { motion } from 'framer-motion';
import { Instagram } from 'lucide-react';
import type { WizardFormData } from '../GameWizard';

interface InstagramInfoStepProps {
  formData: WizardFormData;
  updateFormData: (data: Partial<WizardFormData>) => void;
}

const categories = [
  'مد و پوشاک',
  'زیبایی',
  'تناسب اندام',
  'غذا',
  'سفر',
  'فناوری',
  'هنر',
  'موسیقی',
  'آموزش',
  'کسب‌وکار',
  'سبک زندگی',
  'سلامت',
  'بازی',
  'ورزش',
  'سایر',
];

const followerRanges = [
  'کمتر از ۱ هزار',
  '۱ تا ۵ هزار',
  '۵ تا ۱۰ هزار',
  '۱۰ تا ۵۰ هزار',
  '۵۰ تا ۱۰۰ هزار',
  '۱۰۰ تا ۵۰۰ هزار',
  '۵۰۰ هزار تا ۱ میلیون',
  'بیش از ۱ میلیون',
];

export default function InstagramInfoStep({ formData, updateFormData }: InstagramInfoStepProps) {
  return (
    <div className="py-4">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
        <div
          className="inline-flex items-center justify-center h-16 w-16 rounded-full mb-4"
          style={{ backgroundColor: 'rgba(139, 92, 246, 0.1)' }}
        >
          <Instagram className="h-8 w-8" style={{ color: 'var(--primary)' }} />
        </div>
        <h2 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
          ابتدا درباره صفحه‌تان بگویید
        </h2>
        <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
          این به ما کمک می‌کند تجربه بازی شما را شخصی‌سازی کنیم
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-6 max-w-md mx-auto"
      >
        {/* Instagram Handle */}
        <div>
          <label className="block text-sm font-medium mb-1.5" style={{ color: 'var(--text-primary)' }}>
            نام کاربری اینستاگرام*
          </label>
          <div className="relative">
            <span className="absolute right-3 top-1/2 -translate-y-1/2" style={{ color: 'var(--text-secondary)' }}>
              @
            </span>
            <input
              type="text"
              value={formData.instagramHandle.replace('@', '')}
              onChange={(e) => updateFormData({ instagramHandle: e.target.value })}
              className="w-full px-3 py-2 pr-7 border rounded-md"
              style={{
                backgroundColor: 'var(--background)',
                color: 'var(--text-primary)',
                borderColor: 'var(--border)',
              }}
              placeholder="نام کاربری"
              required
            />
          </div>
        </div>

        {/* Page Category */}
        <div>
          <label className="block text-sm font-medium mb-1.5" style={{ color: 'var(--text-primary)' }}>
            دسته‌بندی صفحه
          </label>
          <select
            value={formData.pageCategory}
            onChange={(e) => updateFormData({ pageCategory: e.target.value })}
            className="w-full px-3 py-2 border rounded-md appearance-none"
            style={{
              backgroundColor: 'var(--background)',
              color: 'var(--text-primary)',
              borderColor: 'var(--border)',
            }}
          >
            <option value="">دسته‌بندی انتخاب کنید</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Follower Count */}
        <div>
          <label className="block text-sm font-medium mb-1.5" style={{ color: 'var(--text-primary)' }}>
            تعداد تقریبی فالوور (اختیاری)
          </label>
          <select
            value={formData.followerCount}
            onChange={(e) => updateFormData({ followerCount: e.target.value })}
            className="w-full px-3 py-2 border rounded-md appearance-none"
            style={{
              backgroundColor: 'var(--background)',
              color: 'var(--text-primary)',
              borderColor: 'var(--border)',
            }}
          >
            <option value="">محدوده انتخاب کنید</option>
            {followerRanges.map((range) => (
              <option key={range} value={range}>
                {range}
              </option>
            ))}
          </select>
        </div>
      </motion.div>
    </div>
  );
}
