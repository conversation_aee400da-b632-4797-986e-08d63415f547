/**
 * Centralized Pricing Configuration
 *
 * This file contains all pricing-related configurations for the application.
 * To change prices, simply modify the values in this file.
 */

// Base price per credit in Tomans
export const PRICE_PER_CREDIT_TOMANS = 2000;

// Currency configuration
export const CURRENCY = {
  symbol: 'تومان',
  code: 'IRR',
  name: 'تومان ایران',
};

// Credit packages for the credits page
export const CREDIT_PACKAGES = [
  {
    name: 'شروع',
    credits: 10,
    popular: false,
    discount: 0, // No discount for starter package
  },
  {
    name: 'استاندارد',
    credits: 50,
    popular: true,
    discount: 0.1, // 10% discount
  },
  {
    name: 'پریمیوم',
    credits: 100,
    popular: false,
    discount: 0.15, // 15% discount
  },
];

// Subscription plans for the main page
export const SUBSCRIPTION_PLANS = [
  {
    name: 'رایگان',
    pricePerMonth: 0,
    description: 'عالی برای شروع',
    features: ['۱ نوع بازی', 'سفارشی‌سازی پایه', '۱۰۰ بازی در ماه', 'پشتیبانی ایمیل'],
    cta: 'شروع رایگان',
    popular: false,
    creditsIncluded: 100, // Free credits per month
  },
  {
    name: 'رشد',
    pricePerMonth: 580000, // 580,000 Tomans per month
    description: 'برای فروشگاه‌های اینستاگرام در حال رشد',
    features: ['همه انواع بازی', 'سفارشی‌سازی کامل', '۱٬۰۰۰ بازی در ماه', 'پشتیبانی اولویت‌دار', 'داشبورد تحلیل‌ها'],
    cta: 'شروع آزمایش ۱۴ روزه',
    popular: true,
    creditsIncluded: 1000, // Credits per month
  },
  {
    name: 'حرفه‌ای',
    pricePerMonth: 1580000, // 1,580,000 Tomans per month
    description: 'برای کسب‌وکارهای تثبیت شده',
    features: [
      'همه انواع بازی',
      'سفارشی‌سازی پیشرفته',
      'بازی نامحدود',
      'پشتیبانی اولویت‌دار',
      'تحلیل‌های پیشرفته',
      'برندینگ سفارشی',
      'دسترسی API',
    ],
    cta: 'تماس با فروش',
    popular: false,
    creditsIncluded: -1, // Unlimited
  },
];

// Utility functions for pricing calculations
export const calculateCreditPackagePrice = (credits: number, discount: number = 0): number => {
  const basePrice = credits * PRICE_PER_CREDIT_TOMANS;
  return Math.round(basePrice * (1 - discount));
};

export const calculatePricePerCredit = (totalPrice: number, credits: number): number => {
  return Math.round(totalPrice / credits);
};

export const formatPrice = (price: number): string => {
  // Convert to Persian numerals and add thousand separators
  const formattedNumber = price.toLocaleString('fa-IR');
  return `${formattedNumber} ${CURRENCY.symbol}`;
};

export const formatPricePerCredit = (price: number): string => {
  return `${price.toLocaleString('fa-IR')} ${CURRENCY.symbol} هر اعتبار`;
};

// Get calculated credit packages with prices
export const getCreditPackagesWithPrices = () => {
  return CREDIT_PACKAGES.map((pkg) => ({
    ...pkg,
    price: calculateCreditPackagePrice(pkg.credits, pkg.discount),
    pricePerCredit: calculatePricePerCredit(calculateCreditPackagePrice(pkg.credits, pkg.discount), pkg.credits),
  }));
};

// Get subscription plans with formatted prices
export const getSubscriptionPlansWithPrices = () => {
  return SUBSCRIPTION_PLANS.map((plan) => ({
    ...plan,
    formattedPrice: plan.pricePerMonth === 0 ? 'رایگان' : formatPrice(plan.pricePerMonth),
  }));
};
