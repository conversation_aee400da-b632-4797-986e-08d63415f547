/**
 * ZarinPal Payment Gateway Service
 *
 * This service handles all interactions with the ZarinPal payment gateway API.
 * It provides methods for payment request, verification, and error handling.
 */

import {
  ZARINPAL_CONFIG,
  getZarinPalEndpoints,
  validateZarinPalConfig,
  getZarinPalStatusMessage,
  ZARINPAL_STATUS_CODES,
  type ZarinPalRequestResponse,
  type ZarinPalVerifyResponse,
  type ZarinPalRequestPayload,
  type ZarinPalVerifyPayload,
} from './zarinpal-config';

export class ZarinPalService {
  private endpoints = getZarinPalEndpoints();
  private isConfigured = false;

  constructor() {
    // Validate configuration on initialization, but don't throw during build
    const validation = validateZarinPalConfig();
    if (!validation.isValid) {
      console.warn('ZarinPal configuration errors:', validation.errors);
      // Only throw in runtime, not during build
      if (typeof window !== 'undefined' || process.env.NODE_ENV !== 'production') {
        this.isConfigured = false;
      }
    } else {
      this.isConfigured = true;
    }
  }

  private checkConfiguration() {
    if (!this.isConfigured) {
      const validation = validateZarinPalConfig();
      if (!validation.isValid) {
        throw new Error(`ZarinPal configuration is invalid: ${validation.errors.join(', ')}`);
      }
      this.isConfigured = true;
    }
  }

  /**
   * Request a new payment from ZarinPal
   * @param amount Payment amount in Tomans
   * @param description Payment description
   * @param metadata Optional metadata (mobile, email, order_id)
   * @returns Promise with payment authority and redirect URL
   */
  async requestPayment(
    amount: number,
    description: string,
    metadata?: { mobile?: string; email?: string; order_id?: string }
  ): Promise<{ authority: string; paymentUrl: string }> {
    this.checkConfiguration();
    try {
      const payload: ZarinPalRequestPayload = {
        merchant_id: ZARINPAL_CONFIG.merchantId,
        amount,
        description,
        callback_url: ZARINPAL_CONFIG.callbackUrl,
        metadata,
      };

      console.log('ZarinPal Request Payload:', payload);

      const response = await fetch(this.endpoints.REQUEST, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ZarinPalRequestResponse = await response.json();
      console.log('ZarinPal Request Response:', data);

      if (data.errors && data.errors.length > 0) {
        throw new Error(`ZarinPal API Error: ${data.errors.join(', ')}`);
      }

      if (!data.data || !data.data.authority) {
        throw new Error('Invalid response from ZarinPal API');
      }

      const paymentUrl = `${this.endpoints.START_PAY}${data.data.authority}`;

      return {
        authority: data.data.authority,
        paymentUrl,
      };
    } catch (error) {
      console.error('ZarinPal request payment error:', error);
      throw new Error(`Failed to request payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Verify a payment with ZarinPal
   * @param authority Payment authority from callback
   * @param amount Original payment amount in Tomans
   * @returns Promise with verification result
   */
  async verifyPayment(
    authority: string,
    amount: number
  ): Promise<{
    success: boolean;
    refId?: number;
    cardPan?: string;
    cardHash?: string;
    fee?: number;
    message: string;
    code: number;
  }> {
    this.checkConfiguration();
    try {
      const payload: ZarinPalVerifyPayload = {
        merchant_id: ZARINPAL_CONFIG.merchantId,
        amount,
        authority,
      };

      console.log('ZarinPal Verify Payload:', payload);

      const response = await fetch(this.endpoints.VERIFY, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ZarinPalVerifyResponse = await response.json();
      console.log('ZarinPal Verify Response:', data);

      if (data.errors && data.errors.length > 0) {
        return {
          success: false,
          message: data.errors.join(', '),
          code: -1,
        };
      }

      if (!data.data) {
        return {
          success: false,
          message: 'Invalid response from ZarinPal API',
          code: -1,
        };
      }

      const isSuccess =
        data.data.code === ZARINPAL_STATUS_CODES.SUCCESS || data.data.code === ZARINPAL_STATUS_CODES.ALREADY_VERIFIED;

      return {
        success: isSuccess,
        refId: data.data.ref_id,
        cardPan: data.data.card_pan,
        cardHash: data.data.card_hash,
        fee: data.data.fee,
        message: getZarinPalStatusMessage(data.data.code),
        code: data.data.code,
      };
    } catch (error) {
      console.error('ZarinPal verify payment error:', error);
      return {
        success: false,
        message: `Failed to verify payment: ${error instanceof Error ? error.message : 'Unknown error'}`,
        code: -1,
      };
    }
  }

  /**
   * Generate payment URL from authority
   * @param authority Payment authority
   * @returns Payment URL
   */
  generatePaymentUrl(authority: string): string {
    return `${this.endpoints.START_PAY}${authority}`;
  }

  /**
   * Check if the service is configured for sandbox mode
   * @returns boolean indicating sandbox mode
   */
  isSandboxMode(): boolean {
    return ZARINPAL_CONFIG.isSandbox;
  }

  /**
   * Get current configuration (without sensitive data)
   * @returns Configuration object
   */
  getConfig() {
    return {
      isSandbox: ZARINPAL_CONFIG.isSandbox,
      callbackUrl: ZARINPAL_CONFIG.callbackUrl,
      endpoints: this.endpoints,
    };
  }
}

// Export a singleton instance
export const zarinPalService = new ZarinPalService();
