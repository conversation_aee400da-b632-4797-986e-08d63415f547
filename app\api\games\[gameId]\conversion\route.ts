import { type NextRequest, NextResponse } from "next/server";
import {
  recordGameConversion,
  getGameById,
  decrementPrizeQuantity,
} from "@/lib/models/game";
import { deductCredits } from "@/lib/models/user";
import { sendCongratulationSMS } from "@/lib/sms";

export async function POST(
  request: NextRequest,
  { params }: { params: { gameId: string } }
) {
  try {
    const gameId = params.gameId;
    console.log("API: Recording conversion for game ID:", gameId);

    // Parse the request body
    const data = await request.json();
    console.log("API: Conversion data:", data);

    if (!data.phoneNumber || !data.prize) {
      console.log("API: Missing required fields");
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the game to check the user ID and get game details
    const game = await getGameById(gameId);

    if (!game) {
      console.log("API: Game not found");
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    console.log("API: Game details:", {
      name: game.name,
      instagramHandle: game.instagramHandle,
      instagramUsername: game.instagramUsername,
      socialInfo: game.socialInfo,
    });

    // Extract Instagram username from the game object
    // Check different possible properties where Instagram username might be stored
    let instagramUsername = "ما"; // Default fallback
    if (game.instagramUsername) {
      instagramUsername = game.instagramUsername;
    } else if (game.instagramHandle) {
      instagramUsername = game.instagramHandle;
    } else if (game.socialInfo && game.socialInfo.instagramUsername) {
      instagramUsername = game.socialInfo.instagramUsername;
    } else if (game.socialInfo && game.socialInfo.instagramHandle) {
      instagramUsername = game.socialInfo.instagramHandle;
    }

    // Deduct a credit from the user's account
    try {
      await deductCredits(game.userId, 1);
      console.log("API: Deducted 1 credit from user:", game.userId);
    } catch (error) {
      console.error("API: Error deducting credits:", error);
      // Continue with the conversion even if credit deduction fails
    }

    // Record the conversion
    const success = await recordGameConversion(
      gameId,
      data.prize,
      data.phoneNumber
    );

    if (!success) {
      console.log("API: Failed to record conversion");
      return NextResponse.json(
        { error: "Failed to record conversion" },
        { status: 500 }
      );
    }

    // Decrement the prize quantity
    await decrementPrizeQuantity(gameId, data.prize);

    // Send congratulation SMS using the updated method with prize, game name, and Instagram handle
    try {
      // Pass empty string as message to use the template in sms.ts
      // Pass prize name, game name, and Instagram handle as separate parameters
      const smsResult = await sendCongratulationSMS(
        data.phoneNumber,
        "", // Empty message to use the template in sms.ts
        data.prize, // Prize name
        game.name || "بازی", // Game name
        instagramUsername // Instagram username extracted from game object
      );

      console.log("API: Congratulation SMS result:", smsResult);
    } catch (error) {
      console.error("API: Error sending congratulation SMS:", error);
      // Continue even if SMS sending fails
    }

    console.log("API: Conversion recorded successfully");
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("API Error recording conversion:", error);
    return NextResponse.json(
      { error: "Failed to record conversion" },
      { status: 500 }
    );
  }
}
