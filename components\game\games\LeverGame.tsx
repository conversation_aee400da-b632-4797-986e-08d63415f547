'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import confetti from 'canvas-confetti';
import { Star, Heart, Gift, Trophy, Diamond, Sparkles, Zap, DollarSign, HelpCircle } from 'lucide-react';

interface LeverGameProps {
  prizes: Array<{
    name: string;
    probability: number;
    isAvailable?: boolean;
    icon?: string;
  }>;
  primaryColor: string;
  secondaryColor: string;
  isPlaying: boolean;
  onComplete: (prize: string) => void;
}

export default function LeverGame({ prizes, primaryColor, secondaryColor, isPlaying, onComplete }: LeverGameProps) {
  const [leverPulled, setLeverPulled] = useState(false);
  const [reels, setReels] = useState<number[]>([0, 0, 0]);
  const [selectedPrize, setSelectedPrize] = useState<string | null>(null);
  const [selectedIcon, setSelectedIcon] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);
  const spinIntervalsRef = useRef<NodeJS.Timeout[]>([]);
  const gameInitializedRef = useRef(false);
  const isSpinningRef = useRef(false);

  const iconComponents = {
    Star: Star,
    Heart: Heart,
    Gift: Gift,
    Trophy: Trophy,
    Diamond: Diamond,
    Sparkles: Sparkles,
    Zap: Zap,
    DollarSign: DollarSign,
    QuestionMark: HelpCircle,
  };

  const availableIcons = prizes.map((prize) => prize.icon || 'QuestionMark');

  useEffect(() => {
    console.log('Available Icons:', availableIcons);
    console.log('Icon Components Keys:', Object.keys(iconComponents));
  }, [availableIcons]);

  useEffect(() => {
    if (!gameInitializedRef.current) {
      const questionMarkIndex =
        availableIcons.indexOf('QuestionMark') >= 0 ? availableIcons.indexOf('QuestionMark') : 0;
      setReels([questionMarkIndex, questionMarkIndex, questionMarkIndex]);
      gameInitializedRef.current = true;
      console.log('Initial Reels:', [questionMarkIndex, questionMarkIndex, questionMarkIndex]);
    }
  }, [availableIcons]);

  useEffect(() => {
    if (!isPlaying || !gameInitializedRef.current || leverPulled || isSpinningRef.current) {
      console.log('Spin skipped:', {
        isPlaying,
        gameInitialized: gameInitializedRef.current,
        leverPulled,
        isSpinning: isSpinningRef.current,
      });
      return;
    }

    console.log('Starting spin with isPlaying:', isPlaying);
    setLeverPulled(true);
    setShowResult(false);
    isSpinningRef.current = true;

    // انتخاب جایزه بر اساس احتمال
    const randomValue = Math.random() * 100;
    let cumulativeProbability = 0;
    let winningPrize = prizes[prizes.length - 1].name;
    let winningIcon = prizes[prizes.length - 1].icon || 'QuestionMark';

    for (let i = 0; i < prizes.length; i++) {
      if (prizes[i].isAvailable === false) continue;
      cumulativeProbability += prizes[i].probability;
      if (randomValue <= cumulativeProbability) {
        winningPrize = prizes[i].name;
        winningIcon = prizes[i].icon || 'QuestionMark';
        break;
      }
    }

    console.log('Winning Prize:', winningPrize, 'Winning Icon:', winningIcon);

    // پاک کردن intervalهای قبلی
    spinIntervalsRef.current.forEach((interval) => clearInterval(interval));
    spinIntervalsRef.current = [];

    // زمان‌بندی چرخش و توقف
    const spinStartDelays = [0, 500, 1000]; // تأخیر شروع برای هر ریل
    const spinDurations = [2000, 2500, 3000]; // مدت زمان چرخش هر ریل
    const spinSpeed = 100;

    spinDurations.forEach((duration, index) => {
      let currentIndex = reels[index];

      // شروع چرخش با تأخیر
      setTimeout(() => {
        const spinInterval = setInterval(() => {
          setReels((prevReels) => {
            const newReels = [...prevReels];
            currentIndex = (currentIndex + 1) % availableIcons.length;
            newReels[index] = currentIndex;
            console.log(`Reel ${index} updated to icon index:`, currentIndex, 'Icon:', availableIcons[currentIndex]);
            return newReels;
          });
        }, spinSpeed);

        spinIntervalsRef.current.push(spinInterval);

        // توقف ریل
        setTimeout(() => {
          clearInterval(spinInterval);

          const iconIndex = availableIcons.indexOf(winningIcon);
          setReels((prevReels) => {
            const newReels = [...prevReels];
            newReels[index] = iconIndex;
            console.log(`Reel ${index} stopped at icon index:`, iconIndex, 'Icon:', winningIcon);
            return newReels;
          });

          // وقتی آخرین ریل متوقف شد
          if (index === spinDurations.length - 1) {
            setSelectedIcon(winningIcon);
            console.log('Final Reels:', [iconIndex, iconIndex, iconIndex], 'Selected Icon:', winningIcon);

            setTimeout(() => {
              setSelectedPrize(winningPrize);
              setShowResult(true);

              confetti({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 },
              });

              setTimeout(() => {
                onComplete(winningPrize);
                isSpinningRef.current = false;
              }, 2000);
            }, 1000);
          }
        }, duration);
      }, spinStartDelays[index]);
    });

    return () => {
      spinIntervalsRef.current.forEach((interval) => clearInterval(interval));
    };
  }, [isPlaying, leverPulled, availableIcons, prizes, onComplete]);

  useEffect(() => {
    if (!isPlaying && leverPulled && !isSpinningRef.current) {
      setLeverPulled(false);
      setSelectedPrize(null);
      setSelectedIcon(null);
      setShowResult(false);

      const questionMarkIndex =
        availableIcons.indexOf('QuestionMark') >= 0 ? availableIcons.indexOf('QuestionMark') : 0;
      setReels([questionMarkIndex, questionMarkIndex, questionMarkIndex]);
      console.log('Game Reset - Reels:', [questionMarkIndex, questionMarkIndex, questionMarkIndex]);
    }
  }, [isPlaying, availableIcons, leverPulled]);

  const ensuredAvailableIcons = [...new Set([...availableIcons, 'QuestionMark'])];
  useEffect(() => {
    console.log('Ensured Available Icons:', ensuredAvailableIcons);
  }, [ensuredAvailableIcons]);

  const renderReelIcon = (reelPosition: number, index: number) => {
    const iconName = ensuredAvailableIcons[reelPosition] || 'QuestionMark';
    const IconComponent = iconComponents[iconName as keyof typeof iconComponents] || HelpCircle;
    console.log(`Rendering Reel ${index} - Icon:`, iconName);
    return (
      <motion.div
        key={`${index}-${reelPosition}-${iconName}-${Date.now()}`}
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 20, opacity: 0 }}
        transition={{ duration: 0.1 }}
      >
        <IconComponent className="h-8 w-8" />
      </motion.div>
    );
  };

  return (
    <div className="relative flex flex-col items-center justify-center h-80">
      <motion.div
        className="relative w-64 h-48 rounded-lg overflow-hidden"
        style={{ backgroundColor: primaryColor }}
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <motion.div
          className="absolute top-4 left-0 right-0 flex justify-center"
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="flex gap-2 p-4 bg-white rounded-md shadow-inner">
            {reels.map((reelPosition, index) => (
              <motion.div
                key={index}
                className="w-12 h-16 bg-gray-100 rounded flex items-center justify-center overflow-hidden"
                style={{
                  borderColor: secondaryColor,
                  borderWidth: '1px',
                  borderStyle: 'solid',
                }}
                initial={{ rotateX: -30, opacity: 0 }}
                animate={{ rotateX: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
              >
                {renderReelIcon(reelPosition, index)}
              </motion.div>
            ))}
          </div>
        </motion.div>

        <motion.div
          className="absolute bottom-0 right-8 w-8 h-16 bg-gray-800 rounded-t-lg"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        />

        <motion.div
          className="absolute bottom-16 right-8 w-8 h-24 flex flex-col items-center"
          initial={{ rotateZ: 0 }}
          animate={
            leverPulled
              ? {
                  rotateZ: [0, 30, 0],
                  y: [0, 20, 0],
                }
              : {
                  rotateZ: [0, -3, 3, -2, 0],
                  y: [0, -2, 2, -1, 0],
                }
          }
          transition={
            leverPulled
              ? {
                  duration: 1.5,
                  times: [0, 0.5, 1],
                  ease: 'easeInOut',
                }
              : {
                  duration: 2,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: 'reverse',
                  ease: 'easeInOut',
                  repeatDelay: 1,
                }
          }
        >
          <div className="w-8 h-8 rounded-full shadow-md" style={{ backgroundColor: secondaryColor }}></div>
          <div className="w-4 h-24 shadow-sm" style={{ backgroundColor: secondaryColor }}></div>
        </motion.div>
      </motion.div>

      {showResult && selectedPrize && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg z-30"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.7, ease: 'easeOut' }}
        >
          <motion.div
            className="bg-white p-6 rounded-lg shadow-xl text-center"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: 'spring', damping: 12, delay: 0.2 }}
          >
            <h3 className="text-xl font-bold mb-2">
              {selectedPrize === 'No Prize' ? 'Better luck next time!' : 'Congratulations!'}
            </h3>
            <p className="text-lg" style={{ color: primaryColor }}>
              {selectedPrize === 'No Prize' ? 'Try again soon!' : `You won: ${selectedPrize}`}
            </p>
            {selectedIcon && (
              <div className="mt-2 flex justify-center">
                {(() => {
                  const IconComponent = iconComponents[selectedIcon as keyof typeof iconComponents] || HelpCircle;
                  return <IconComponent className="h-8 w-8" style={{ color: primaryColor }} />;
                })()}
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
