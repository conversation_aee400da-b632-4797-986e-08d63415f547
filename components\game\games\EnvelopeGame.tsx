'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface EnvelopeGameProps {
  prizes: Array<{
    name: string;
    probability: number;
    isAvailable?: boolean;
  }>;
  primaryColor: string;
  secondaryColor: string;
  isPlaying: boolean;
  onComplete: (prize: string) => void;
}

export default function EnvelopeGame({
  prizes,
  primaryColor,
  secondaryColor,
  isPlaying,
  onComplete,
}: EnvelopeGameProps) {
  const [selectedEnvelope, setSelectedEnvelope] = useState<number | null>(null);
  const [isRevealing, setIsRevealing] = useState(false);
  const [revealedPrize, setRevealedPrize] = useState<string | null>(null);
  const [envelopes, setEnvelopes] = useState<Array<{ id: number; isOpen: boolean; prize: string | null }>>([]);

  // Initialize envelopes
  useEffect(() => {
    // Create 3 envelopes regardless of prize count
    const newEnvelopes = Array(3)
      .fill(0)
      .map((_, index) => ({
        id: index,
        isOpen: false,
        prize: null,
      }));
    setEnvelopes(newEnvelopes);
    setSelectedEnvelope(null);
    setRevealedPrize(null);
  }, [prizes]);

  // Handle game start
  useEffect(() => {
    if (isPlaying) {
      // Reset game state
      setSelectedEnvelope(null);
      setRevealedPrize(null);
      setIsRevealing(false);

      // Reset envelopes
      setEnvelopes(envelopes.map((env) => ({ ...env, isOpen: false, prize: null })));
    }
  }, [isPlaying]);

  const handleEnvelopeClick = (envelopeId: number) => {
    if (isRevealing || selectedEnvelope !== null || !isPlaying) return;

    setSelectedEnvelope(envelopeId);
    setIsRevealing(true);

    // Filter prizes to only include those with probability > 0 for selection
    const selectablePrizes = prizes.filter((prize) => prize.probability > 0 && prize.isAvailable !== false);

    let selectedPrize: string;

    if (selectablePrizes.length === 0) {
      // If no selectable prizes, use a default message
      selectedPrize = 'No prizes available';
    } else {
      // Determine the winning prize based on probability
      const randomValue = Math.random() * 100;
      let cumulativeProbability = 0;
      let winningPrize = selectablePrizes[0];

      for (const prize of selectablePrizes) {
        cumulativeProbability += prize.probability;
        if (randomValue <= cumulativeProbability) {
          winningPrize = prize;
          break;
        }
      }

      selectedPrize = winningPrize.name;
    }

    // Update the selected envelope with the prize
    const updatedEnvelopes = envelopes.map((env) => {
      if (env.id === envelopeId) {
        return { ...env, isOpen: true, prize: selectedPrize };
      }
      return env;
    });
    setEnvelopes(updatedEnvelopes);

    // Reveal the prize after a short delay
    setTimeout(() => {
      setRevealedPrize(selectedPrize);
      setIsRevealing(false);
      onComplete(selectedPrize);
    }, 1000);
  };

  return (
    <div className="flex flex-col items-center justify-center p-4">
      <div className="flex justify-center gap-4 mb-8">
        {envelopes.map((envelope) => (
          <motion.div
            key={envelope.id}
            className="relative cursor-pointer"
            whileHover={{ scale: envelope.isOpen ? 1 : 1.05 }}
            onClick={() => handleEnvelopeClick(envelope.id)}
          >
            <motion.div
              className="w-24 h-32 rounded-md flex items-center justify-center"
              style={{
                backgroundColor: envelope.isOpen ? '#ffffff' : primaryColor,
                border: `2px solid ${secondaryColor}`,
              }}
              animate={{
                rotateY: envelope.isOpen ? 180 : 0,
                backgroundColor: envelope.isOpen ? '#ffffff' : primaryColor,
              }}
              transition={{ duration: 0.5 }}
            >
              {envelope.isOpen ? (
                <motion.div
                  className="absolute inset-0 flex items-center justify-center text-center p-2"
                  style={{ rotateY: 180, backfaceVisibility: 'hidden' }}
                >
                  <span className="text-sm font-bold">{envelope.prize}</span>
                </motion.div>
              ) : (
                <motion.div
                  className="absolute inset-0 flex items-center justify-center"
                  style={{ backfaceVisibility: 'hidden' }}
                >
                  <span className="text-2xl text-white">?</span>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        ))}
      </div>

      {revealedPrize && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 rounded-lg text-center"
          style={{ backgroundColor: secondaryColor }}
        >
          <h3 className="text-lg font-bold mb-1">You won:</h3>
          <p className="text-xl">{revealedPrize}</p>
        </motion.div>
      )}

      {/* Display all possible prizes (including unavailable ones) */}
      <div className="mt-6 grid grid-cols-2 gap-2 w-full max-w-md">
        {prizes.map((prize, index) => (
          <div
            key={index}
            className={`p-2 rounded-md text-center text-sm ${prize.isAvailable === false ? 'opacity-50' : ''}`}
            style={{
              backgroundColor: index % 2 === 0 ? primaryColor : secondaryColor,
              color: '#fff',
            }}
          >
            {prize.name}
          </div>
        ))}
      </div>
    </div>
  );
}
