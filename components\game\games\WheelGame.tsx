'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import confetti from 'canvas-confetti';

// تابع برای تیره یا روشن کردن رنگ
const lightenOrDarkenColor = (color: string, factor: number) => {
  let r: number = Number.parseInt(color.slice(1, 3), 16);
  let g: number = Number.parseInt(color.slice(3, 5), 16);
  let b: number = Number.parseInt(color.slice(5, 7), 16);

  r = Math.min(255, Math.max(0, r + factor));
  g = Math.min(255, Math.max(0, g + factor));
  b = Math.min(255, Math.max(0, b + factor));

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

interface WheelGameProps {
  prizes: Array<{ name: string; probability: number; isAvailable?: boolean }>;
  primaryColor: string;
  secondaryColor: string;
  isPlaying: boolean;
  onComplete: (prize: string) => void;
  isPreviewMode?: boolean;
}

export default function WheelGame({
  prizes,
  primaryColor,
  secondaryColor,
  isPlaying,
  onComplete,
  isPreviewMode = false,
}: WheelGameProps) {
  const [rotation, setRotation] = useState(0);
  const [selectedPrize, setSelectedPrize] = useState<string | null>(null);
  const [isSpinning, setIsSpinning] = useState(false);
  const wheelRef = useRef<HTMLDivElement>(null);

  const segmentAngle = 360 / prizes.length;
  const initialOffset = -segmentAngle;

  // تابع برای مدیریت رنگ‌ها
  const getColorForSegments = (prizes: Array<{ name: string }>, primaryColor: string, secondaryColor: string) => {
    const colors =
      prizes.length % 2 === 0
        ? [primaryColor, secondaryColor]
        : [primaryColor, secondaryColor, lightenOrDarkenColor(primaryColor, 40)];

    return prizes.map((_, index) => {
      const colorIndex = index % colors.length;
      return colors[colorIndex];
    });
  };

  // Preview mode animation
  useEffect(() => {
    if (isPreviewMode) {
      // Gentle continuous rotation for preview mode
      const interval = setInterval(() => {
        setRotation((prev) => (prev - 1) % 360);
      }, 50);

      return () => clearInterval(interval);
    }
  }, [isPreviewMode]);

  useEffect(() => {
    if (isPlaying && !selectedPrize && !isSpinning) {
      setIsSpinning(true);
      const randomValue = Math.random() * 100;
      let cumulativeProbability = 0;
      let winningPrize = prizes[prizes.length - 1].name;
      let winningIndex = prizes.length - 1;

      for (let i = 0; i < prizes.length; i++) {
        cumulativeProbability += prizes[i].probability;
        if (randomValue <= cumulativeProbability) {
          winningPrize = prizes[i].name;
          winningIndex = i;
          break;
        }
      }
      // console.log$$[^)]*$$;

      const startAngle = winningIndex * segmentAngle;
      const endAngle = (winningIndex + 1) * segmentAngle;
      const randomAngleInSegment = Math.random() * (endAngle - startAngle) + startAngle;
      const finalRotation = 1800 + randomAngleInSegment;

      setRotation(-finalRotation);

      setTimeout(() => {
        const normalizedRotation = ((finalRotation % 360) + 360) % 360;
        // تنظیم زاویه با در نظر گرفتن initialOffset و موقعیت پوینتر
        const adjustedAngle = (normalizedRotation - initialOffset + 360) % 360;
        // محاسبه‌ی ایندکس خونه‌ای که زیر پوینتر قرار داره
        const pointerIndex = Math.floor(adjustedAngle / segmentAngle);
        const pointerPrize = prizes[pointerIndex].name;

        setSelectedPrize(pointerPrize);

        if (!pointerPrize.includes('No Prize') && !pointerPrize.includes('Sorry') && !pointerPrize.includes('Thanks')) {
          confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
          });
        }

        setTimeout(() => {
          onComplete(pointerPrize);
          setIsSpinning(false);
        }, 2000);
      }, 5000);
    }
  }, [isPlaying, prizes, onComplete, segmentAngle, initialOffset, isSpinning]);

  const colors = getColorForSegments(prizes, primaryColor, secondaryColor);

  const segments = prizes.map((prize, index) => {
    const color = colors[index];
    const segmentSize = 360 / prizes.length;
    const startAngle = index * segmentSize;
    const endAngle = (index + 1) * segmentSize;

    return {
      prize: prize.name,
      startAngle,
      endAngle,
      color,
    };
  });

  // Determine size based on preview mode
  const wheelSize = isPreviewMode ? 'w-48 h-48' : 'w-64 h-64';
  const containerHeight = isPreviewMode ? 'h-48' : 'h-80';
  const centerSize = isPreviewMode ? 'w-8 h-8' : 'w-12 h-12';
  const innerCenterSize = isPreviewMode ? 'w-6 h-6' : 'w-8 h-8';
  const pointerSize = isPreviewMode ? 'w-4 h-4' : 'w-6 h-6';
  const textSize = isPreviewMode ? 'text-[8px]' : 'text-xs';
  const maxTextWidth = isPreviewMode ? 'max-w-[40px]' : 'max-w-[60px]';

  return (
    <div className={`relative flex flex-col items-center justify-center ${containerHeight}`}>
      <div className={`relative ${wheelSize}`}>
        <motion.div
          ref={wheelRef}
          className="absolute inset-0 rounded-full overflow-hidden"
          initial={{ rotate: initialOffset }}
          animate={{ rotate: rotation + initialOffset }}
          transition={
            isPreviewMode
              ? {
                  duration: 60,
                  ease: 'linear',
                  repeat: Number.POSITIVE_INFINITY,
                }
              : { duration: 5, ease: 'easeOut' }
          }
          style={{
            backgroundImage: `conic-gradient(
              ${segments
                .map((segment) => `${segment.color} ${segment.startAngle}deg ${segment.endAngle}deg`)
                .join(', ')}
            )`,
          }}
        >
          {segments.map((segment, index) => {
            const angle = (segment.startAngle + segment.endAngle) / 2;
            const radians = ((angle - 90) * Math.PI) / 180;
            const radius = isPreviewMode ? 96 : 128;
            const radiusFactor = 0.65;

            const x = radius + Math.cos(radians) * (radius * radiusFactor);
            const y = radius + Math.sin(radians) * (radius * radiusFactor);

            return (
              <div
                key={index}
                className={`absolute font-bold text-white ${textSize}`}
                style={{
                  left: `${x}px`,
                  top: `${y}px`,
                  transform: `translate(-50%, -50%) rotate(${angle}deg)`,
                  transformOrigin: 'center center',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.7)',
                  width: 'fit-content',
                  maxWidth: maxTextWidth,
                  padding: '2px 4px',
                  textAlign: 'center',
                  pointerEvents: 'none',
                }}
              >
                {segment.prize}
              </div>
            );
          })}
        </motion.div>

        <div
          className={`absolute inset-0 m-auto ${centerSize} rounded-full z-10 flex items-center justify-center`}
          style={{ backgroundColor: 'white' }}
        >
          <div className={`${innerCenterSize} rounded-full`} style={{ backgroundColor: primaryColor }}></div>
        </div>

        <div
          className={`absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${pointerSize} z-20`}
          style={{ color: primaryColor }}
        >
          ▼
        </div>
      </div>

      {selectedPrize && !isPreviewMode && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg z-30"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="bg-white p-6 rounded-lg shadow-xl text-center"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ type: 'spring', damping: 12 }}
          >
            <h3 className="text-xl font-bold mb-2">
              {selectedPrize.includes('No Prize') || selectedPrize.includes('Sorry') || selectedPrize.includes('Thanks')
                ? 'دفعه بعد شانست بیشتره!'
                : 'تبریک!'}
            </h3>
            <p className="text-lg" style={{ color: primaryColor }}>
              {selectedPrize.includes('No Prize') || selectedPrize.includes('Sorry') || selectedPrize.includes('Thanks')
                ? 'دوباره امتحان کن!'
                : `جایزه شما: ${selectedPrize}`}
            </p>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
