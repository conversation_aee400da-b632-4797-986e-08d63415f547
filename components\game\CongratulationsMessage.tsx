"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Check<PERSON>ircle,
  Home,
  RefreshCw,
  Loader2,
  AlertCircle,
  Bug,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface CongratulationsMessageProps {
  prize: string;
  phoneNumber: string;
  gameName?: string;
  gameId?: string;
}

export default function CongratulationsMessage({
  prize,
  phoneNumber,
  gameName = "our game",
  gameId,
}: CongratulationsMessageProps) {
  const [isSendingSMS, setIsSendingSMS] = useState(false);
  const [smsSent, setSmsSent] = useState(false);
  const [smsError, setSmsError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [showDelayedSuccess, setShowDelayedSuccess] = useState(false);
  const [bypassMode, setBypassMode] = useState(false);

  useEffect(() => {
    // Send SMS notification when component mounts
    const sendPrizeNotification = async () => {
      if (!phoneNumber || !prize || smsSent || !gameId) return;

      setIsSendingSMS(true);
      setSmsError(null);

      try {
        console.log(
          `Sending prize notification for ${prize} to ${phoneNumber}`
        );

        const response = await fetch(`/api/games/${gameId}/notify-winner`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            phoneNumber,
            prize,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          setSmsSent(true);
          // console.log\(.+\);;

          // Check if we're in bypass mode
          if (data.bypassMode) {
            setBypassMode(true);
          }
        } else {
          // Even if there's an error, the SMS might still be delivered
          // Set a timer to show a success message after a delay
          setSmsError(data.error || "Failed to send prize notification");
          console.error("Error sending prize notification:", data);

          // After 10 seconds, show a message suggesting the SMS might arrive with delay
          setTimeout(() => {
            setShowDelayedSuccess(true);
          }, 10000);
        }
      } catch (error) {
        setSmsError("Network error when sending notification");
        console.error("Error sending prize notification:", error);

        // After 10 seconds, show a message suggesting the SMS might arrive with delay
        setTimeout(() => {
          setShowDelayedSuccess(true);
        }, 10000);
      } finally {
        setIsSendingSMS(false);
      }
    };

    sendPrizeNotification();
  }, [phoneNumber, prize, smsSent, gameId, retryCount]);

  const handleRetry = () => {
    if (isSendingSMS) return;
    setSmsSent(false);
    setSmsError(null);
    setShowDelayedSuccess(false);
    setRetryCount((prev) => prev + 1);
  };

  return (
    <motion.div
      className="bg-white/80 backdrop-blur-sm p-5 sm:p-6 rounded-lg shadow-xl w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div className="flex flex-col items-center justify-center text-center">
        {bypassMode && (
          <div className="bg-amber-100 border border-amber-300 rounded-md p-2 mb-4 w-full flex items-center gap-2">
            <Bug className="h-4 w-4 text-amber-600 flex-shrink-0" />
            <p className="text-xs text-amber-800">
              Development Mode: SMS notifications are bypassed. Prize details
              are not actually sent.
            </p>
          </div>
        )}

        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", damping: 8, delay: 0.2 }}
          className="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-4"
        >
          <CheckCircle className="h-10 w-10 text-green-600" />
        </motion.div>

        <h2 className="text-2xl font-bold mb-2">
          {prize === "No Prize" ? "Thank You for Playing!" : "Congratulations!"}
        </h2>

        <p className="text-lg mb-4">
          {prize === "No Prize"
            ? "Better luck next time!"
            : `You've won: ${prize}`}
        </p>

        <div className="bg-gray-100 p-4 rounded-lg mb-6 w-full">
          <p className="text-sm text-gray-600">
            {prize === "No Prize"
              ? `We've sent a message to ${phoneNumber} with a special offer for playing.`
              : `We've sent your prize details to ${phoneNumber}. Check your messages to claim your reward!`}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 w-full">
          <Button
            variant="outline"
            className="flex-1 flex items-center justify-center gap-2"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4" />
            Play Again
          </Button>

          <Link href="/" className="flex-1">
            <Button className="w-full flex items-center justify-center gap-2">
              <Home className="h-4 w-4" />
              Return Home
            </Button>
          </Link>
        </div>

        {isSendingSMS && (
          <div className="flex items-center justify-center mt-4 text-gray-600">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <p>Sending prize details to your phone...</p>
          </div>
        )}

        {smsSent && (
          <p className="mt-4 text-green-600 text-center">
            Prize details have been sent to your phone!
          </p>
        )}

        {smsError && !showDelayedSuccess && (
          <div className="mt-4 text-center">
            <p className="text-amber-600 flex items-center justify-center gap-2">
              <AlertCircle className="h-4 w-4" />
              There was a delay sending your SMS. It may arrive shortly.
            </p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={handleRetry}
              disabled={isSendingSMS}
            >
              {isSendingSMS ? (
                <>
                  <Loader2 className="h-3 w-3 animate-spin mr-2" />
                  Retrying...
                </>
              ) : (
                "Retry Sending SMS"
              )}
            </Button>
          </div>
        )}

        {showDelayedSuccess && (
          <div className="mt-4 text-center">
            <p className="text-green-600">
              Your SMS may be delayed but should arrive shortly. If you don't
              receive it, please contact us on Instagram.
            </p>
          </div>
        )}
      </div>
    </motion.div>
  );
}
