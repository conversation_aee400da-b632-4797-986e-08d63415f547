import type { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface User {
    id: string;
    name: string;
    email?: string;
    mobileNumber?: string;
    provider?: string;
  }

  interface Session {
    user: {
      id: string;
      email?: string;
      mobileNumber?: string;
      provider?: string;
    } & DefaultSession['user'];
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    email?: string;
    mobileNumber?: string;
    provider?: string;
  }
}
