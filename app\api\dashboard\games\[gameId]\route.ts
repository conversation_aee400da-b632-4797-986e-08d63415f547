import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

interface RouteParams {
  params: {
    gameId: string;
  };
}

export async function GET(request: Request, context: RouteParams) {
  try {
    // Wait for params to be resolved
    const params = await context.params;

    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    const gameId = params.gameId;

    if (!ObjectId.isValid(gameId)) {
      return NextResponse.json({ error: 'Invalid game ID' }, { status: 400 });
    }

    const client = await clientPromise;
    const db = client.db();

    const game = await db.collection('games').findOne({
      _id: new ObjectId(gameId),
      userId: userId,
    });

    if (!game) {
      return NextResponse.json({ error: 'Game not found' }, { status: 404 });
    }

    const formattedGame = {
      id: game._id.toString(),
      name: game.name,
      type: game.type,
      plays: game.plays || 0,
      conversions: game.conversions || 0,
      status: game.status,
      createdAt: game.createdAt.toISOString(),
      lastActive: game.lastActive ? game.lastActive.toISOString() : game.createdAt.toISOString(),
      instagramHandle: game.instagramHandle,
      pageCategory: game.pageCategory,
      followerCount: game.followerCount,
      prizes: game.prizes,
      colorScheme: game.colorScheme,
      gameLink: game.gameLink,
      gameInteractions: game.gameInteractions || {
        plays: [],
        conversions: [],
        codeVerifications: [],
        phoneVerifications: [],
        sessions: [],
      },
      sessionMetrics: game.sessionMetrics || {
        totalSessions: 0,
        averageSessionTime: 0,
        completionRate: 0,
        lastCalculated: new Date().toISOString(),
      },
    };
    return NextResponse.json(formattedGame);
  } catch (error) {
    console.error('Error fetching game details:', error);
    return NextResponse.json({ error: 'Failed to fetch game details' }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    const { gameId } = params;

    if (!ObjectId.isValid(gameId)) {
      return NextResponse.json({ error: 'Invalid game ID' }, { status: 400 });
    }

    const updateData = await request.json();

    if (!updateData) {
      return NextResponse.json({ error: 'No update data provided' }, { status: 400 });
    }

    const client = await clientPromise;
    const db = client.db();

    const existingGame = await db.collection('games').findOne({
      _id: new ObjectId(gameId),
      userId: userId,
    });

    if (!existingGame) {
      return NextResponse.json({ error: 'Game not found' }, { status: 404 });
    }

    const updateObject: any = {
      updatedAt: new Date(),
    };

    if (updateData.name !== undefined) {
      updateObject.name = updateData.name;
    }

    if (updateData.colorScheme !== undefined) {
      updateObject.colorScheme = updateData.colorScheme;
    }

    if (updateData.prizes !== undefined) {
      const totalProbability = updateData.prizes.reduce((sum: number, prize: any) => sum + prize.probability, 0);
      if (Math.abs(totalProbability - 100) > 0.1) {
        return NextResponse.json({ error: 'Total prize probability must equal 100%' }, { status: 400 });
      }
      updateObject.prizes = updateData.prizes;
    }

    if (updateData.status !== undefined) {
      updateObject.status = updateData.status;
    }

    await db.collection('games').updateOne({ _id: new ObjectId(gameId) }, { $set: updateObject });

    const updatedGame = await db.collection('games').findOne({
      _id: new ObjectId(gameId),
    });

    const formattedGame = {
      id: updatedGame._id.toString(),
      name: updatedGame.name,
      type: updatedGame.type,
      plays: updatedGame.plays || 0,
      conversions: updatedGame.conversions || 0,
      status: updatedGame.status,
      createdAt: updatedGame.createdAt.toISOString(),
      lastActive: updatedGame.lastActive ? updatedGame.lastActive.toISOString() : updatedGame.createdAt.toISOString(),
      instagramHandle: updatedGame.instagramHandle,
      pageCategory: updatedGame.pageCategory,
      followerCount: updatedGame.followerCount,
      prizes: updatedGame.prizes,
      colorScheme: updatedGame.colorScheme,
      gameLink: updatedGame.gameLink,
      gameInteractions: updatedGame.gameInteractions || {
        plays: [],
        conversions: [],
        codeVerifications: [],
        phoneVerifications: [],
      },
    };

    return NextResponse.json(formattedGame);
  } catch (error) {
    console.error('Error updating game:', error);
    return NextResponse.json({ error: 'Failed to update game' }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: RouteParams) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    const { gameId } = params;

    // Validate gameId as a valid ObjectId
    if (!ObjectId.isValid(gameId)) {
      return NextResponse.json({ error: 'Invalid game ID' }, { status: 400 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db();

    // Check if the game exists and belongs to the user
    const existingGame = await db.collection('games').findOne({
      _id: new ObjectId(gameId),
      userId: userId,
    });

    if (!existingGame) {
      return NextResponse.json({ error: 'Game not found' }, { status: 404 });
    }

    // Delete the game
    const result = await db.collection('games').deleteOne({
      _id: new ObjectId(gameId),
    });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: 'Failed to delete game' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Game deleted successfully' });
  } catch (error) {
    console.error('Error deleting game:', error);
    return NextResponse.json({ error: 'Failed to delete game' }, { status: 500 });
  }
}
