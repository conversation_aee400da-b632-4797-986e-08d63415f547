"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Z<PERSON>, Gift, <PERSON>rk<PERSON>, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import WheelGame from "./games/WheelGame";
import LeverGame from "./games/LeverGame";
import EnvelopeGame from "./games/EnvelopeGame";

interface GameDisplayProps {
  gameData: {
    type: string;
    name: string;
    primaryColor?: string;
    secondaryColor?: string;
    colorScheme?: string;
    prizes: Array<{
      name?: string;
      type?: string;
      description?: string;
      probability: number;
      quantity?: number;
      remainingQuantity?: number;
      icon?: string;
    }>;
    id?: string;
  };
  onComplete: (prize: string) => void;
}

export default function GameDisplay({
  gameData,
  onComplete,
}: GameDisplayProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [availablePrizes, setAvailablePrizes] = useState(gameData.prizes);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available prizes on component mount
  useEffect(() => {
    const fetchAvailablePrizes = async () => {
      if (!gameData.id) return;

      try {
        setIsLoading(true);
        setError(null);
        const response = await fetch(`/api/games/${gameData.id}/award-prize`);

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.availablePrizes) {
            setAvailablePrizes(data.availablePrizes);
          }
        } else {
          const errorData = await response.json();
          setError(errorData.error || "Failed to fetch prizes");
        }
      } catch (error) {
        console.error("Error fetching available prizes:", error);
        setError("Failed to connect to the server");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAvailablePrizes();
  }, [gameData.id]);

  // Determine colors based on colorScheme or use provided colors
  const getPrimaryColor = () => {
    if (gameData.primaryColor) return gameData.primaryColor;

    switch (gameData.colorScheme) {
      case "purple":
        return "#8b5cf6";
      case "blue":
        return "#3b82f6";
      case "orange":
        return "#f97316";
      case "green":
        return "#84cc16";
      default:
        return "#8b5cf6"; // Default to purple
    }
  };

  const getSecondaryColor = () => {
    if (gameData.secondaryColor) return gameData.secondaryColor;

    switch (gameData.colorScheme) {
      case "purple":
        return "#c4b5fd";
      case "blue":
        return "#93c5fd";
      case "orange":
        return "#fdba74";
      case "green":
        return "#bef264";
      default:
        return "#c4b5fd"; // Default to purple
    }
  };

  // Format prizes to ensure compatibility
  // Include all prizes for visual display, but only those with probability > 0 can be won
  const formatPrizes = () => {
    // For visual display, include all prizes but mark unavailable ones
    const allPrizes = availablePrizes.map((prize) => ({
      name: prize.description || prize.name || "Prize",
      probability: prize.probability,
      originalDescription: prize.description || prize.name || "Prize",
      originalIcon: prize.icon,
      icon: prize.icon || "QuestionMark",
      isAvailable: prize.remainingQuantity > 0 && prize.probability > 0,
    }));

    // If no prizes are available to win, return a default "Sorry" prize
    const winnable = allPrizes.filter((prize) => prize.isAvailable);

    if (winnable.length === 0) {
      return [
        {
          name: "Sorry, all prizes have been claimed!",
          probability: 100,
          isAvailable: true,
          icon: "QuestionMark",
        },
      ];
    }

    // For prize selection logic, normalize probabilities of available prizes
    const totalProbability = winnable.reduce(
      (sum, prize) => sum + prize.probability,
      0
    );

    return allPrizes.map((prize) => {
      // If prize is available, calculate normalized probability
      if (prize.isAvailable) {
        return {
          ...prize,
          // Normalize probability for selection logic
          probability:
            totalProbability === 100
              ? prize.probability
              : (prize.probability / totalProbability) * 100,
        };
      }

      // If prize is not available, keep it visible but with zero probability
      return {
        ...prize,
        probability: 0, // Ensure it's never selected
      };
    });
  };

  // Handle awarding a prize
  const handleAwardPrize = async (prize: string) => {
    if (!gameData.id) {
      console.error("Game ID is missing");
      setError("Game ID is missing");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Find the original prize description
      const formattedPrizes = formatPrizes();
      const selectedPrize = formattedPrizes.find(
        (p) => p.name === prize && p.isAvailable
      );

      if (!selectedPrize) {
        console.error("Selected prize not found or not available");
        setError("Selected prize not available");
        return;
      }

      // Call API to decrement prize quantity and redistribute probabilities
      const response = await fetch(`/api/games/${gameData.id}/award-prize`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prizeDescription: selectedPrize.originalDescription,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.availablePrizes) {
          // Update available prizes with the new probabilities
          setAvailablePrizes(data.availablePrizes);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Failed to award prize");
      }
    } catch (error) {
      console.error("Error awarding prize:", error);
      setError("Failed to connect to the server");
    } finally {
      setIsLoading(false);
    }
  };

  // Determine which game component to render based on game type
  const renderGame = () => {
    const primaryColor = getPrimaryColor();
    const secondaryColor = getSecondaryColor();
    const formattedPrizes = formatPrizes();

    switch (gameData.type) {
      case "wheel":
        return (
          <WheelGame
            prizes={formattedPrizes}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
            isPlaying={isPlaying}
            onComplete={handleGameComplete}
          />
        );
      case "lever":
        return (
          <LeverGame
            prizes={formattedPrizes.map((prize) => ({
              ...prize,
              icon: prize.originalIcon || prize.icon || "QuestionMark",
            }))}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
            isPlaying={isPlaying}
            onComplete={handleGameComplete}
          />
        );
      case "envelope":
        return (
          <EnvelopeGame
            prizes={formattedPrizes}
            primaryColor={primaryColor}
            secondaryColor={secondaryColor}
            isPlaying={isPlaying}
            onComplete={handleGameComplete}
          />
        );
      default:
        return <div className="text-center p-8">Game type not supported</div>;
    }
  };

  const handleGameComplete = async (prize: string) => {
    setIsPlaying(false);

    // Award the prize in the database and update probabilities
    await handleAwardPrize(prize);

    // Call the parent component's onComplete handler
    onComplete(prize);
  };

  const getGameIcon = () => {
    switch (gameData.type) {
      case "wheel":
        return Compass;
      case "lever":
        return Zap;
      case "envelope":
        return Gift;
      default:
        return Sparkles;
    }
  };

  const GameIcon = getGameIcon();
  const primaryColor = getPrimaryColor();

  const containerVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1],
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Check if there are any prizes with remaining quantity and non-zero probability
  const hasPrizesAvailable = availablePrizes.some(
    (prize) => prize.remainingQuantity > 0 && prize.probability > 0
  );

  return (
    <motion.div
      className="bg-white/80 backdrop-blur-sm p-4 sm:p-6 rounded-lg shadow-xl w-full"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        className="flex items-center justify-center gap-2 mb-4"
        variants={itemVariants}
      >
        <GameIcon className="h-6 w-6" style={{ color: primaryColor }} />
        <h2 className="text-2xl font-bold text-center">{gameData.name}</h2>
      </motion.div>

      {error && (
        <motion.div
          className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4 flex items-center gap-2"
          variants={itemVariants}
        >
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </motion.div>
      )}

      <motion.div className="my-6" variants={itemVariants}>
        {renderGame()}
      </motion.div>

      {!isPlaying && (
        <motion.div className="flex justify-center" variants={itemVariants}>
          <Button
            onClick={() => setIsPlaying(true)}
            className="px-6 py-5 sm:px-8 sm:py-6 text-lg font-bold"
            style={{
              backgroundColor: primaryColor,
              color: "white",
            }}
            disabled={isPlaying || isLoading || !hasPrizesAvailable}
          >
            {isLoading
              ? "Loading..."
              : !hasPrizesAvailable
              ? "No Prizes Available"
              : gameData.type === "wheel"
              ? "Spin the Wheel!"
              : gameData.type === "lever"
              ? "Pull the Lever!"
              : "Pick an Envelope!"}
          </Button>
        </motion.div>
      )}

      {!hasPrizesAvailable && (
        <motion.div
          className="text-center mt-4 text-red-500"
          variants={itemVariants}
        >
          All prizes have been claimed!
        </motion.div>
      )}
    </motion.div>
  );
}
