"use client"

import { motion } from "framer-motion"

interface LeverGameBackgroundProps {
  primaryColor: string
  secondaryColor: string
}

export default function LeverGameBackground({ primaryColor, secondaryColor }: LeverGameBackgroundProps) {
  return (
    <div className="absolute inset-0 overflow-hidden opacity-30">
      <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent" />

      {/* Background gradient */}
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(135deg, ${primaryColor}, ${secondaryColor})`,
        }}
      />

      {/* Slot machine reels in background */}
      <div className="absolute inset-0 flex items-center justify-center">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="relative mx-4">
            {/* Reel container */}
            <motion.div
              className="w-20 h-[200vh] bg-white/20 rounded-full overflow-hidden"
              animate={{ y: ["0%", "-50%"] }}
              transition={{
                duration: 20 + i * 5,
                repeat: Number.POSITIVE_INFINITY,
                ease: "linear",
              }}
            >
              {/* Reel symbols */}
              {[...Array(20)].map((_, j) => (
                <div
                  key={j}
                  className="h-20 flex items-center justify-center text-4xl font-bold"
                  style={{ color: j % 2 === 0 ? primaryColor : secondaryColor }}
                >
                  {["7", "$", "★", "♥", "♦", "♠", "♣", "?"][j % 8]}
                </div>
              ))}
            </motion.div>
          </div>
        ))}
      </div>

      {/* Decorative levers */}
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute bottom-0"
          style={{
            left: `${25 + i * 25}%`,
            transformOrigin: "bottom center",
          }}
          animate={{ rotate: [0, 15, 0] }}
          transition={{
            duration: 4,
            delay: i * 2,
            repeat: Number.POSITIVE_INFINITY,
            repeatDelay: 10,
          }}
        >
          <div
            className="w-8 h-40"
            style={{
              background: `linear-gradient(to bottom, ${secondaryColor}, ${primaryColor})`,
              borderRadius: "8px 8px 0 0",
            }}
          />
          <div className="w-16 h-16 rounded-full -mt-8 mx-auto" style={{ backgroundColor: secondaryColor }} />
        </motion.div>
      ))}
    </div>
  )
}
