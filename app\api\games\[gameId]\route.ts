import { type NextRequest, NextResponse } from "next/server"
import { getGameById } from "@/lib/models/game"

export async function GET(request: NextRequest, { params }: { params: { gameId: string | Promise<string> } }) {
  try {
    // In Next.js 14, we need to await the params object itself first
    const resolvedParams = await params
    const gameId = resolvedParams.gameId

    console.log("API: Fetching game with ID:", gameId)

    // Get the game from the database
    const game = await getGameById(gameId)

    if (!game) {
      console.log("API: Game not found for ID:", gameId)
      return NextResponse.json({ error: "Game not found" }, { status: 404 })
    }

    console.log("API: Game found:", game.name)

    // Return the game data
    return NextResponse.json({ game })
  } catch (error) {
    console.error("API Error fetching game:", error)
    return NextResponse.json({ error: "Failed to fetch game" }, { status: 500 })
  }
}
