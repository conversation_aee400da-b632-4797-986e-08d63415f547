import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { getUserCredits } from "@/lib/models/user"

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the user ID from the session
    const userId = session.user.id

    // Get user credits
    const credits = await getUserCredits(userId)

    // Return the credits
    return NextResponse.json({ credits })
  } catch (error) {
    console.error("API Error fetching user credits:", error)
    return NextResponse.json({ error: "Failed to fetch user credits" }, { status: 500 })
  }
}
