"use client"

import { useState, useEffect } from "react"

export default function NotificationBadge() {
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    const fetchUnreadCount = async () => {
      try {
        const response = await fetch("/api/notifications")

        if (response.ok) {
          const data = await response.json()
          setUnreadCount(data.unreadCount)
        }
      } catch (error) {
        console.error("Error fetching notification count:", error)
      }
    }

    fetchUnreadCount()

    // Set up polling to check for new notifications
    const intervalId = setInterval(fetchUnreadCount, 60000) // Check every minute

    return () => clearInterval(intervalId)
  }, [])

  if (unreadCount === 0) {
    return null
  }

  return (
    <span className="flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
      {unreadCount > 99 ? "99+" : unreadCount}
    </span>
  )
}
