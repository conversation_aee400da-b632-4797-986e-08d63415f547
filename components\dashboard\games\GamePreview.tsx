"use client";

import type React from "react";

import { useState, useEffect } from "react";
import WheelGame from "@/components/game/games/WheelGame";
import {
  Star,
  Heart,
  Gift,
  Trophy,
  Diamond,
  Sparkles,
  Zap,
  DollarSign,
  HelpCircle,
} from "lucide-react";
import { motion } from "framer-motion";

interface GamePreviewProps {
  gameType: string;
  gameId: string;
  colorScheme?: string;
  prizes?: any[];
}

export default function GamePreview({
  gameType,
  gameId,
  colorScheme = "purple",
  prizes,
}: GamePreviewProps) {
  const [gameData, setGameData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Define color schemes
  const colorSchemes = {
    purple: { primary: "#8b5cf6", secondary: "#F9A8D4" },
    blue: { primary: "#3b82f6", secondary: "#93c5fd" },
    green: { primary: "#84cc16", secondary: "#bef264" },
    orange: { primary: "#f97316", secondary: "#fdba74" },
  };

  // Get the current color scheme
  const currentColorScheme =
    colorSchemes[colorScheme as keyof typeof colorSchemes] ||
    colorSchemes.purple;

  useEffect(() => {
    // If prizes are provided directly, use them
    if (prizes) {
      setGameData({
        prizes: prizes,
        colorScheme: colorScheme,
      });
      setIsLoading(false);
      return;
    }

    // Otherwise fetch game data
    const fetchGameData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/dashboard/games/${gameId}`);

        if (response.ok) {
          const data = await response.json();
          setGameData(data);
        } else {
          console.error("Failed to fetch game data");
        }
      } catch (error) {
        console.error("Error fetching game data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGameData();
  }, [gameId, prizes, colorScheme]);

  // Helper function to render icons
  const renderIcon = (iconName: string, color: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      Star: <Star className="h-8 w-8" style={{ color }} />,
      Heart: <Heart className="h-8 w-8" style={{ color }} />,
      Gift: <Gift className="h-8 w-8" style={{ color }} />,
      Trophy: <Trophy className="h-8 w-8" style={{ color }} />,
      Diamond: <Diamond className="h-8 w-8" style={{ color }} />,
      Sparkles: <Sparkles className="h-8 w-8" style={{ color }} />,
      Zap: <Zap className="h-8 w-8" style={{ color }} />,
      DollarSign: <DollarSign className="h-8 w-8" style={{ color }} />,
      QuestionMark: <HelpCircle className="h-8 w-8" style={{ color }} />,
    };

    return (
      iconMap[iconName] || <HelpCircle className="h-8 w-8" style={{ color }} />
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!gameData) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-sm text-gray-500">Game preview not available</p>
      </div>
    );
  }

  // Render different game types
  if (gameType === "wheel") {
    return (
      <div className="flex items-center justify-center h-full">
        <div style={{ transform: "scale(0.8)" }}>
          <WheelGame
            prizes={(prizes || gameData.prizes || []).map((prize: any) => ({
              name: prize.description || prize.type,
              probability: prize.probability,
              isAvailable: prize.remainingQuantity > 0,
            }))}
            primaryColor={currentColorScheme.primary}
            secondaryColor={currentColorScheme.secondary}
            isPlaying={false}
            onComplete={() => {}}
          />
        </div>
      </div>
    );
  }

  if (gameType === "lever") {
    return (
      <div className="flex items-center justify-center h-full">
        <div style={{ transform: "scale(0.8)" }}>
          <div className="relative flex flex-col items-center justify-center">
            <div
              className="relative w-64 h-48 rounded-lg overflow-hidden"
              style={{ backgroundColor: currentColorScheme.primary }}
            >
              <div className="absolute top-4 left-0 right-0 flex justify-center">
                <div className="flex gap-2 p-4 bg-white rounded-md shadow-inner">
                  {[0, 1, 2].map((index) => (
                    <div
                      key={index}
                      className="w-12 h-16 bg-gray-100 rounded flex items-center justify-center"
                      style={{
                        borderColor: currentColorScheme.secondary,
                        borderWidth: "1px",
                        borderStyle: "solid",
                      }}
                    >
                      {renderIcon("QuestionMark", currentColorScheme.primary)}
                    </div>
                  ))}
                </div>
              </div>
              <div className="absolute bottom-0 right-8 w-8 h-16 bg-gray-800 rounded-t-lg" />
              <motion.div
                className="absolute bottom-16 right-8 w-8 h-24 flex flex-col items-center"
                animate={{ rotateZ: [0, -3, 3, -2, 0], y: [0, -2, 2, -1, 0] }}
                transition={{
                  duration: 2,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: "reverse",
                  ease: "easeInOut",
                  repeatDelay: 1,
                }}
              >
                <div
                  className="w-8 h-8 rounded-full shadow-md"
                  style={{ backgroundColor: currentColorScheme.secondary }}
                ></div>
                <div
                  className="w-4 h-24 shadow-sm"
                  style={{ backgroundColor: currentColorScheme.secondary }}
                ></div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (gameType === "envelope") {
    return (
      <div className="flex items-center justify-center gap-4 h-full">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="w-20 h-24 rounded-md flex items-center justify-center"
            style={{
              backgroundColor:
                i % 2 === 0
                  ? currentColorScheme.primary
                  : currentColorScheme.secondary,
            }}
          >
            <span className="text-white font-bold text-xl">?</span>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center h-full">
      <p className="text-sm text-gray-500">Select a game type to see preview</p>
    </div>
  );
}
