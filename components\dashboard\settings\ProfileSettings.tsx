"use client"

import type React from "react"

import { useState } from "react"
import { Camera, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

export default function ProfileSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    firstName: "Shop",
    lastName: "Owner",
    displayName: "Shop Owner",
    email: "<EMAIL>",
    phone: "+1234567890",
    bio: "Instagram shop owner selling fashion and accessories.",
    website: "https://myshop.example.com",
    instagram: "@myshop",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      })
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-1" style={{ color: "var(--text-primary)" }}>
          Profile Information
        </h2>
        <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
          Update your personal information and how others see you on the platform
        </p>
      </div>

      {/* Profile Picture */}
      <div className="flex items-center gap-5">
        <div className="relative">
          <div
            className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden"
            style={{ borderColor: "var(--border)" }}
          >
            <span className="text-2xl font-bold" style={{ color: "var(--text-primary)" }}>
              {formData.firstName.charAt(0)}
              {formData.lastName.charAt(0)}
            </span>
          </div>
          <Button
            size="sm"
            className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0"
            style={{ backgroundColor: "var(--primary)" }}
          >
            <Camera className="h-4 w-4" />
            <span className="sr-only">Change profile picture</span>
          </Button>
        </div>
        <div>
          <h3 className="font-medium" style={{ color: "var(--text-primary)" }}>
            Profile Picture
          </h3>
          <p className="text-sm" style={{ color: "var(--text-secondary)" }}>
            JPG, GIF or PNG. 1MB max.
          </p>
          <div className="flex gap-2 mt-2">
            <Button size="sm" variant="outline">
              Upload
            </Button>
            <Button size="sm" variant="ghost">
              Remove
            </Button>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input id="firstName" name="firstName" value={formData.firstName} onChange={handleChange} required />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input id="lastName" name="lastName" value={formData.lastName} onChange={handleChange} required />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="displayName">Display Name</Label>
          <Input id="displayName" name="displayName" value={formData.displayName} onChange={handleChange} required />
          <p className="text-xs" style={{ color: "var(--text-secondary)" }}>
            This is how your name will appear on the platform
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input id="email" name="email" type="email" value={formData.email} onChange={handleChange} required />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input id="phone" name="phone" type="tel" value={formData.phone} onChange={handleChange} />
        </div>

        <div className="space-y-2">
          <Label htmlFor="bio">Bio</Label>
          <Textarea id="bio" name="bio" value={formData.bio} onChange={handleChange} rows={4} />
          <p className="text-xs" style={{ color: "var(--text-secondary)" }}>
            Brief description about yourself or your business
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input id="website" name="website" type="url" value={formData.website} onChange={handleChange} />
          </div>
          <div className="space-y-2">
            <Label htmlFor="instagram">Instagram Handle</Label>
            <Input id="instagram" name="instagram" value={formData.instagram} onChange={handleChange} />
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" type="button">
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading} style={{ backgroundColor: "var(--primary)" }}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
