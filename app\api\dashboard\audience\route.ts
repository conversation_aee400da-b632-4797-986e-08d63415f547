import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET(request: Request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      console.log("No authenticated user found for audience data")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id
    console.log("Fetching audience data for user ID:", userId)

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30days"
    console.log("Selected period:", period)

    // Connect to MongoDB
    const client = await clientPromise
    const db = client.db()

    // Fetch games for the user
    let games = []

    // Try different user ID formats
    if (ObjectId.isValid(userId)) {
      games = await db
        .collection("games")
        .find({ userId: new ObjectId(userId) })
        .toArray()
      console.log(`Found ${games.length} games with ObjectId userId`)
    }

    if (games.length === 0) {
      games = await db.collection("games").find({ userId: userId }).toArray()
      console.log(`Found ${games.length} games with string userId`)
    }

    if (games.length === 0 && ObjectId.isValid(userId)) {
      games = await db.collection("games").find({ userId: userId.toString() }).toArray()
      console.log(`Found ${games.length} games with string representation of ObjectId`)
    }

    if (games.length === 0) {
      console.log("No games found for user, returning empty data")
      return NextResponse.json({
        audienceStats: {
          totalPlayers: 0,
          avgPlayTime: "0m 0s",
          conversionRate: 0,
          repeatPlayers: 0,
        },
        participants: [],
        activeGames: [],
        inactiveGames: [],
        chartData: {
          labels: [],
          totalPlayers: [],
          newPlayers: [],
          returningPlayers: [],
        },
      })
    }

    // Log game interactions for debugging
    games.forEach((game) => {
      console.log(`Game ${game._id} plays:`, JSON.stringify(game.gameInteractions?.plays || [], null, 2))
    })

    // Determine date range
    const now = new Date()
    let startDate: Date

    if (period === "7days") {
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    } else if (period === "30days") {
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    } else {
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    }
    console.log("Date range:", startDate.toISOString(), "to", now.toISOString())

    // Generate daily labels (YYYY-MM-DD format for consistency)
    const labels: string[] = []
    const dateMap = new Map<string, number>() // Map YYYY-MM-DD to index
    const currentDate = new Date(startDate)
    let index = 0
    while (currentDate <= now) {
      const dateStr = currentDate.toISOString().split("T")[0] // YYYY-MM-DD
      labels.push(
        currentDate.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        }),
      )
      dateMap.set(dateStr, index)
      currentDate.setDate(currentDate.getDate() + 1)
      index++
    }

    // Initialize data arrays
    const totalPlayers = new Array(labels.length).fill(0)
    const newPlayers = new Array(labels.length).fill(0)
    const returningPlayers = new Array(labels.length).fill(0)

    // Track unique players and their first play dates
    const playerFirstPlay = new Map<string, Date>()
    const uniquePlayers = new Set<string>()

    // Process game interactions
    games.forEach((game) => {
      if (game.gameInteractions?.plays?.length) {
        // Sort plays by timestamp
        const plays = game.gameInteractions.plays
          .filter((play) => {
            const playDate = new Date(play.timestamp)
            const isValid = !isNaN(playDate.getTime()) && playDate >= startDate && playDate <= now
            if (!isValid) {
              console.warn("Invalid or out-of-range play:", play)
            }
            return isValid
          })
          .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

        plays.forEach((play) => {
          const playDate = new Date(play.timestamp)
          const phoneNumber = play.phoneNumber || `anonymous-${Math.random()}`
          const dateKey = playDate.toISOString().split("T")[0] // YYYY-MM-DD
          const labelIndex = dateMap.get(dateKey)

          if (labelIndex !== undefined) {
            console.log(`Processing play: phone=${phoneNumber}, date=${dateKey}, index=${labelIndex}`)
            uniquePlayers.add(phoneNumber)

            // Check if this is the player's first play
            if (!playerFirstPlay.has(phoneNumber)) {
              playerFirstPlay.set(phoneNumber, playDate)
              newPlayers[labelIndex]++
              console.log(`New player: ${phoneNumber} on ${dateKey}`)
            } else {
              returningPlayers[labelIndex]++
              console.log(`Returning player: ${phoneNumber} on ${dateKey}`)
            }

            // Update total players for the day (cumulative unique players up to this point)
            totalPlayers[labelIndex] = uniquePlayers.size
          } else {
            console.warn(`No matching label for play date: ${dateKey}, play:`, play)
          }
        })
      }
    })

    // Calculate audience stats
    let totalPlayTime = 0
    let totalPlays = 0
    let conversions = 0
    let repeatPlayerCount = 0

    const uniquePlayersMap = new Map()
    const participants: any[] = []

    games.forEach((game) => {
      const gameName = game.name || "Unnamed Game"
      const gameId = game._id.toString()
      const gameStatus = game.status || "inactive"
      const gameType = game.type || "wheel"

      const processInteractions = (interactions: any[], interactionType: string) => {
        if (interactions && Array.isArray(interactions)) {
          interactions.forEach((interaction) => {
            const phoneNumber = interaction.phoneNumber || "Anonymous"

            if (!uniquePlayersMap.has(phoneNumber)) {
              uniquePlayersMap.set(phoneNumber, {
                count: 1,
                playTime: interaction.duration || 0,
                converted: interactionType === "conversions",
              })

              participants.push({
                id: new ObjectId().toString(),
                name: "Anonymous Player",
                contact: phoneNumber,
                gameId: gameId,
                date: new Date(interaction.timestamp).toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                  year: "numeric",
                }),
                prize: interactionType === "conversions" ? interaction.prize || "No Prize" : "No Prize",
                converted: interactionType === "conversions",
              })
            } else {
              const playerData = uniquePlayersMap.get(phoneNumber)
              playerData.count += 1
              playerData.playTime += interaction.duration || 0
              if (interactionType === "conversions") {
                playerData.converted = true
              }
              uniquePlayersMap.set(phoneNumber, playerData)
            }

            if (interaction.duration) {
              totalPlayTime += interaction.duration
            }
          })
        }
      }

      if (game.gameInteractions?.plays) {
        totalPlays += game.gameInteractions.plays.length
        processInteractions(game.gameInteractions.plays, "plays")
      }

      if (game.gameInteractions?.conversions) {
        conversions += game.gameInteractions.conversions.length
        processInteractions(game.gameInteractions.conversions, "conversions")
      }

      if (game.gameInteractions?.codeVerifications) {
        processInteractions(game.gameInteractions.codeVerifications, "codeVerifications")
      }

      if (game.gameInteractions?.phoneVerifications) {
        processInteractions(game.gameInteractions.phoneVerifications, "phoneVerifications")
      }
    })

    const totalPlayersCount = uniquePlayersMap.size

    uniquePlayersMap.forEach((data) => {
      if (data.count > 1) {
        repeatPlayerCount++
      }
    })

    const repeatPlayerPercentage = totalPlayersCount > 0 ? Math.round((repeatPlayerCount / totalPlayersCount) * 100) : 0

    const conversionRate = totalPlays > 0 ? Number.parseFloat(((conversions / totalPlays) * 100).toFixed(1)) : 0

    const avgPlayTimeSeconds = totalPlays > 0 ? Math.round(totalPlayTime / totalPlays) : 0
    const avgPlayTimeMinutes = Math.floor(avgPlayTimeSeconds / 60)
    const avgPlayTimeRemainingSeconds = avgPlayTimeSeconds % 60
    const avgPlayTime = `${avgPlayTimeMinutes}m ${avgPlayTimeRemainingSeconds}s`

    const activeGames = games
      .filter((game) => game.status === "active" || game.status === "published")
      .map((game) => ({
        id: game._id.toString(),
        name: game.name || "Unnamed Game",
        type: game.type || "wheel",
        status: "active",
        plays: game.gameInteractions?.plays?.length || 0,
        participants: participants.filter((p) => p.gameId === game._id.toString()).length,
      }))

    const inactiveGames = games
      .filter((game) => game.status !== "active" && game.status !== "published")
      .map((game) => ({
        id: game._id.toString(),
        name: game.name || "Unnamed Game",
        type: game.type || "wheel",
        status: "inactive",
        plays: game.gameInteractions?.plays?.length || 0,
        participants: participants.filter((p) => p.gameId === game._id.toString()).length,
      }))

    const audienceData = {
      audienceStats: {
        totalPlayers: totalPlayersCount,
        avgPlayTime,
        conversionRate,
        repeatPlayers: repeatPlayerPercentage,
      },
      participants: participants.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 10),
      activeGames,
      inactiveGames,
      chartData: {
        labels,
        totalPlayers,
        newPlayers,
        returningPlayers,
      },
    }

    console.log("Returning audience data:", JSON.stringify(audienceData, null, 2))
    return NextResponse.json(audienceData)
  } catch (error) {
    console.error("Error fetching audience data:", error)
    return NextResponse.json({ error: "Failed to fetch audience data", details: error.message }, { status: 500 })
  }
}
