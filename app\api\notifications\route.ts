import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { getUserNotifications, createNotification, countUnreadNotifications } from "@/lib/models/notification"

// Get all notifications for the current user
export async function GET(request: Request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id

    // Get query parameters
    const url = new URL(request.url)
    const includeDeleted = url.searchParams.get("includeDeleted") === "true"

    // Get notifications
    const notifications = await getUserNotifications(userId, includeDeleted)

    // Get unread count
    const unreadCount = await countUnreadNotifications(userId)

    return NextResponse.json({
      notifications,
      unreadCount,
    })
  } catch (error) {
    console.error("Error fetching notifications:", error)
    return NextResponse.json({ error: "Failed to fetch notifications", details: error.message }, { status: 500 })
  }
}

// Create a new notification
export async function POST(request: Request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user || !session.user.isAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { userId, type, title, message, link, metadata } = body

    if (!userId || !type || !title || !message) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Create notification
    const notification = await createNotification({
      userId,
      type,
      title,
      message,
      link,
      metadata,
    })

    return NextResponse.json(notification, { status: 201 })
  } catch (error) {
    console.error("Error creating notification:", error)
    return NextResponse.json({ error: "Failed to create notification", details: error.message }, { status: 500 })
  }
}
