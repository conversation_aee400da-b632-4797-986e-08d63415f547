import { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'بوستاگرام - پلتفرم گیمیفیکیشن اینستاگرام',
    short_name: 'بوستاگرام',
    description: 'پلتفرم بوستاگرام برای ایجاد بازی‌های جذاب و تعاملی برای فالوورهای اینستاگرام شما',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#6366f1',
    orientation: 'portrait-primary',
    scope: '/',
    lang: 'fa',
    dir: 'rtl',
    icons: [
      {
        src: '/web-app-manifest-192x192.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/web-app-manifest-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      },
      {
        src: '/favicon.ico',
        sizes: '32x32',
        type: 'image/x-icon',
      },
    ],
    categories: ['business', 'social', 'games'],
  };
}
