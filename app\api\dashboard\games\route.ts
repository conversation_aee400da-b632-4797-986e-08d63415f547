import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET(request: Request) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      console.log('No authenticated user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    console.log('Fetching games for user ID:', userId);

    // Parse query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const type = url.searchParams.get('type');
    const sortBy = url.searchParams.get('sortBy') || 'newest';
    const dateRange = url.searchParams.get('dateRange');

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db();

    // Build query - try different user ID formats
    let games = [];

    // First try with ObjectId
    try {
      const objectIdQuery = { userId: new ObjectId(userId) };
      console.log('Trying query with ObjectId:', objectIdQuery);
      games = await db.collection('games').find(objectIdQuery).toArray();
      console.log(`Found ${games.length} games with ObjectId query`);
    } catch (error) {
      console.log('Error with ObjectId query:', error.message);
    }

    // If no games found, try with string ID
    if (games.length === 0) {
      try {
        const stringIdQuery = { userId: userId };
        console.log('Trying query with string ID:', stringIdQuery);
        games = await db.collection('games').find(stringIdQuery).toArray();
        console.log(`Found ${games.length} games with string ID query`);
      } catch (error) {
        console.log('Error with string ID query:', error.message);
      }
    }

    // If still no games, try with string representation of ObjectId
    if (games.length === 0) {
      try {
        const stringObjectIdQuery = { userId: userId.toString() };
        console.log('Trying query with string representation of ObjectId:', stringObjectIdQuery);
        games = await db.collection('games').find(stringObjectIdQuery).toArray();
        console.log(`Found ${games.length} games with string representation of ObjectId query`);
      } catch (error) {
        console.log('Error with string representation of ObjectId query:', error.message);
      }
    }

    // Log all games in the database to help debug
    const allGames = await db.collection('games').find({}).toArray();
    console.log(`Total games in database: ${allGames.length}`);
    if (allGames.length > 0) {
      console.log('Sample game:', JSON.stringify(allGames[0], null, 2));
      console.log(
        'User IDs in database:',
        allGames.map((game) => game.userId)
      );
    }

    // Apply filters if games were found
    if (games.length > 0) {
      // Apply status filter
      if (status) {
        games = games.filter((game) => game.status === status);
      }

      // Apply type filter
      if (type) {
        games = games.filter((game) => game.type === type);
      }

      // Apply date range filter
      if (dateRange) {
        const now = new Date();
        let dateLimit;

        if (dateRange === '30days') {
          dateLimit = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        } else if (dateRange === '90days') {
          dateLimit = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        }

        if (dateLimit) {
          games = games.filter((game) => new Date(game.createdAt) >= dateLimit);
        }
      }

      // Apply sorting
      if (sortBy === 'newest') {
        games.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      } else if (sortBy === 'oldest') {
        games.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      } else if (sortBy === 'name-asc') {
        games.sort((a, b) => a.name.localeCompare(b.name));
      } else if (sortBy === 'name-desc') {
        games.sort((a, b) => b.name.localeCompare(a.name));
      } else if (sortBy === 'plays-high') {
        games.sort((a, b) => (b.plays || 0) - (a.plays || 0));
      } else if (sortBy === 'plays-low') {
        games.sort((a, b) => (a.plays || 0) - (b.plays || 0));
      } else if (sortBy === 'conversion-high') {
        games.sort((a, b) => {
          const rateA = a.plays ? a.conversions / a.plays : 0;
          const rateB = b.plays ? b.conversions / b.plays : 0;
          return rateB - rateA;
        });
      } else if (sortBy === 'conversion-low') {
        games.sort((a, b) => {
          const rateA = a.plays ? a.conversions / a.plays : 0;
          const rateB = b.plays ? b.conversions / b.plays : 0;
          return rateA - rateB;
        });
      }
    }

    // Format the games for the frontend
    const formattedGames = games.map((game) => ({
      id: game._id.toString(),
      name: game.name || 'Unnamed Game',
      type: game.type || 'unknown',
      plays: game.plays || 0,
      conversions: game.conversions || 0,
      status: game.status || 'inactive',
      createdAt: game.createdAt ? game.createdAt.toISOString() : new Date().toISOString(),
      lastActive: game.lastActive
        ? game.lastActive.toISOString()
        : game.createdAt
        ? game.createdAt.toISOString()
        : new Date().toISOString(),
      instagramHandle: game.instagramHandle || '',
      gameLink: game.gameLink || '',
      // Include session data for spend time calculations
      gameInteractions: game.gameInteractions || {
        plays: [],
        conversions: [],
        codeVerifications: [],
        phoneVerifications: [],
        sessions: [],
      },
      sessionMetrics: game.sessionMetrics || {
        totalSessions: 0,
        averageSessionTime: 0,
        completionRate: 0,
        lastCalculated: new Date().toISOString(),
      },
    }));

    console.log(`Returning ${formattedGames.length} formatted games`);
    return NextResponse.json(formattedGames);
  } catch (error) {
    console.error('Error fetching games:', error);
    return NextResponse.json({ error: 'Failed to fetch games', details: error.message }, { status: 500 });
  }
}
