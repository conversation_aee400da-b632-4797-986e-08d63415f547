"use client"

import { ChevronLeft } from "lucide-react"
import { motion } from "framer-motion"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"

interface AudienceFilterSidebarProps {
  isOpen: boolean
  onClose: () => void
}

export default function AudienceFilterSidebar({ isOpen, onClose }: AudienceFilterSidebarProps) {
  return (
    <motion.div
      className={`border-r w-72 overflow-y-auto flex-shrink-0 bg-white z-10 ${isOpen ? "block" : "hidden"}`}
      style={{ borderColor: "var(--border)" }}
      initial={{ x: -280 }}
      animate={{ x: isOpen ? 0 : -280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-lg" style={{ color: "var(--text-primary)" }}>
            Game Audience Filters
          </h3>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-6">
          {/* Game Type Filters */}
          <div>
            <h4 className="font-medium mb-2 text-sm" style={{ color: "var(--text-primary)" }}>
              Game Type
            </h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="game-all" defaultChecked />
                <Label htmlFor="game-all" className="text-sm">
                  All Game Types
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="game-wheel" />
                <Label htmlFor="game-wheel" className="text-sm">
                  Wheel Games
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="game-lever" />
                <Label htmlFor="game-lever" className="text-sm">
                  Lever Games
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="game-envelope" />
                <Label htmlFor="game-envelope" className="text-sm">
                  Envelope Games
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Player Demographics */}
          <div>
            <h4 className="font-medium mb-2 text-sm" style={{ color: "var(--text-primary)" }}>
              Player Demographics
            </h4>

            <div className="space-y-4">
              {/* Age Range */}
              <div>
                <Label className="text-xs mb-2 block">Age Range</Label>
                <div className="px-2">
                  <Slider defaultValue={[18, 65]} min={13} max={65} step={1} className="mb-1" />
                  <div className="flex justify-between text-xs" style={{ color: "var(--text-secondary)" }}>
                    <span>13</span>
                    <span>65+</span>
                  </div>
                </div>
              </div>

              {/* Gender */}
              <div>
                <Label className="text-xs mb-2 block">Gender</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="gender-all" defaultChecked />
                    <Label htmlFor="gender-all" className="text-sm">
                      All
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="gender-male" />
                    <Label htmlFor="gender-male" className="text-sm">
                      Male
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="gender-female" />
                    <Label htmlFor="gender-female" className="text-sm">
                      Female
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Game Engagement */}
          <div>
            <h4 className="font-medium mb-2 text-sm" style={{ color: "var(--text-primary)" }}>
              Game Engagement
            </h4>

            <div className="space-y-4">
              {/* Player Type */}
              <div>
                <Label className="text-xs mb-2 block">Player Type</Label>
                <RadioGroup defaultValue="all">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="all" id="player-all" />
                    <Label htmlFor="player-all" className="text-sm">
                      All Players
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="new" id="player-new" />
                    <Label htmlFor="player-new" className="text-sm">
                      New Players
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="returning" id="player-returning" />
                    <Label htmlFor="player-returning" className="text-sm">
                      Returning Players
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Completion Status */}
              <div>
                <Label className="text-xs mb-2 block">Completion Status</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="completion-all" defaultChecked />
                    <Label htmlFor="completion-all" className="text-sm">
                      All Players
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="completion-finished" />
                    <Label htmlFor="completion-finished" className="text-sm">
                      Completed Games
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="completion-abandoned" />
                    <Label htmlFor="completion-abandoned" className="text-sm">
                      Abandoned Games
                    </Label>
                  </div>
                </div>
              </div>

              {/* Conversion Status */}
              <div>
                <Label className="text-xs mb-2 block">Conversion Status</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="conversion-all" defaultChecked />
                    <Label htmlFor="conversion-all" className="text-sm">
                      All Players
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="conversion-yes" />
                    <Label htmlFor="conversion-yes" className="text-sm">
                      Converted
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="conversion-no" />
                    <Label htmlFor="conversion-no" className="text-sm">
                      Not Converted
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 space-y-2">
          <Button variant="outline" className="w-full">
            Reset Filters
          </Button>
          <Button
            className="w-full"
            style={{ backgroundColor: "var(--primary)", color: "var(--button-text)" }}
            onClick={onClose}
          >
            Apply Filters
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
