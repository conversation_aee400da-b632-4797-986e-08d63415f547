import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { amount } = body;

    // Validate amount
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json({ error: 'Invalid credit amount' }, { status: 400 });
    }

    // Instead of directly adding credits, redirect to payment gateway
    // This endpoint now initiates a payment request
    const paymentResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/payment/request`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('Cookie') || '', // Forward session cookie
      },
      body: JSON.stringify({
        type: 'credits',
        amount,
      }),
    });

    if (!paymentResponse.ok) {
      const errorData = await paymentResponse.json();
      return NextResponse.json(
        {
          error: errorData.error || 'Failed to create payment request',
        },
        { status: paymentResponse.status }
      );
    }

    const paymentData = await paymentResponse.json();

    // Return payment URL for redirect
    return NextResponse.json({
      success: true,
      paymentUrl: paymentData.paymentUrl,
      authority: paymentData.authority,
      message: 'Payment request created successfully. Redirecting to payment gateway.',
    });
  } catch (error) {
    console.error('API Error creating payment request:', error);
    return NextResponse.json({ error: 'Failed to create payment request' }, { status: 500 });
  }
}
