'use client';

import type React from 'react';
import { SessionProvider } from 'next-auth/react';
import { SidebarMobileProvider } from '@/hooks/use-sidebar-mobile';
import { AuthProvider } from '@/lib/auth-context';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <SessionProvider>
      <AuthProvider>
        <SidebarMobileProvider>{children}</SidebarMobileProvider>
      </AuthProvider>
    </SessionProvider>
  );
}
