import { useEffect, useRef, useCallback } from 'react';

interface SessionTrackingOptions {
  gameId: string;
  onSessionStart?: (sessionId: string) => void;
  onSessionEnd?: (sessionId: string, duration: number) => void;
  onError?: (error: Error) => void;
}

export function useSessionTracking({
  gameId,
  onSessionStart,
  onSessionEnd,
  onError,
}: SessionTrackingOptions) {
  const sessionIdRef = useRef<string | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const isActiveRef = useRef<boolean>(false);

  // Generate unique session ID
  const generateSessionId = useCallback(() => {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }, []);

  // Start session tracking
  const startSession = useCallback(async () => {
    if (isActiveRef.current || !gameId) return;

    try {
      const sessionId = generateSessionId();
      sessionIdRef.current = sessionId;
      startTimeRef.current = Date.now();
      isActiveRef.current = true;

      console.log('Starting session tracking for game:', gameId, 'sessionId:', sessionId);

      // Call API to start session
      const response = await fetch(`/api/games/${gameId}/session/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to start session: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Session started successfully:', data);

      onSessionStart?.(sessionId);
    } catch (error) {
      console.error('Error starting session:', error);
      onError?.(error as Error);
      // Reset state on error
      sessionIdRef.current = null;
      startTimeRef.current = null;
      isActiveRef.current = false;
    }
  }, [gameId, generateSessionId, onSessionStart, onError]);

  // End session tracking
  const endSession = useCallback(async (
    exitReason: 'completed' | 'abandoned' | 'error' = 'completed',
    phoneNumber?: string
  ) => {
    if (!isActiveRef.current || !sessionIdRef.current || !startTimeRef.current) return;

    try {
      const sessionId = sessionIdRef.current;
      const duration = Math.floor((Date.now() - startTimeRef.current) / 1000);

      console.log('Ending session:', sessionId, 'duration:', duration, 'reason:', exitReason);

      // Call API to end session
      const response = await fetch(`/api/games/${gameId}/session/end`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          sessionId, 
          exitReason,
          ...(phoneNumber && { phoneNumber })
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to end session: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Session ended successfully:', data);

      onSessionEnd?.(sessionId, duration);
    } catch (error) {
      console.error('Error ending session:', error);
      onError?.(error as Error);
    } finally {
      // Reset state
      sessionIdRef.current = null;
      startTimeRef.current = null;
      isActiveRef.current = false;
    }
  }, [gameId, onSessionEnd, onError]);

  // Handle page visibility changes
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden && isActiveRef.current) {
      // Page is hidden, end session as abandoned
      endSession('abandoned');
    }
  }, [endSession]);

  // Handle page unload
  const handleBeforeUnload = useCallback(() => {
    if (isActiveRef.current && sessionIdRef.current) {
      // Use sendBeacon for reliable tracking on page unload
      const sessionId = sessionIdRef.current;
      const duration = startTimeRef.current ? Math.floor((Date.now() - startTimeRef.current) / 1000) : 0;
      
      const data = JSON.stringify({
        sessionId,
        exitReason: 'abandoned',
      });

      // Try to send the data using sendBeacon (more reliable on page unload)
      if (navigator.sendBeacon) {
        navigator.sendBeacon(`/api/games/${gameId}/session/end`, data);
      } else {
        // Fallback to synchronous fetch
        fetch(`/api/games/${gameId}/session/end`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: data,
          keepalive: true,
        }).catch(console.error);
      }
    }
  }, [gameId]);

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [handleVisibilityChange, handleBeforeUnload]);

  // Auto-start session when gameId is available
  useEffect(() => {
    if (gameId && !isActiveRef.current) {
      startSession();
    }

    // Cleanup on unmount or gameId change
    return () => {
      if (isActiveRef.current) {
        endSession('abandoned');
      }
    };
  }, [gameId, startSession, endSession]);

  return {
    sessionId: sessionIdRef.current,
    isActive: isActiveRef.current,
    startSession,
    endSession,
    getDuration: () => {
      return startTimeRef.current ? Math.floor((Date.now() - startTimeRef.current) / 1000) : 0;
    },
  };
}
