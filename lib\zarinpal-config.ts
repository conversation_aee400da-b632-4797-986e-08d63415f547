/**
 * ZarinPal Payment Gateway Configuration
 * 
 * This file contains all ZarinPal-related configurations for the application.
 * It handles both sandbox and production environments.
 */

// ZarinPal API endpoints
export const ZARINPAL_ENDPOINTS = {
  // Production endpoints
  PRODUCTION: {
    REQUEST: 'https://api.zarinpal.com/pg/v4/payment/request.json',
    VERIFY: 'https://api.zarinpal.com/pg/v4/payment/verify.json',
    START_PAY: 'https://www.zarinpal.com/pg/StartPay/',
  },
  // Sandbox endpoints for testing
  SANDBOX: {
    REQUEST: 'https://sandbox.zarinpal.com/pg/v4/payment/request.json',
    VERIFY: 'https://sandbox.zarinpal.com/pg/v4/payment/verify.json',
    START_PAY: 'https://sandbox.zarinpal.com/pg/StartPay/',
  },
};

// ZarinPal configuration
export const ZARINPAL_CONFIG = {
  merchantId: process.env.ZARINPAL_MERCHANT_ID || '',
  callbackUrl: process.env.ZARINPAL_CALLBACK_URL || `${process.env.NEXT_PUBLIC_SITE_URL}/payment/callback`,
  isSandbox: process.env.ZARINPAL_SANDBOX === 'true',
};

// Get the appropriate endpoints based on environment
export function getZarinPalEndpoints() {
  return ZARINPAL_CONFIG.isSandbox ? ZARINPAL_ENDPOINTS.SANDBOX : ZARINPAL_ENDPOINTS.PRODUCTION;
}

// ZarinPal API response types
export interface ZarinPalRequestResponse {
  data: {
    code: number;
    message: string;
    authority: string;
    fee_type: string;
    fee: number;
  };
  errors: string[];
}

export interface ZarinPalVerifyResponse {
  data: {
    code: number;
    message: string;
    card_hash: string;
    card_pan: string;
    ref_id: number;
    fee_type: string;
    fee: number;
    shaparak_fee: string;
    order_id?: number;
  };
  errors: string[];
}

// ZarinPal request/verify payload types
export interface ZarinPalRequestPayload {
  merchant_id: string;
  amount: number;
  description: string;
  callback_url: string;
  metadata?: {
    mobile?: string;
    email?: string;
    order_id?: string;
  };
}

export interface ZarinPalVerifyPayload {
  merchant_id: string;
  amount: number;
  authority: string;
}

// Payment status codes
export const ZARINPAL_STATUS_CODES = {
  SUCCESS: 100,
  ALREADY_VERIFIED: 101,
  INVALID_MERCHANT: -9,
  INVALID_IP: -10,
  INVALID_MERCHANT_ID: -11,
  INVALID_REQUEST: -12,
  PAYMENT_NOT_FOUND: -21,
  INVALID_AMOUNT: -22,
  INVALID_AUTHORITY: -33,
  PAYMENT_FAILED: -54,
} as const;

// Helper function to get status message
export function getZarinPalStatusMessage(code: number): string {
  switch (code) {
    case ZARINPAL_STATUS_CODES.SUCCESS:
      return 'پرداخت با موفقیت انجام شد';
    case ZARINPAL_STATUS_CODES.ALREADY_VERIFIED:
      return 'پرداخت قبلاً تأیید شده است';
    case ZARINPAL_STATUS_CODES.INVALID_MERCHANT:
      return 'کد پذیرنده نامعتبر است';
    case ZARINPAL_STATUS_CODES.INVALID_IP:
      return 'آی‌پی نامعتبر است';
    case ZARINPAL_STATUS_CODES.INVALID_MERCHANT_ID:
      return 'شناسه پذیرنده نامعتبر است';
    case ZARINPAL_STATUS_CODES.INVALID_REQUEST:
      return 'درخواست نامعتبر است';
    case ZARINPAL_STATUS_CODES.PAYMENT_NOT_FOUND:
      return 'پرداخت یافت نشد';
    case ZARINPAL_STATUS_CODES.INVALID_AMOUNT:
      return 'مبلغ نامعتبر است';
    case ZARINPAL_STATUS_CODES.INVALID_AUTHORITY:
      return 'کد authority نامعتبر است';
    case ZARINPAL_STATUS_CODES.PAYMENT_FAILED:
      return 'پرداخت ناموفق بود';
    default:
      return 'خطای نامشخص در پرداخت';
  }
}

// Validate ZarinPal configuration
export function validateZarinPalConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!ZARINPAL_CONFIG.merchantId) {
    errors.push('ZARINPAL_MERCHANT_ID is required');
  }

  if (!ZARINPAL_CONFIG.callbackUrl) {
    errors.push('ZARINPAL_CALLBACK_URL is required');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
