import { formatDistanceToNow } from 'date-fns';
import { faIR } from 'date-fns/locale';
import { Award, Eye, MessageSquare, Phone } from 'lucide-react';

interface Activity {
  id: string;
  type: string;
  gameId: string;
  gameName: string;
  timestamp: string;
  phoneNumber: string;
  details: string;
}

interface ActivityFeedProps {
  activities: Activity[];
}

export default function ActivityFeed({ activities }: ActivityFeedProps) {
  // If no activities, show a placeholder
  if (!activities || activities.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-sm text-gray-500">فعالیت اخیری وجود ندارد</p>
      </div>
    );
  }

  // Helper function to get the appropriate icon for each activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'play':
        return <Eye className="h-4 w-4" />;
      case 'conversion':
        return <Award className="h-4 w-4" />;
      case 'phone_verification':
        return <Phone className="h-4 w-4" />;
      case 'code_verification':
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  // Helper function to get the appropriate color for each activity type
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'play':
        return 'bg-blue-100 text-blue-600';
      case 'conversion':
        return 'bg-green-100 text-green-600';
      case 'phone_verification':
        return 'bg-purple-100 text-purple-600';
      case 'code_verification':
        return 'bg-orange-100 text-orange-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start gap-3">
          <div className={`p-2 rounded-full ${getActivityColor(activity.type)}`}>{getActivityIcon(activity.type)}</div>
          <div className="flex-1 space-y-1">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                {activity.gameName}
              </p>
              <span className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                {formatDistanceToNow(new Date(activity.timestamp), {
                  addSuffix: true,
                  locale: faIR,
                })}
              </span>
            </div>
            <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
              {activity.details}
            </p>
            <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
              {activity.phoneNumber && activity.phoneNumber !== 'Anonymous' ? `تلفن: ${activity.phoneNumber}` : ''}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}
