'use client';

import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';
import NotificationList from '@/components/dashboard/notifications/NotificationList';
import NotificationPreferences from '@/components/dashboard/notifications/NotificationPreferences';

export default function MessagesPage() {
  const [activeTab, setActiveTab] = useState('notifications');

  return (
    <div className="jsx-b075ba1b0b8bfafc flex h-screen overflow-hidden" dir="rtl">
      <style jsx global>{`
        :root {
          /* Dashboard component variables */
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>
      {/* Sidebar */}
      <DashboardSidebar />

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Header */}
        <DashboardHeader />

        {/* Content */}
        <main className="p-4 md:p-6 space-y-6">
          <div>
            <h1 className="text-2xl font-bold mb-1">پیام‌ها و اعلان‌ها</h1>
            <p className="text-sm text-muted-foreground">اعلان‌ها و ترجیحات ارتباطی خود را مدیریت کنید</p>
          </div>

          <Tabs defaultValue="notifications" onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="notifications">اعلان‌ها</TabsTrigger>
              <TabsTrigger value="preferences">ترجیحات</TabsTrigger>
            </TabsList>

            <TabsContent value="notifications">
              <NotificationList />
            </TabsContent>

            <TabsContent value="preferences">
              <NotificationPreferences />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
}
