'use client';

import React from 'react';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Compass,
  Zap,
  Gift,
  Sparkles,
  Palette,
  Check,
  ChevronDown,
  ChevronUp,
  Lock,
  X,
  Info,
  ArrowLeft,
} from 'lucide-react';
import type { WizardFormData } from '../GameWizard';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface GameTypeStepProps {
  formData: WizardFormData;
  updateFormData: (data: Partial<WizardFormData>) => void;
}

// Define color schemes
const colorSchemes = [
  { id: 'purple', name: 'بنفش', primary: '#8b5cf6', secondary: '#c4b5fd' },
  { id: 'orange', name: 'نارنجی', primary: '#f97316', secondary: '#fdba74' },
  { id: 'green', name: 'سبز', primary: '#84cc16', secondary: '#bef264' },
  { id: 'blue', name: 'آب<PERSON>', primary: '#0ea5e9', secondary: '#7dd3fc' },
  { id: 'pink', name: 'صورتی', primary: '#ec4899', secondary: '#f9a8d4' },
];

const gameTypes = [
  {
    id: 'wheel',
    name: 'چرخ شانس',
    description: 'چرخ را بچرخانید و جوایز هیجان‌انگیز ببرید',
    icon: Compass,
    color: '#8b5cf6', // primary
    detailedDescription:
      "The Lucky Wheel lets your followers spin to win random prizes. It's perfect for giveaways and promotions that create excitement through the element of chance.",
    features: [
      'Customizable prize segments',
      'Adjustable win probabilities',
      'Animation speed control',
      'Winner celebration effects',
    ],
  },
  {
    id: 'lever',
    name: 'اهرم شانس',
    description: 'اهرم را بکشید تا جایزه‌تان را کشف کنید',
    icon: Zap,
    color: '#f97316', // orange
    detailedDescription:
      'The Chance Lever creates a slot-machine style experience where followers pull down to reveal their prize. Great for building anticipation and engagement.',
    features: ['Customizable symbols', 'Multi-reel options', 'Sound effects', 'Progressive jackpot feature'],
  },
  {
    id: 'envelope',
    name: 'پاکت‌های سورپرایز',
    description: 'پاکتی انتخاب کنید تا پاداش خود را کشف کنید',
    icon: Gift,
    color: '#84cc16', // secondary
    detailedDescription:
      'Surprise Envelopes let followers choose from multiple options to reveal hidden prizes. Perfect for creating a sense of mystery and personal choice.',
    features: ['Multiple envelope designs', 'Peek feature', 'Reveal animations', 'Multi-stage reveals'],
  },
  {
    id: 'coming-soon',
    name: 'بازی‌های بیشتر به زودی',
    description: 'ما مدام انواع بازی جدید اضافه می‌کنیم',
    icon: Sparkles,
    color: '#9ca3af', // gray
    detailedDescription:
      "We're working on exciting new game formats to help you engage your audience in fresh ways. Stay tuned for updates!",
    features: ['Treasure hunts', 'Quizzes', 'Puzzle games', 'Augmented reality experiences'],
    comingSoon: true,
  },
];

export default function GameTypeStep({ formData, updateFormData }: GameTypeStepProps) {
  const [selectedGameId, setSelectedGameId] = useState<string>(formData.gameType || '');
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState<boolean>(false);
  const [selectedColorScheme, setSelectedColorScheme] = useState<string>(formData.colorScheme || 'purple');
  const [isDesignOpen, setIsDesignOpen] = useState<boolean>(false);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('preview');
  const modalRef = useRef<HTMLDivElement>(null);

  // New state for mobile inline preview
  const [expandedGameId, setExpandedGameId] = useState<string | null>(null);
  const [mobilePreviewTab, setMobilePreviewTab] = useState<string>('preview');

  // Check if we're on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsPreviewModalOpen(false);
      }
    };

    if (isPreviewModalOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isPreviewModalOpen]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsPreviewModalOpen(false);
      }
    };

    if (isPreviewModalOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isPreviewModalOpen]);

  // Handle game selection
  const handleGameSelect = (gameId: string) => {
    const game = gameTypes.find((g) => g.id === gameId);
    if (game && !game.comingSoon) {
      setSelectedGameId(gameId);
      updateFormData({ gameType: gameId as any });
    }
  };

  // Handle preview button click
  const handlePreviewClick = (gameId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent card selection when clicking preview button

    const game = gameTypes.find((g) => g.id === gameId);
    if (game && !game.comingSoon) {
      setSelectedGameId(gameId);
      updateFormData({ gameType: gameId as any });

      if (isMobile) {
        // For mobile, toggle the expanded state for inline preview
        setExpandedGameId(expandedGameId === gameId ? null : gameId);
        setMobilePreviewTab('preview');
      } else {
        // For desktop, open the modal as before
        setIsPreviewModalOpen(true);
        setActiveTab('preview');
      }
    }
  };

  // Handle color scheme selection
  const handleColorSchemeSelect = (schemeId: string) => {
    setSelectedColorScheme(schemeId);
    updateFormData({ colorScheme: schemeId });
  };

  // Get the selected game
  const selectedGame = gameTypes.find((game) => game.id === selectedGameId);

  // Get the expanded game for mobile
  const expandedGame = gameTypes.find((game) => game.id === expandedGameId);

  // Get the selected color scheme
  const selectedScheme = colorSchemes.find((scheme) => scheme.id === selectedColorScheme) || colorSchemes[0];

  // Lock body scroll when modal is open
  useEffect(() => {
    if (isPreviewModalOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isPreviewModalOpen]);

  return (
    <div className="py-4">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
          چه نوع بازی می‌خواهید بسازید؟
        </h2>
        <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
          نوع بازی را انتخاب کنید که بیشترین مشارکت فالوورهایتان را داشته باشد
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        {gameTypes.map((game, index) => (
          <React.Fragment key={game.id}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              whileHover={!game.comingSoon ? { scale: 1.03, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' } : {}}
              className={`cursor-pointer rounded-lg overflow-hidden transition-all ${
                selectedGameId === game.id ? 'ring-2 ring-offset-2' : ''
              } ${game.comingSoon ? 'opacity-70' : ''}`}
              style={{
                backgroundColor: 'var(--card-bg)',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                ringColor: game.color,
              }}
              onClick={() => handleGameSelect(game.id)}
            >
              <div className="relative h-full flex flex-col">
                {/* Card header with gradient background */}
                <div
                  className="p-4 text-white"
                  style={{
                    background: game.comingSoon
                      ? `linear-gradient(135deg, ${game.color}dd, ${game.color}99)`
                      : `linear-gradient(135deg, ${game.color}, ${game.color}99)`,
                  }}
                >
                  <div className="flex justify-between items-start">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center bg-white/20 backdrop-blur-sm">
                      <game.icon className="h-6 w-6 text-white" />
                    </div>

                    {game.comingSoon && (
                      <div className="bg-white/20 backdrop-blur-sm rounded-full p-1">
                        <Lock className="h-4 w-4 text-white" />
                      </div>
                    )}

                    {selectedGameId === game.id && !game.comingSoon && (
                      <div className="bg-white rounded-full p-1">
                        <Check className="h-4 w-4" style={{ color: game.color }} />
                      </div>
                    )}
                  </div>

                  <h3 className="text-lg font-semibold mt-3 text-white">{game.name}</h3>
                </div>

                {/* Card body */}
                <div className="p-4 flex-grow flex flex-col justify-between">
                  <p className="text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                    {game.description}
                  </p>

                  {/* Game Preview Thumbnail */}
                  <div className="mt-auto h-16 w-full rounded-md overflow-hidden relative bg-gray-100/50">
                    {game.id === 'wheel' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div
                          className="w-12 h-12 rounded-full animate-spin-slow"
                          style={{
                            background: `conic-gradient(from 0deg, ${game.color}, #ec4899, #f97316, #84cc16, ${game.color})`,
                          }}
                        />
                      </div>
                    )}

                    {game.id === 'lever' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="h-full w-4 bg-gray-300 rounded-full relative">
                          <motion.div
                            className="absolute bottom-0 w-4 h-4 rounded-full"
                            style={{ backgroundColor: game.color }}
                            animate={{ y: [0, -6, 0] }}
                            transition={{ repeat: Number.POSITIVE_INFINITY, duration: 2 }}
                          />
                        </div>
                      </div>
                    )}

                    {game.id === 'envelope' && (
                      <div className="absolute inset-0 flex items-center justify-center gap-2">
                        {[0, 1, 2].map((i) => (
                          <motion.div
                            key={i}
                            className="w-8 h-10 rounded-md flex items-center justify-center"
                            style={{ backgroundColor: game.color }}
                            whileHover={{ scale: 1.1 }}
                            animate={{ rotate: [0, i % 2 === 0 ? 3 : -3, 0] }}
                            transition={{ repeat: Number.POSITIVE_INFINITY, duration: 2, delay: i * 0.3 }}
                          >
                            <Gift className="h-4 w-4 text-white" />
                          </motion.div>
                        ))}
                      </div>
                    )}

                    {game.id === 'coming-soon' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ repeat: Number.POSITIVE_INFINITY, duration: 2 }}
                        >
                          <Sparkles className="h-8 w-8 text-gray-400" />
                        </motion.div>
                      </div>
                    )}
                  </div>

                  {/* Preview button or Coming soon badge */}
                  <div className="mt-3 flex justify-center">
                    {game.comingSoon ? (
                      <div className="bg-gray-100 text-gray-500 text-xs font-medium py-1 px-2 rounded-full text-center">
                        به زودی
                      </div>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs flex items-center gap-1 h-7 px-2"
                        style={{ color: game.color }}
                        onClick={(e) => handlePreviewClick(game.id, e)}
                      >
                        <Info className="h-3 w-3" />
                        پیش‌نمایش
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Mobile Inline Preview - Only render when this game is expanded and we're on mobile */}
            {isMobile && expandedGameId === game.id && !game.comingSoon && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="col-span-1 sm:col-span-2 lg:col-span-4 bg-white rounded-lg shadow-lg overflow-hidden mb-4"
                style={{
                  borderColor: game.color,
                  borderWidth: '1px',
                  borderStyle: 'solid',
                }}
              >
                {/* Mobile Preview Header */}
                <div
                  className="p-3 flex justify-between items-center border-b"
                  style={{
                    borderColor: 'var(--border)',
                    background: `linear-gradient(135deg, ${game.color}20, ${game.color}10)`,
                  }}
                >
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1 h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        setExpandedGameId(null);
                      }}
                    >
                      <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <h3 className="text-base font-medium" style={{ color: 'var(--text-primary)' }}>
                      {game.name} Preview
                    </h3>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs h-8"
                    style={{
                      backgroundColor: game.color,
                      color: 'white',
                      borderColor: game.color,
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGameSelect(game.id);
                      setExpandedGameId(null);
                    }}
                  >
                    Select
                  </Button>
                </div>

                {/* Mobile Preview Tabs */}
                <div className="border-b" style={{ borderColor: 'var(--border)' }}>
                  <div className="flex">
                    <button
                      className={`flex-1 py-2 px-4 text-sm font-medium ${
                        mobilePreviewTab === 'preview' ? 'border-b-2' : ''
                      }`}
                      style={{
                        borderColor: mobilePreviewTab === 'preview' ? game.color : 'transparent',
                        color: mobilePreviewTab === 'preview' ? game.color : 'var(--text-secondary)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setMobilePreviewTab('preview');
                      }}
                    >
                      Preview
                    </button>
                    <button
                      className={`flex-1 py-2 px-4 text-sm font-medium ${
                        mobilePreviewTab === 'design' ? 'border-b-2' : ''
                      }`}
                      style={{
                        borderColor: mobilePreviewTab === 'design' ? game.color : 'transparent',
                        color: mobilePreviewTab === 'design' ? game.color : 'var(--text-secondary)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setMobilePreviewTab('design');
                      }}
                    >
                      Design
                    </button>
                    <button
                      className={`flex-1 py-2 px-4 text-sm font-medium ${
                        mobilePreviewTab === 'about' ? 'border-b-2' : ''
                      }`}
                      style={{
                        borderColor: mobilePreviewTab === 'about' ? game.color : 'transparent',
                        color: mobilePreviewTab === 'about' ? game.color : 'var(--text-secondary)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setMobilePreviewTab('about');
                      }}
                    >
                      About
                    </button>
                  </div>
                </div>

                {/* Mobile Preview Content */}
                <div className="p-4">
                  {/* Preview Tab */}
                  {mobilePreviewTab === 'preview' && (
                    <div className="flex items-center justify-center py-4">
                      <div className="bg-gray-100/50 rounded-lg p-4 flex items-center justify-center h-48 w-full max-w-xs overflow-hidden">
                        <div className="preview-container relative">{renderGamePreview(game.id, selectedScheme)}</div>
                      </div>
                    </div>
                  )}

                  {/* Design Tab */}
                  {mobilePreviewTab === 'design' && (
                    <div className="py-2">
                      <h4 className="text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
                        Color Scheme
                      </h4>
                      <div className="flex flex-wrap gap-3 justify-center mb-4">
                        {colorSchemes.map((scheme) => (
                          <button
                            key={scheme.id}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleColorSchemeSelect(scheme.id);
                            }}
                            className={`w-12 h-12 rounded-full relative ${
                              selectedColorScheme === scheme.id ? 'ring-2 ring-offset-2' : ''
                            }`}
                            style={{
                              background: `linear-gradient(135deg, ${scheme.primary}, ${scheme.secondary})`,
                              ringColor: scheme.primary,
                            }}
                            aria-label={`${scheme.name} color scheme`}
                          >
                            {selectedColorScheme === scheme.id && (
                              <Check className="h-5 w-5 text-white absolute inset-0 m-auto" />
                            )}
                          </button>
                        ))}
                      </div>

                      <h4 className="text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
                        Theme
                      </h4>
                      <Tabs defaultValue="standard">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="standard">Standard</TabsTrigger>
                          <TabsTrigger value="minimal">Minimal</TabsTrigger>
                          <TabsTrigger value="bold">Bold</TabsTrigger>
                        </TabsList>
                      </Tabs>
                    </div>
                  )}

                  {/* About Tab */}
                  {mobilePreviewTab === 'about' && (
                    <div className="py-2">
                      <p className="text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                        {game.detailedDescription}
                      </p>

                      <h4 className="text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Features:
                      </h4>
                      <ul className="space-y-2">
                        {game.features.map((feature, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <Check className="h-5 w-5 mt-0.5 flex-shrink-0" style={{ color: game.color }} />
                            <span style={{ color: 'var(--text-secondary)' }}>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </React.Fragment>
        ))}
      </motion.div>

      {/* Desktop Preview Modal - Only render on desktop */}
      <AnimatePresence>
        {!isMobile && isPreviewModalOpen && selectedGame && !selectedGame.comingSoon && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-0 sm:p-4 bg-black/50 backdrop-blur-sm"
            style={{ zIndex: 9999 }}
          >
            <motion.div
              ref={modalRef}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: 'spring', damping: 20 }}
              className="bg-white rounded-lg shadow-xl w-full h-full sm:h-auto sm:max-w-3xl sm:max-h-[90vh] overflow-hidden flex flex-col"
            >
              {/* Modal Header */}
              <div
                className="p-3 sm:p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10"
                style={{ borderColor: 'var(--border)' }}
              >
                <h3 className="text-base sm:text-lg font-semibold truncate" style={{ color: 'var(--text-primary)' }}>
                  {selectedGame.name} Preview
                </h3>
                <div className="flex items-center gap-2">
                  {/* Mobile-friendly close button with larger touch target */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsPreviewModalOpen(false)}
                    className="h-9 w-9 rounded-full"
                    aria-label="Close preview"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Design Options - Desktop */}
              <AnimatePresence>
                {isDesignOpen && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border-b p-4"
                    style={{ borderColor: 'var(--border)' }}
                  >
                    <h4 className="text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
                      Color Scheme
                    </h4>
                    <div className="flex flex-wrap gap-3">
                      {colorSchemes.map((scheme) => (
                        <button
                          key={scheme.id}
                          onClick={() => handleColorSchemeSelect(scheme.id)}
                          className={`w-10 h-10 rounded-full relative ${
                            selectedColorScheme === scheme.id ? 'ring-2 ring-offset-2' : ''
                          }`}
                          style={{
                            background: `linear-gradient(135deg, ${scheme.primary}, ${scheme.secondary})`,
                            ringColor: scheme.primary,
                          }}
                          aria-label={`${scheme.name} color scheme`}
                        >
                          {selectedColorScheme === scheme.id && (
                            <Check className="h-5 w-5 text-white absolute inset-0 m-auto" />
                          )}
                        </button>
                      ))}
                    </div>

                    <h4 className="text-sm font-medium mt-4 mb-3" style={{ color: 'var(--text-primary)' }}>
                      Theme
                    </h4>
                    <Tabs defaultValue="standard">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="standard">Standard</TabsTrigger>
                        <TabsTrigger value="minimal">Minimal</TabsTrigger>
                        <TabsTrigger value="bold">Bold</TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Modal Content Container - Scrollable */}
              <div className="overflow-y-auto flex-grow">
                <div className="p-4 md:p-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Preview Visualization - Fixed Container */}
                    <div className="bg-gray-100/50 rounded-lg p-4 flex items-center justify-center h-64 overflow-hidden">
                      <div className="preview-container relative">
                        {renderGamePreview(selectedGame.id, selectedScheme)}
                      </div>
                    </div>

                    {/* Game Description */}
                    <div>
                      <div className="flex justify-between items-center mb-3">
                        <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
                          About {selectedGame.name}
                        </h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsDesignOpen(!isDesignOpen)}
                          className="flex items-center gap-1"
                        >
                          <Palette className="h-4 w-4" />
                          <span>Design</span>
                          {isDesignOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                        </Button>
                      </div>
                      <p className="text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                        {selectedGame.detailedDescription}
                      </p>

                      <h4 className="text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Features:
                      </h4>
                      <ul className="space-y-1">
                        {selectedGame.features.map((feature, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <Check className="h-4 w-4 mt-0.5 flex-shrink-0" style={{ color: selectedScheme.primary }} />
                            <span style={{ color: 'var(--text-secondary)' }}>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Modal Footer - Sticky */}
              <div
                className="p-3 sm:p-4 border-t flex justify-end sticky bottom-0 bg-white z-10"
                style={{ borderColor: 'var(--border)' }}
              >
                <Button
                  onClick={() => setIsPreviewModalOpen(false)}
                  style={{
                    backgroundColor: selectedScheme.primary,
                    color: 'white',
                  }}
                  className="w-full sm:w-auto"
                >
                  Select This Game
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Add global styles for preview containers */}
      <style jsx global>{`
        .preview-container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: visible;
        }
      `}</style>
    </div>
  );
}

// Helper function to render game preview based on game type
function renderGamePreview(gameId: string, selectedScheme: { primary: string; secondary: string }) {
  if (gameId === 'wheel') {
    return (
      <div className="relative">
        <motion.div
          className="w-36 h-36 sm:w-40 sm:h-40 md:w-48 md:h-48 rounded-full"
          style={{
            background: `conic-gradient(
              from 0deg, 
              ${selectedScheme.primary} 0%, 
              ${selectedScheme.primary} 10%, 
              ${selectedScheme.secondary} 10%, 
              ${selectedScheme.secondary} 30%,
              ${selectedScheme.primary} 30%, 
              ${selectedScheme.primary} 50%,
              ${selectedScheme.secondary} 50%, 
              ${selectedScheme.secondary} 70%,
              ${selectedScheme.primary} 70%, 
              ${selectedScheme.primary} 90%,
              ${selectedScheme.secondary} 90%, 
              ${selectedScheme.secondary} 100%
            )`,
          }}
          animate={{ rotate: [0, 360] }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: 'linear',
          }}
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white shadow-lg flex items-center justify-center">
            <Compass className="h-5 w-5 sm:h-6 sm:w-6" style={{ color: selectedScheme.primary }} />
          </div>
        </div>
        <div
          className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-5 h-5 sm:w-6 sm:h-6"
          style={{ color: selectedScheme.primary }}
        >
          ▼
        </div>
      </div>
    );
  }

  if (gameId === 'lever') {
    return (
      <div className="relative flex items-center justify-center">
        <div className="bg-gray-200 rounded-lg p-3 sm:p-4 w-36 sm:w-40 md:w-48 h-24 sm:h-28 md:h-32 flex items-center justify-center relative">
          <div className="grid grid-cols-3 gap-1 sm:gap-2">
            {[1, 2, 3].map((i) => (
              <motion.div
                key={i}
                className="w-8 h-12 sm:w-10 sm:h-14 md:w-12 md:h-16 bg-white rounded border flex items-center justify-center"
                style={{ borderColor: 'var(--border)' }}
                animate={{
                  y: [0, -6, 0, -3, 0],
                }}
                transition={{
                  duration: 2,
                  delay: i * 0.2,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatDelay: 3,
                }}
              >
                <span style={{ color: selectedScheme.primary, fontWeight: 'bold' }}>
                  {i === 1 ? '7' : i === 2 ? '$' : '★'}
                </span>
              </motion.div>
            ))}
          </div>
          <motion.div
            className="absolute -bottom-8 sm:-bottom-10 md:-bottom-12 left-1/2 transform -translate-x-1/2 w-6 sm:w-8 h-16 sm:h-20 md:h-24"
            animate={{ y: [0, -6, 0] }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              repeatDelay: 3,
            }}
          >
            <div
              className="w-6 sm:w-8 h-6 sm:h-8 rounded-full"
              style={{ backgroundColor: selectedScheme.primary }}
            ></div>
            <div className="w-2 h-10 sm:h-12 md:h-16 mx-auto" style={{ backgroundColor: selectedScheme.primary }}></div>
          </motion.div>
        </div>
      </div>
    );
  }

  if (gameId === 'envelope') {
    return (
      <div className="flex items-center justify-center gap-2 sm:gap-3 md:gap-4">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-14 sm:w-16 md:w-20 h-20 sm:h-24 md:h-28 rounded-lg flex items-center justify-center relative overflow-hidden cursor-pointer"
            style={{ backgroundColor: i === 1 ? selectedScheme.primary : selectedScheme.secondary }}
            whileHover={{ scale: 1.1, rotate: 0 }}
            animate={{ rotate: [0, i % 2 === 0 ? 3 : -3, 0] }}
            transition={{
              rotate: { repeat: Number.POSITIVE_INFINITY, duration: 2, delay: i * 0.3 },
              scale: { duration: 0.2 },
            }}
          >
            <Gift className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 text-white" />
            <motion.div
              className="absolute inset-0 bg-white"
              initial={{ top: '100%' }}
              whileHover={{ top: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="h-full w-full flex items-center justify-center">
                <span className="font-bold text-xs sm:text-sm md:text-base" style={{ color: selectedScheme.primary }}>
                  {i === 0 ? '10%' : i === 1 ? 'WIN!' : 'Try'}
                </span>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>
    );
  }

  return null;
}
