'use client';

import { useState, useEffect } from 'react';
import { Bell, ChevronDown, Menu, Search, Settings, User, LogOut, CreditCard, Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { useSidebarMobile } from '@/hooks/use-sidebar-mobile';
import { useAuth } from '@/lib/auth-context';

export default function DashboardHeader() {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { toggleSidebar } = useSidebarMobile();
  const { user, signOut } = useAuth();
  const [credits, setCredits] = useState<number | null>(null);
  const router = useRouter();

  // Fetch user credits
  useEffect(() => {
    const fetchCredits = async () => {
      if (user?.id) {
        try {
          const response = await fetch(`/api/user/credits`);
          if (response.ok) {
            const data = await response.json();
            setCredits(data.credits);
          }
        } catch (error) {
          console.error('Error fetching credits:', error);
        }
      }
    };

    fetchCredits();

    // Listen for credits-updated event
    const handleCreditsUpdated = (event: CustomEvent) => {
      setCredits(event.detail.credits);
    };

    window.addEventListener('credits-updated', handleCreditsUpdated as EventListener);

    return () => {
      window.removeEventListener('credits-updated', handleCreditsUpdated as EventListener);
    };
  }, [user]);

  return (
    <header
      className="sticky top-0 z-20 border-b px-4 py-3 flex items-center justify-between bg-white"
      style={{ borderColor: 'var(--border)' }}
    >
      {/* Mobile Menu Button */}
      <Button variant="ghost" size="icon" className="md:hidden" onClick={toggleSidebar} aria-label="Toggle menu">
        <Menu className="h-5 w-5" />
      </Button>

      {/* Search Bar */}
      <div className="hidden md:flex items-center relative max-w-md w-full">
        <Search className="absolute right-3 h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
        <input
          type="text"
          placeholder="جستجو..."
          className="w-full pr-9 pl-4 py-2 text-sm rounded-md border"
          style={{
            borderColor: 'var(--border)',
            backgroundColor: 'var(--background)',
            color: 'var(--text-primary)',
          }}
        />
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center gap-2">
        {/* Credits Display */}
        <div className="hidden md:flex items-center gap-2">
          {credits !== null && (
            <div className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 rounded-full">
              <CreditCard className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                {credits} اعتبار
              </span>
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => router.push('/dashboard/credits')}
          >
            <Plus className="h-3 w-3" />
            افزودن اعتبار
          </Button>
        </div>

        {/* Notifications */}
        <div className="relative">
          <Button
            variant="ghost"
            size="icon"
            className="relative"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <Bell className="h-5 w-5" />
            <span
              className="absolute top-1 left-1 w-2 h-2 rounded-full"
              style={{ backgroundColor: 'var(--secondary)' }}
            ></span>
          </Button>

          <AnimatePresence>
            {showNotifications && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute left-0 mt-2 w-80 rounded-md shadow-lg overflow-hidden z-30"
                style={{ backgroundColor: 'var(--background)' }}
              >
                <div
                  className="p-3 border-b font-medium"
                  style={{
                    borderColor: 'var(--border)',
                    color: 'var(--text-primary)',
                  }}
                >
                  اعلان‌ها
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {[1, 2, 3].map((i) => (
                    <div
                      key={i}
                      className="p-3 border-b hover:bg-gray-50 cursor-pointer"
                      style={{ borderColor: 'var(--border)' }}
                    >
                      <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                        بازی جدید ثبت شد
                      </p>
                      <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                        کسی بازی "فروش تابستانی" شما را بازی کرد
                      </p>
                      <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                        {i * 5} دقیقه پیش
                      </p>
                    </div>
                  ))}
                </div>
                <div className="p-2 text-center">
                  <Button variant="ghost" size="sm" className="text-xs w-full" style={{ color: 'var(--primary)' }}>
                    مشاهده همه اعلان‌ها
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Settings */}
        <Button variant="ghost" size="icon">
          <Settings className="h-5 w-5" />
        </Button>

        {/* User Menu */}
        <div className="relative">
          <Button variant="ghost" className="flex items-center gap-2" onClick={() => setShowUserMenu(!showUserMenu)}>
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center"
              style={{ backgroundColor: 'var(--primary)' }}
            >
              <User className="h-4 w-4 text-white" />
            </div>
            <span className="hidden sm:inline text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
              {user?.name || 'صاحب فروشگاه'}
            </span>
            <ChevronDown className="h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
          </Button>

          <AnimatePresence>
            {showUserMenu && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute left-0 mt-2 w-48 rounded-md shadow-lg overflow-hidden z-30"
                style={{ backgroundColor: 'var(--background)' }}
              >
                <div className="py-1">
                  <Link href="/dashboard/settings" passHref>
                    <button
                      className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50"
                      style={{ color: 'var(--text-primary)' }}
                      onClick={() => setShowUserMenu(false)}
                    >
                      پروفایل شما
                    </button>
                  </Link>
                  <Link href="/dashboard/settings" passHref>
                    <button
                      className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50"
                      style={{ color: 'var(--text-primary)' }}
                      onClick={() => setShowUserMenu(false)}
                    >
                      تنظیمات حساب
                    </button>
                  </Link>
                  <Link href="/dashboard/settings" passHref>
                    <button
                      className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50"
                      style={{ color: 'var(--text-primary)' }}
                      onClick={() => setShowUserMenu(false)}
                    >
                      صورتحساب
                    </button>
                  </Link>
                  <div className="border-t my-1" style={{ borderColor: 'var(--border)' }}></div>
                  <button
                    className="w-full text-right px-4 py-2 text-sm hover:bg-gray-50 flex items-center gap-2 text-red-500"
                    onClick={() => {
                      setShowUserMenu(false);
                      signOut();
                    }}
                  >
                    <LogOut className="h-4 w-4" />
                    خروج
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </header>
  );
}
