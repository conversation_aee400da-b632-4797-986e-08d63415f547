import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET() {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session || !session.user) {
      console.log("No authenticated user found")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id
    console.log("Fetching stats for user ID:", userId)

    // Connect to MongoDB
    const client = await clientPromise
    const db = client.db()

    // Try different user ID formats to find games
    let games = []

    // First try with ObjectId
    try {
      const objectIdQuery = { userId: new ObjectId(userId) }
      console.log("Trying stats query with ObjectId:", objectIdQuery)
      games = await db.collection("games").find(objectIdQuery).toArray()
      console.log(`Found ${games.length} games with ObjectId query for stats`)
    } catch (error) {
      console.log("Error with ObjectId query for stats:", error.message)
    }

    // If no games found, try with string ID
    if (games.length === 0) {
      try {
        const stringIdQuery = { userId: userId }
        console.log("Trying stats query with string ID:", stringIdQuery)
        games = await db.collection("games").find(stringIdQuery).toArray()
        console.log(`Found ${games.length} games with string ID query for stats`)
      } catch (error) {
        console.log("Error with string ID query for stats:", error.message)
      }
    }

    // If still no games, try with string representation of ObjectId
    if (games.length === 0) {
      try {
        const stringObjectIdQuery = { userId: userId.toString() }
        console.log("Trying stats query with string representation of ObjectId:", stringObjectIdQuery)
        games = await db.collection("games").find(stringObjectIdQuery).toArray()
        console.log(`Found ${games.length} games with string representation of ObjectId query for stats`)
      } catch (error) {
        console.log("Error with string representation of ObjectId query for stats:", error.message)
      }
    }

    // If we still have no games, create default stats
    if (games.length === 0) {
      console.log("No games found for stats, returning default values")
      return NextResponse.json({
        totalPlays: 0,
        totalConversions: 0,
        conversionRate: 0,
        activeGames: 0,
        trends: {
          playsChange: 0,
          conversionsChange: 0,
          rateChange: 0,
          activeGamesChange: 0,
        },
      })
    }

    // Calculate total stats from actual data
    const totalPlays = games.reduce((sum, game) => sum + (game.plays || 0), 0)
    const totalConversions = games.reduce((sum, game) => sum + (game.conversions || 0), 0)
    const conversionRate = totalPlays > 0 ? Math.round((totalConversions / totalPlays) * 100) : 0
    const activeGames = games.filter((game) => game.status === "active").length

    console.log("Calculated stats:", {
      totalPlays,
      totalConversions,
      conversionRate,
      activeGames,
    })

    // For trends, we'll use simple placeholder values for now
    // In a real implementation, you would compare with previous periods
    const stats = {
      totalPlays,
      totalConversions,
      conversionRate,
      activeGames,
      trends: {
        playsChange: 5,
        conversionsChange: 10,
        rateChange: 2,
        activeGamesChange: 0,
      },
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error("Error fetching dashboard stats:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch dashboard stats",
        details: error.message,
      },
      { status: 500 },
    )
  }
}
