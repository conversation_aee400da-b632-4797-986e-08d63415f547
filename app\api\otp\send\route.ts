import { NextResponse, type NextRequest } from "next/server"
import { generateOTP, sendOTP } from "@/lib/sms"
import { storeOTP, getOTP, normalizePhoneNumber } from "@/lib/otp-store"

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json()

    // console.log$$[^)]*$$;

    if (!phoneNumber) {
      return NextResponse.json({ success: false, error: "Phone number is required" }, { status: 400 })
    }

    const normalizedPhone = normalizePhoneNumber(phoneNumber)
    // console.log$$[^)]*$$;

    // Rate limiting - check if an OTP was recently sent to this number
    const existingOTP = getOTP(normalizedPhone)
    const now = new Date()

    if (existingOTP) {
      const timeSinceLastOTP = now.getTime() - existingOTP.expiresAt.getTime() + 5 * 60 * 1000

      // If less than 1 minute has passed since the last OTP was sent
      if (timeSinceLastOTP < 60 * 1000) {
        const timeToWait = Math.ceil((60 * 1000 - timeSinceLastOTP) / 1000)
        return NextResponse.json(
          {
            success: false,
            error: `Please wait ${timeToWait} seconds before requesting a new code`,
          },
          { status: 429 },
        )
      }
    }

    // Generate a new OTP
    const otpCode = generateOTP()
    // console.log$$[^)]*$$;

    // Store OTP with 5-minute expiration
    storeOTP(normalizedPhone, otpCode, 5)

    // For testing purposes, always log the OTP
    // console.log$$[^)]*$$;

    // Send OTP via SMS
    const smsResult = await sendOTP(phoneNumber, otpCode)

    if (!smsResult.success) {
      console.error("SMS sending failed:", smsResult.message)

      // Store the OTP anyway so we can test with the code from the logs
      return NextResponse.json({
        success: true,
        message: "OTP generated (SMS delivery issue, check logs for code)",
        debug: smsResult.message,
        otpForTesting: process.env.NODE_ENV === "development" ? otpCode : undefined,
      })
    }

    return NextResponse.json({
      success: true,
      message: "OTP sent successfully",
      debug: process.env.NODE_ENV === "development" ? { otpCode } : undefined,
    })
  } catch (error) {
    console.error("Error in OTP send route:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Failed to send OTP",
        debug: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
