import { type NextRequest, NextResponse } from 'next/server';
import { endGameSession } from '@/lib/models/game';

export async function POST(request: NextRequest, { params }: { params: { gameId: string | Promise<string> } }) {
  try {
    console.log('API: Ending game session');

    // Get the gameId from params
    const resolvedParams = await params;
    const gameId = resolvedParams.gameId;

    // Parse request body
    const body = await request.json();
    const { sessionId, exitReason = 'completed', phoneNumber, manualDuration } = body;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Validate exit reason
    const validExitReasons = ['completed', 'abandoned', 'error'];
    if (!validExitReasons.includes(exitReason)) {
      return NextResponse.json({ error: 'Invalid exit reason' }, { status: 400 });
    }

    console.log('API: Ending session for gameId:', gameId, 'sessionId:', sessionId, 'reason:', exitReason);

    // End the session
    const success = await endGameSession(gameId, sessionId, exitReason, phoneNumber, manualDuration);

    if (!success) {
      return NextResponse.json({ error: 'Failed to end session' }, { status: 500 });
    }

    console.log('API: Session ended successfully');

    return NextResponse.json({
      success: true,
      message: 'Session ended successfully',
      sessionId,
      endTime: new Date().toISOString(),
      exitReason,
    });
  } catch (error) {
    console.error('API Error ending session:', error);
    return NextResponse.json(
      {
        error: 'Failed to end session',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
