"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { Sparkles, Phone, AlertCircle, Info } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { validateMobileNumber } from "@/utils/validation"

interface PhoneVerificationFormProps {
  onSubmit: (phoneNumber: string) => void
  gameName: string
}

export default function PhoneVerificationForm({ onSubmit, gameName }: PhoneVerificationFormProps) {
  const [phoneNumber, setPhoneNumber] = useState("")
  const [error, setError] = useState("")
  const [warning, setWarning] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!phoneNumber.trim()) {
      setError("Please enter your phone number")
      return
    }

    // Use the enhanced validation for Persian phone numbers
    if (!validateMobileNumber(phoneNumber)) {
      setError("Please enter a valid phone number")
      return
    }

    setError("")
    setWarning("")
    setIsSubmitting(true)

    try {
      // Format the phone number to ensure it's in the correct format for the API
      // For Persian numbers, ensure they start with +98
      let formattedNumber = phoneNumber.trim()

      // Remove all non-digit characters
      const digitsOnly = formattedNumber.replace(/\D/g, "")

      // If it's a Persian number starting with 0, convert to international format
      if (digitsOnly.startsWith("0") && digitsOnly.length === 11) {
        formattedNumber = `+98${digitsOnly.substring(1)}`
      }
      // If it doesn't have a country code, assume it's a Persian number
      else if (!formattedNumber.startsWith("+") && !formattedNumber.startsWith("00")) {
        formattedNumber = `+98${digitsOnly}`
      }

      // console.log$$[^)]*$$;

      // Send OTP via API
      const response = await fetch("/api/otp/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ phoneNumber: formattedNumber }),
      })

      const data = await response.json()
      // console.log$$[^)]*$$;

      if (!data.success) {
        setError(data.error || "Failed to send verification code")
        setIsSubmitting(false)
        return
      }

      // Check if there's a warning message (SMS delivery issue)
      if (data.debug) {
        setWarning("SMS delivery might be delayed. Please check your messages or try again later.")
        // console.log$$[^)]*$$;
      }

      // If successful, proceed to the next step
      onSubmit(formattedNumber)
    } catch (error) {
      console.error("Error sending OTP:", error)
      setError("Failed to send verification code. Please try again.")
      setIsSubmitting(false)
    }
  }

  // Handle phone number input with formatting
  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setPhoneNumber(value)
    if (error) setError("")
    if (warning) setWarning("")
  }

  return (
    <motion.div
      className="bg-white/80 backdrop-blur-sm p-5 sm:p-6 rounded-lg shadow-xl w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div className="flex items-center justify-center mb-6">
        <div className="bg-primary/10 p-3 rounded-full">
          <Sparkles className="h-6 w-6 text-primary" />
        </div>
      </div>

      <h2 className="text-2xl font-bold text-center mb-2">{gameName}</h2>
      <p className="text-center text-gray-600 mb-6">Enter your phone number to play and win exciting prizes!</p>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="phoneNumber">Phone Number</Label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="phoneNumber"
              type="tel"
              placeholder="09123456789 or +98 ************"
              className="pl-10"
              value={phoneNumber}
              onChange={handlePhoneNumberChange}
              dir="ltr"
            />
          </div>
          {error && (
            <div className="flex items-center text-red-500 text-sm mt-1">
              <AlertCircle className="h-4 w-4 mr-1" />
              <span>{error}</span>
            </div>
          )}
          {warning && (
            <div className="flex items-center text-amber-500 text-sm mt-1">
              <Info className="h-4 w-4 mr-1" />
              <span>{warning}</span>
            </div>
          )}
        </div>

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? "Sending..." : "Continue"}
        </Button>
      </form>

      <p className="text-xs text-center mt-6 text-gray-500">
        By continuing, you agree to receive SMS messages about your game and prize. Standard message rates may apply.
      </p>
    </motion.div>
  )
}
