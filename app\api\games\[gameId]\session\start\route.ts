import { type NextRequest, NextResponse } from 'next/server';
import { startGameSession } from '@/lib/models/game';

export async function POST(
  request: NextRequest,
  { params }: { params: { gameId: string | Promise<string> } }
) {
  try {
    console.log('API: Starting game session');

    // Get the gameId from params
    const resolvedParams = await params;
    const gameId = resolvedParams.gameId;

    // Parse request body
    const body = await request.json();
    const { sessionId } = body;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    console.log('API: Starting session for gameId:', gameId, 'sessionId:', sessionId);

    // Start the session
    const success = await startGameSession(gameId, sessionId);

    if (!success) {
      return NextResponse.json({ error: 'Failed to start session' }, { status: 500 });
    }

    console.log('API: Session started successfully');

    return NextResponse.json({
      success: true,
      message: 'Session started successfully',
      sessionId,
      startTime: new Date().toISOString(),
    });

  } catch (error) {
    console.error('API Error starting session:', error);
    return NextResponse.json(
      {
        error: 'Failed to start session',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
